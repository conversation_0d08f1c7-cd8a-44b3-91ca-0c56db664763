const {ConSoleCommonHandler} = require("../../../electron/console_handle/ConSoleCommonHandler");
const EE = require("../../../core/ee");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {FileUtils} = require("../utils/FileUtils");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");


class YSSHConSoleHandler extends ConSoleCommonHandler{



    async before(obj) {
        let {service} = EE.app;
        let shd =JSON.parse(obj.shd);
        let ssh =JSON.parse(obj.ssh);
        shd.path = this.path;
        if(ssh.UPCContext){
            UPCContext.load(ssh.UPCContext);
        }
        if(shd.UPCContext){
            UPCContext.load(shd.UPCContext);
        }
        //将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(ssh);
        PricingFileWriteUtils.writeToMemory(shd);
        service.systemService.loadProject(shd);
        service.systemService.loadProject(ssh);

        //将项目数据更新到ysf文件当中
        FileUtils.creatYshFile(ssh,shd,this.path);
        return shd;
    }

    /**
     *文件数据处理
     */
    async fileDataHandle(obj){
        return obj;
    }


    async after(win,obj) {
        //用户的打开历史记录列表数据处理
        ProjectFileUtils.writeUserHistoryListFile(obj);
    }


}

module.exports = {
    YSSHConSoleHandler
}
