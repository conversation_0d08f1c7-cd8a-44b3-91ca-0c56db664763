const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");


/**
 * 结算人材机分期计算
 */
class JieSuanRcjStageUtils  {


    /**
     * 将项目数据写入到内存当中
     * @param qd 清单数据
     */
    qdStageCalculate(qd) {
        let stageType = qd.stageType;
        //分期方式 1：按分期比例输入
        if (stageType == JieSuanConstantUtil.STAGE_RATIO){
            let stageQuantitiesList = qd.stageQuantitiesList;
            //按分期比例
            stageQuantitiesList.forEach(k =>{
                k.stageQuantity = NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide100(k.stageRatio),qd.quantity));
            });

            let index = null;
            for (let i = stageQuantitiesList.length - 1; i >= 0; i--) {
                if (stageQuantitiesList[i].stageQuantity !== 0) {
                    index = i;
                    break;
                }
            }
            if (ObjectUtil.isNotEmpty(index)){
                let sum = 0;
                stageQuantitiesList.slice(0, index).forEach(function(obj) {
                    sum += Number(obj.stageQuantity);
                });
                //比例不足时做100补齐处理
                stageQuantitiesList[index].stageQuantity = NumberUtil.subtract(qd.quantity,sum);
            }
        }
    }




}

module.exports = {
    JieSuanRcjStageUtils: new JieSuanRcjStageUtils()
};

