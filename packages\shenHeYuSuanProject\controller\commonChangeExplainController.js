const { Controller } = require('../../../core');

/**
 * 增减说明统一修改入口
 */
class CommonChangeExplainController extends Controller {


  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  async updateChangeExplain(args) {
    return await this.service.shenHeYuSuanProject.commonChangeExplainService.updateChangeExplain(args);
  }

}

CommonChangeExplainController.toString = () => '[class CommonChangeExplainController]';
module.exports = CommonChangeExplainController;

