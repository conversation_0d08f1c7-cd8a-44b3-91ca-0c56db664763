const YsshssConstant = require("../enum/YsshssConstant");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {Service} = require("../../../core");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {PricingFileFindUtils} = require('../../../electron/utils/PricingFileFindUtils');
const {ResponseData} = require('../../../electron/utils/ResponseData');
const _ = require("lodash");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {treeToArray} = require("../../../electron/main_editor/tree");

/**
 * 分部分项
 * @class
 */
class YsshFbfxService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 分部分项数据匹配
     * @param constructId   审定工程项目id
     * @param singleId 审定单项工程id
     * @param unitId 审定单位工程id
     * @param ssConstructId  送审工程项目id
     * @param ssSingleId   送审工程项目id
     * @param ssUnitId  送审工程项目id
     * @param addQdFbFlag  新增清单分部 标识  true新增  false不增加
     * @returns {Promise<void>}
     */
    async fbfxDataMatching(args) {
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag, type, addQdFbFlag,isDisplayAllData} = args;
        //进行数据比对 主要是为了标记数据审核状态
        let fbfxListResult = await this.fbfxListComparison(args);
        let fbfxList = ConvertUtil.deepCopy(fbfxListResult);
        let qingdanGLResult = await this.shGLQuery(args)
        let qingdanGl = qingdanGLResult ? qingdanGLResult.result || [] : [];
        let qingdanGlMap = new Map();
        let sdFbfxIndexId = new Set();
        //处理关联合并送审id  map
        // if (ObjectUtils.isEmpty(fbfxListResult) || (ObjectUtils.isNotEmpty(sequenceNbr) && !fbfxListResult.find(item=> item.sequenceNbr==sequenceNbr)) ) {
        if (ObjectUtils.isEmpty(fbfxListResult)) {
            console.log("请求参数有误.....")
            return {
                "data": [],
                "total": 0,
                "pageNum": pageNum,
                "pageSize": pageSize
            };
        }

        fbfxList.forEach((fbfxItem, index) => {
            if (fbfxItem.kind == BranchProjectLevelConstant.qd) {
                if (ObjectUtils.isEmpty(fbfxItem[YsshssConstant.ysshSysj])
                    || fbfxItem[YsshssConstant.ysshSysj][YsshssConstant.change] === YsshssConstant.insert) {
                    if (ObjectUtils.isNotEmpty(fbfxItem[YsshssConstant.qingdanGL])) {
                        sdFbfxIndexId.add(index);

                        qingdanGl.forEach(item => {
                            if (item.ysshQdglId === fbfxItem[YsshssConstant.qingdanGL].ysshQdglId) {
                                let t = qingdanGlMap.get(item.ysshQdglId);
                                if (ObjectUtils.isEmpty(t)) {
                                    t = new Array();
                                    qingdanGlMap.set(item.ysshQdglId, t);
                                }

                                t.push(fbfxItem);
                                delete fbfxItem[YsshssConstant.ysshSysj];
                            }
                        });
                    }
                }
            }

        });
        let newFbfxList = [];
        //匹配关联数据
        fbfxList.forEach((item, index) => {
            if (!sdFbfxIndexId.has(index)) {
                newFbfxList.push(item);
                if (ObjectUtils.isNotEmpty(item[YsshssConstant.ysshSysj])
                    && ObjectUtils.isNotEmpty(item[YsshssConstant.ysshSysj].sequenceNbr)) {
                    let t = qingdanGlMap.get(item[YsshssConstant.ysshSysj].sequenceNbr);
                    if (ObjectUtils.isNotEmpty(t)) {
                        //此处只有审定数据
                        item['qdglList'] = t;
                    }
                }
            }
        });
        //只有在 对比匹配页面  以及修改送审修改后进行新增清单分部数据
        // if(addQdFbFlag){
        //判断数据中是否存在审删数据  如果存在审删数据  需要给审定数据中添加一行“新增清单分部”
        newFbfxList = await this.newAddQdFenBu(newFbfxList, constructId, singleId, unitId);
        // }


        //处理清单关联操作

        // 因为清单的对比状态变更是在查询时才对比出来的  所以无法获取清单的对比状态变更事件  只能在查询的时候拿出所有的清单进行检查维护数据
        for (const item of newFbfxList) {
            if (item.kind == BranchProjectLevelConstant.qd) {
                args.qdId = item.sequenceNbr;
                args.ssQdId = item.ysshGlId ? item.ysshGlId : item.ysshSysj.sequenceNbr;
                args.change = item.ysshSysj ? item.ysshSysj.change : 0;
                await this.service.shenHeYuSuanProject.ysshQdAssociationService.checkAndUpdateQdAssociation(args);
            }
        }
        // 因为清单的关联项存在前后的关联关系，所以只能在前一次循环中处理好所有的数据，然后重新获取最新正确的关联数据
        for (const item of newFbfxList) {
            if (item.kind == BranchProjectLevelConstant.qd) {
                args.qdId = item.sequenceNbr;
                args.ssQdId = item.ysshGlId ? item.ysshGlId : item.ysshSysj.sequenceNbr;
                args.change = item.ysshSysj ? item.ysshSysj.change : 0;
                item.ysshQdglValue = await this.service.shenHeYuSuanProject.ysshQdAssociationService.getQdAssociationValue(args);
            }

        }

        this.countFbfxHJ(newFbfxList);
        return {
            "data": newFbfxList,
            "total": fbfxListResult.length,
            "pageNum": pageNum,
            "pageSize": pageSize
        };

    }


    countFbfxHJ(treeArray) {
        treeArray.forEach(item => {
            if (item.kind != BranchProjectLevelConstant.de && item.kind != BranchProjectLevelConstant.qd) {
                let total = this.getQdListCountSum(treeArray, item);
                //计算增减金额、比例  （审定金额-送审金额）/送审金额*100%
                let changeTotal=NumberUtil.numberScale2(item.total-total);
                let changeRatio=NumberUtil.multiply(NumberUtil.divide(changeTotal,total),100);
                item.ysshSysj = {
                    "total": total,
                    "changeTotal":NumberUtil.numberScale2(changeTotal),
                    "changeRatio":NumberUtil.numberScale2(changeRatio)
                }


            }


        });
    }

    getQdListCountSum(treeArray, node) {
        let total = 0;

        let qdList = [];

        function getqdList(qdList, node) {
            //获取子集数据
            let filter = treeArray.filter(i => i.parentId == node.sequenceNbr);
            if (ObjectUtils.isNotEmpty(filter)) {
                if (filter[0].kind == BranchProjectLevelConstant.fb || filter[0].kind == BranchProjectLevelConstant.zfb) {
                    filter.forEach(fb => {
                        getqdList(qdList, fb)
                    });
                } else {
                    if (filter[0].kind == BranchProjectLevelConstant.qd) {
                        qdList.push(...filter);
                    }
                }
            }
        }

        getqdList(qdList, node);
        if (ObjectUtils.isNotEmpty(qdList)) {
            qdList.forEach(qd => {
                if (qd.ysshSysj) {
                    total= NumberUtil.add(total,qd.ysshSysj.total);
                }
            })
        }
        return NumberUtil.numberScale2(total);
    }

    /**
     * 如果存在审删项，处理审删数据位置
     * @param newFbfxList
     * @param constructId
     * @param singleId
     * @param unitId
     */
    async newAddQdFenBu(newFbfxList, constructId, singleId, unitId) {
        //过滤出所有的审删清单
        let delQd = newFbfxList.filter(item => item.kind === BranchProjectLevelConstant.qd && item.ysshSysj.change === YsshssConstant.delete);
        let map = delQd.map(qd => qd.sequenceNbr);
        let arr = _.cloneDeep(newFbfxList);
        newFbfxList.forEach(de => {
            if (de.ysshSysj && de.kind == BranchProjectLevelConstant.de) {
                //审删定额
                if (de.ysshSysj.change == YsshssConstant.delete) {
                    // newFbfxList.forEach(qd => {
                    //     if (qd.ysshSysj && qd.kind == BranchProjectLevelConstant.qd) {
                    //         if (qd.ysshSysj.sequenceNbr == de.parentId) {
                    //             de.parentId = qd.sequenceNbr;
                    //         }
                    //     }
                    // })
                    for(const qd of arr){
                            if (qd.ysshSysj && qd.kind == BranchProjectLevelConstant.qd) {
                                if (qd.ysshSysj.sequenceNbr == de.parentId) {
                                    de.parentId = qd.sequenceNbr;
                                    break;
                                }
                            }
                    }
                }
            }
        });
        if (ObjectUtils.isEmpty(delQd)) {
            return newFbfxList;
        }
        //如果存在审删
        if (ObjectUtils.isNotEmpty(delQd)) {
            let allNodes = PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes();
            let line;
            let newArr = [];
            //判断审定数据中是否含有分部
            if (ObjectUtils.isNotEmpty(allNodes)) {
                //获取审删分部
                let find = allNodes.find(fb => fb.isDel);
                let newLine = {
                    kind: "01",
                    name: "新增清单分部"
                }
                let pointLine;
                //  如果没有分部数据  也没有其他数据   则需要新增一个分部
                if (allNodes.length == 1) {
                    pointLine = allNodes[0];
                    let newVar = await this.service.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, newLine, null);
                    line = newVar.data;
                    line.displaySign = 1;
                    newArr.push(line);
                } else {
                    //获取子集
                    let filter2 = allNodes.filter(item => item.parentId == allNodes[0].sequenceNbr);
                    // 如果有分部数据直接新建一个分部
                    if (filter2[0].kind === BranchProjectLevelConstant.fb) {
                        let filter1 = newFbfxList.filter(item => item.kind === BranchProjectLevelConstant.fb);
                        pointLine = filter1[filter1.length - 1]
                        let newVar = await this.service.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, newLine, null);
                        line = newVar.data;
                        line.displaySign = 1;
                        //获取所有审删的定额数据
                        newArr.push(line);
                    } else {
                        // //如果没有分部数据  但是有其他数据   则需要先新增一个分部 将原数据存储之后再新增一个分部
                        // pointLine = allNodes[0];
                        // let newFb = await this.service.itemBillProjectOptionService.insertLine(constructId, singleId, unitId, pointLine, {kind: "01"}, null);
                        // let data = newFb.data;
                        // pointLine = data;
                        line=allNodes[0];
                    }
                }
            }


            delQd.forEach(qd => {
                    qd.parentId = line.sequenceNbr;
                    newArr.push(qd);
                    let filter = newFbfxList.filter(item => (item.kind === BranchProjectLevelConstant.de && item.parentId === qd.sequenceNbr));
                    newArr.push(...filter);
                }
            );

            let newList = [];
            newFbfxList.forEach(de => {

                if (de.kind == BranchProjectLevelConstant.qd) {
                    if (de.ysshSysj) {
                        if (de.ysshSysj.change != YsshssConstant.delete) {
                            newList.push(de);
                        }
                    } else {
                        newList.push(de);
                    }
                } else {
                    newList.push(de);
                }
            });
            //处理审删中的数据标识
            if (ObjectUtils.isNotEmpty(newArr)) {
                newArr.forEach(del => {
                    if (del.ysshSysj) {
                        if (ObjectUtils.isEmpty(del.ysshSysj.total) || del.ysshSysj.total == 0) {
                            del.ysshSysj.change = YsshssConstant.noChange;
                            del["type"]=del.ysshSysj.type;
                            del.changeExplain="";
                        }
                    }

                })
            }
            newFbfxList = newList.filter(item => !map.includes(item.parentId));
            newFbfxList.push(...newArr);
        }


        return newFbfxList;
    }

    /**
     * 仅查询关联数据
     * @param {*} args
     * @returns
     */
    async shGLQuery(args) {
        const {constructId, singleId, unitId} = args;
        let sdFbfxList = PricingFileFindUtils.getQdByfbfx(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(sdFbfxList)) {
            return ResponseData.fail('未查询到审定数据！');
        }
        let result = Array();
        sdFbfxList.forEach(item => {
            //只有正常项可以新建关联，所以只查询正常项即可
            if (ObjectUtils.isNotEmpty(item.ysshQdgl) && ObjectUtils.isNotEmpty(item.ysshGlId)) {
                let sdData = {
                    sequenceNbr: item.sequenceNbr,
                    qdglId: item.ysshQdgl.qdglId,
                    qdglName: item.ysshQdgl.qdglName,
                    sortNo: item.ysshQdgl.sortNo,
                    ysshQdglId: item.ysshQdgl.ysshQdglId,
                }
                result.push(sdData)
            }
        });

        return ResponseData.success(result);
    }


    /**
     * 分部分项 对比
     * @param args 页面查询参数
     * @param
     * @returns {Promise<void>}
     */
    async fbfxListComparison(args) {
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag, type,isDisplayAllData} = args;
        //送审项目
        let ssProject = null;
        if (!ObjectUtils.isEmpty(ssUnitId)) {
            //审定数据没有的情况，送审数有，sequenceNbr 为送审默认数据，反之需要传null
            let ssSequenceNbr = null;
            if (ObjectUtils.isEmpty(unitId)) {
                ssSequenceNbr = sequenceNbr;
            }
            if (isDisplayAllData) {  //如果是展示不折叠的全量数据
                ssProject = this.service.itemBillProjectOptionService.getFbfxAllData(ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, pageNum, pageSize, isAllFlag).data;
            }else {
                ssProject = this.service.itemBillProjectOptionService.getFbFx(ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, pageNum, pageSize, isAllFlag).data;
                ssProject=this.filterZcSb(ssProject);
            }

        }

        //审定项目
        let sdProject = null;
        let sdUnit = null;//关联数据的时候用到
        if (!ObjectUtils.isEmpty(unitId)) {
            sdUnit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            if (isDisplayAllData) {
                sdProject = this.service.itemBillProjectOptionService.getFbfxAllData(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag).data;
            }else {
                sdProject = this.service.itemBillProjectOptionService.getFbFx(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag).data;
                sdProject=this.filterZcSb(sdProject);
            }

            /*sdProject =  PricingFileFindUtils.getFbFx(sdConstructId, sdSingleId, sdUnitId);*/

        }
        let promise = await this.fbfxListPropertyComparison(ssProject, sdProject, sdUnit);

        //获取 审定 对应单位工程的 重点项过滤设置
        let zdxgl = await this.service.shenHeYuSuanProject.ysshZdxglService.getUnitZdxglData(args);

        if (!ObjectUtils.isEmpty(zdxgl)){
            return await this.focusFiltering(constructId, singleId, unitId,promise);
        }

        return promise;
    }


    //过滤掉主材设备
    filterZcSb(arr){
        let filter = arr.filter(i=>i.kind==BranchProjectLevelConstant.top || i.kind==BranchProjectLevelConstant.fb || i.kind==BranchProjectLevelConstant.zfb ||
            i.kind==BranchProjectLevelConstant.qd ||i.kind==BranchProjectLevelConstant.de );
        return filter;
    }

    /**
     * 属性对比处理 完成审核所需展示字段
     * @param ssProject 送审某个单位工程 分部分项list
     * @param sdProject 审定某个单位工程 分部分项list
     * @returns {Promise<void>}
     */
    async fbfxListPropertyComparison(ssProject, sdProject, sdUnit) {
        //如果两部分数据都为空
        if (ObjectUtils.isEmpty(ssProject) && ObjectUtils.isEmpty(sdProject)) {
            return null;
        }
        //给前端返回的数据
        let resultList = [];

        //送审没有 审定有的情况(即为 全部审增)
        if (ObjectUtils.isEmpty(ssProject)) {
            for (let sdProjectElement of sdProject) {
                //对清单定额进行 送审数据处理
                if (sdProjectElement.kind === BranchProjectLevelConstant.qd || sdProjectElement.kind === BranchProjectLevelConstant.de) { //送审数据
                    //审增数据 对象处理
                    let promise = await this.fbfxSz(sdProjectElement);
                    resultList.push(promise);
                } else {
                    //这种对应的分部情况
                    resultList.push(sdProjectElement);
                }
            }
            return resultList;
        }

        //送审有 审定没有的情况(即为 全部审减)
        if (ObjectUtils.isEmpty(sdProject)) {
            for (let ssProjectElement of ssProject) {
                //对清单定额进行 送审数据处理
                if (ssProjectElement.kind === BranchProjectLevelConstant.qd || ssProjectElement.kind === BranchProjectLevelConstant.de) {
                    //审删数据 对象处理
                    let promise1 = await this.fbfxAllSs(ssProjectElement);
                    resultList.push(promise1);
                } else {
                    //这种对应的分部情况
                    resultList.push(ssProjectElement);
                }
            }
            return resultList;
        }

        //这个对应 单位工程中 送审和审定都有分部分项的逻辑
        //存在对应关系List 逻辑处理
        //审定关联 送审 的清单id set
        let set = new Set();
        for (let sdProjectElement of sdProject) {
            //分部 子分部部分 直接添加
            if (sdProjectElement.kind !== BranchProjectLevelConstant.qd && sdProjectElement.kind !== BranchProjectLevelConstant.de) {
                resultList.push(sdProjectElement);
            }

            //审定清单逻辑
            if (sdProjectElement.kind === BranchProjectLevelConstant.qd) {
                let ysshGlId = sdProjectElement.ysshGlId;

                //无关联清单
                if (ObjectUtils.isEmpty(ysshGlId)) {
                    //为空表示 审增项
                    let promise = await this.fbfxSz(sdProjectElement, sdUnit);
                    resultList.push(promise);
                    //审定对应定额
                    let sdDe = sdProject.filter(i => i.parentId == sdProjectElement.sequenceNbr);

                    if (!ObjectUtils.isEmpty(sdDe)) {
                        for (let i in sdDe) {
                            let sdDeXz = await this.fbfxSz(sdDe[i]);
                            resultList.push(sdDeXz);
                        }
                    }

                } else {
                    //有关联清单
                    set.add(ysshGlId);
                    let find = ssProject.find(i => i.sequenceNbr == ysshGlId);
                    let promise2;
                    if (ObjectUtils.isEmpty(find)) {
                        promise2 = await this.fbfxSz(sdProjectElement, sdUnit);
                    } else {
                        promise2 = await this.fbfxSg(sdProjectElement, find, sdUnit);
                    }

                    //添加清单
                    resultList.push(promise2);

                    //审定对应定额
                    let sdDe = sdProject.filter(i => i.parentId == sdProjectElement.sequenceNbr);

                    //送审对应定额
                    let ssDe = ssProject.filter(i => i.parentId == ysshGlId);

                    //定额循环
                    if (!ObjectUtils.isEmpty(sdDe)) {
                        //清单下对应定额主键list
                        let set1 = new Set();
                        for (let filterElement of sdDe) {
                            let ysshGlId1 = filterElement.ysshGlId;
                            if (ObjectUtils.isEmpty(ysshGlId1)) {
                                //为空表示 审增项
                                let promise3 = await this.fbfxSz(filterElement);
                                //添加定额
                                resultList.push(promise3);
                            } else {
                                let find1 = ssDe.find(i => i.sequenceNbr == ysshGlId1);

                                let promise4;
                                if (ObjectUtils.isEmpty(find1)) {
                                    promise4 = await this.fbfxSz(filterElement, sdUnit);
                                } else {
                                    set1.add(ysshGlId1);
                                    promise4 = await this.fbfxSg(filterElement, find1);
                                }

                                //添加定额
                                resultList.push(promise4);
                            }
                        }
                        //这部分过滤对应 关联清单下 审删 定额
                        let deList = ssDe.filter(i => {
                            return !set1.has(i.sequenceNbr);
                        });
                        //添加审删定额
                        if (!ObjectUtils.isEmpty(deList)) {
                            for (let deListElement of deList) {
                                let promise5 = await this.fbfxSs(deListElement);
                                resultList.push(promise5);
                            }
                        }
                    }
                }
            }
        }

        //送审 审删逻辑 处理
        let ssdeList = ssProject.filter(i => {
            return (i.kind == BranchProjectLevelConstant.qd || i.kind == BranchProjectLevelConstant.de)
                && (!set.has(i.sequenceNbr) && !set.has(i.parentId))
        });

        for (let ssdeListElement of ssdeList) {
            let ssqdDe = await this.fbfxSs(ssdeListElement);
            resultList.push(ssqdDe);
        }
        return resultList;
    }


    /**
     * 审定 审增项对象处理
     * @param itemBillProject 审定对象
     * @returns {Promise<void>}
     */
    async fbfxSz(itemBillProject, unitId) {
        let sssj = {};

        let changeExplain = YsshssConstant.insertExplain;
        //增减金额
        sssj[YsshssConstant.changeTotal] = itemBillProject.total;
        sssj[YsshssConstant.changeQuantity]=NumberUtil.subtract(itemBillProject.quantity, null);
        if (ObjectUtils.isEmpty(itemBillProject.total) || itemBillProject.total == 0) {
            //增减比例
            //sssj[YsshssConstant.changeRatio] = 100;
            //数据状态
            sssj[YsshssConstant.change] = YsshssConstant.noChange;

            changeExplain = null;
        } else {
            sssj[YsshssConstant.changeRatio] = 100;
            //数据状态
            sssj[YsshssConstant.change] = YsshssConstant.insert;
        }


        //增江说明手动修改后，按
        if (itemBillProject.changeExplain) {
            changeExplain = itemBillProject.changeExplain;
        }
        //增减说明
        sssj[YsshssConstant.changeExplain] = changeExplain;

        // //清单关联增加默认显示项目
        // if(ObjectUtils.isEmpty(itemBillProject.ysshQdgl)){
        //     sssj[YsshssConstant.qingdanGL] = {
        //         qdglId:null,
        //         qdglName:null,
        //         ysshQdglId:null,
        //         sortNo:null
        //     }
        // }else{
        //     if(itemBillProject.ysshQdgl.type === 'add'){
        //         //正常项变更为审增项
        //         //处理清单关联数据
        //         let qingdan = {
        //             sdConstructId:unitId.constructId,
        //             sdSingleId:unitId.spId,
        //             sdUnitId:unitId.sequenceNbr,
        //             sdItemBillId:itemBillProject.sequenceNbr
        //         };
        //         this.shDelete(qingdan);
        //         itemBillProject.ysshQdgl = null;
        //         sssj[YsshssConstant.qingdanGL] = {
        //             qdglId:null,
        //             qdglName:null,
        //             ysshQdglId:null,
        //             sortNo:null
        //         }
        //     }else{
        //         sssj[YsshssConstant.qingdanGL] = itemBillProject.ysshQdgl;
        //     }
        // }

        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]: sssj}, itemBillProject); //新对象

        return assign;
    }

    /**
     * 全部为审删项对象处理
     * @param itemBillProject 审删对象
     * @returns {Promise<void>}
     */
    async fbfxAllSs(itemBillProject) {
        let sssj = {};
        //送审数据
        //编码,名称,单位,项目特征,工程量,综合单价,综合合价,单价,合价,主键
        let {bdCode, bdName, name, unit, projectAttr, quantity, price, total, zjfPrice, zjfTotal, sequenceNbr, parentId, kind, displaySign,type} = itemBillProject;
        sssj.bdCode = bdCode;
        sssj.bdName = bdName;
        sssj.name = name;
        sssj.unit = unit;
        sssj.kind = kind;
        sssj.type = type;
        sssj.projectAttr = projectAttr;
        sssj.quantity = quantity;
        sssj.price = price;
        sssj.total = total ? total : 0;
        sssj.zjfPrice = zjfPrice;
        sssj.zjfTotal = zjfTotal;
        sssj.sequenceNbr = sequenceNbr;
        sssj.displaySign = displaySign;
        let changeExplain = YsshssConstant.deleteExplain;

        if (ObjectUtils.isEmpty(itemBillProject.total) || itemBillProject.total == 0) {
            //增减比例
            sssj[YsshssConstant.changeRatio] = -100;
            //数据状态
            sssj[YsshssConstant.change] = YsshssConstant.noChange;

            changeExplain = null;
        } else {
            //增减比例
            sssj[YsshssConstant.changeRatio] = -100;
            //数据状态
            sssj[YsshssConstant.change] = YsshssConstant.delete;

            //增减金额
            sssj[YsshssConstant.changeTotal] = "-" + itemBillProject.total;
        }

        sssj[YsshssConstant.changeQuantity]=NumberUtil.subtract(null,itemBillProject.quantity);
        //增江说明手动修改后，按
        if (itemBillProject.changeExplain) {
            changeExplain = itemBillProject.changeExplain;
        }
        //增减说明
        sssj[YsshssConstant.changeExplain] = changeExplain;

        // //全部审删直接赋值默认的清单关联数据
        // sssj[YsshssConstant.qingdanGL] = {
        //     qdglId:null,
        //     qdglName:null,
        //     ysshQdglId:null,
        //     sortNo:null
        // }

        let assign = {};
        assign[YsshssConstant.ysshSysj] = sssj;
        assign['parentId'] = parentId;
        assign['sequenceNbr'] = sequenceNbr;
        assign['kind'] = kind;
        assign['displaySign'] = displaySign;
        return assign;
    }

    /**
     * 审定 审删项对象处理
     * @param itemBillProject 审删对象
     * @returns {Promise<void>}
     */
    async fbfxSs(itemBillProject) {
        let sssj = {};
        //送审数据
        //编码,名称,单位,项目特征,工程量,综合单价,综合合价,单价,合价,主键
        let {bdCode, bdName, name, unit, projectAttr, quantity, price, total, zjfPrice, zjfTotal, sequenceNbr, parentId, kind, displaySign,type} = itemBillProject;
        sssj.bdCode = bdCode;
        sssj.bdName = bdName;
        sssj.name = name;
        sssj.unit = unit;
        sssj.kind = kind;
        sssj.type = type;
        sssj.projectAttr = projectAttr;
        sssj.quantity = quantity;
        sssj.price = price;
        sssj.total = total ? total : 0;
        sssj.zjfPrice = zjfPrice;
        sssj.zjfTotal = zjfTotal;
        sssj.sequenceNbr = sequenceNbr;
        sssj.displaySign = displaySign;


        let changeExplain = YsshssConstant.deleteExplain;
        //处理空数据标识状态
        if (ObjectUtils.isEmpty(itemBillProject.total) || itemBillProject.total == 0) {
            //增减比例
            sssj[YsshssConstant.changeRatio] = -100;
            //数据状态
            sssj[YsshssConstant.change] = YsshssConstant.delete;
        } else {
            //增减比例
            sssj[YsshssConstant.changeRatio] = -100;
            //数据状态
            sssj[YsshssConstant.change] = YsshssConstant.delete;

            //增减金额
            sssj[YsshssConstant.changeTotal] = "-" + itemBillProject.total;
        }

        //增江说明手动修改后，按
        if (itemBillProject.changeExplain) {
            changeExplain = itemBillProject.changeExplain;
        }

        //增减说明
        sssj[YsshssConstant.changeExplain] = changeExplain;
        sssj[YsshssConstant.changeQuantity]=NumberUtil.subtract(null,itemBillProject.quantity);
        // //审删直接赋值默认的清单关联数据
        // sssj[YsshssConstant.qingdanGL] = {
        //     qdglId:null,
        //     qdglName:null,
        //     ysshQdglId:null,
        //     sortNo:null
        // }
        let assign = {};
        assign[YsshssConstant.ysshSysj] = sssj;
        if (kind === BranchProjectLevelConstant.qd) {
            assign['parentId'] = null;
        } else {
            assign['parentId'] = parentId;
        }
        assign['sequenceNbr'] = Snowflake.nextId();
        assign['kind'] = kind;
        assign['displaySign'] = displaySign;
        return assign;
    }


    /**
     * 审定 审改项对象处理
     * @param itemBillProject 审删对象
     * @returns {Promise<void>}
     */
    async fbfxSg(sdItemBillProject, ssItemBillProject, unitId) {
        let sssj = {};
        //送审数据
        //编码,名称,单位,项目特征,工程量,综合单价,综合合价,单价,合价,主键
        let {bdCode, bdName, name, unit, projectAttr, quantity, price, total, zjfPrice, zjfTotal, sequenceNbr} = ssItemBillProject;
        sssj.bdCode = bdCode;
        sssj.bdName = bdName;
        sssj.name = name;
        sssj.unit = unit;
        sssj.projectAttr = projectAttr;
        sssj.quantity = quantity;
        sssj.price = price;
        sssj.total = total ? total : 0;
        sssj.zjfPrice = zjfPrice;
        sssj.zjfTotal = zjfTotal;
        sssj.sequenceNbr = sequenceNbr;

        //工程量差
        let subtract = NumberUtil.subtract(sdItemBillProject.quantity, ssItemBillProject.quantity);

        //增减金额
        let subtract1 = NumberUtil.subtract(sdItemBillProject.total, ssItemBillProject.total);

        //增减单价
        let subtract2 = NumberUtil.subtract(sdItemBillProject.price, ssItemBillProject.price);
        //工程量差
        sssj[YsshssConstant.changeQuantity] = subtract;
        //增减金额
        sssj[YsshssConstant.changeTotal] = subtract1;

        sssj[YsshssConstant.change] = YsshssConstant.noChange;

        //增减比例
        sssj[YsshssConstant.changeRatio] = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.divide(subtract1, ssItemBillProject.total), 100));
        let changeExplain = "";
        if (subtract != 0) {
            changeExplain = changeExplain + "[调量]";
            sssj[YsshssConstant.change] = YsshssConstant.update;
        }
        if (subtract2 != 0) {
            changeExplain = changeExplain + "[调价]"
            sssj[YsshssConstant.change] = YsshssConstant.update;
        }


        if ((ssItemBillProject.total != sdItemBillProject.total) && (ssItemBillProject.total == 0 || sdItemBillProject.total == 0)) {
            if (sdItemBillProject.total == 0) {
                //审删
                sssj[YsshssConstant.change] = YsshssConstant.delete;

                //增减比例
                sssj[YsshssConstant.changeRatio] = -100;
            }

            if (ssItemBillProject.total == 0) {
                //审增
                sssj[YsshssConstant.change] = YsshssConstant.insert;
                //增减比例
                sssj[YsshssConstant.changeRatio] = 100;
            }
        }


        // //第二种审删状态 判断 送审工程量不为0 不为空,审定为0
        // if (!ObjectUtils.isEmpty(ssItemBillProject.total) && ssItemBillProject.total != 0
        //     && (ObjectUtils.isEmpty(sdItemBillProject.total) || sdItemBillProject.total == 0)) {
        //     changeExplain = YsshssConstant.deleteExplain;
        //     sssj[YsshssConstant.change] = YsshssConstant.delete;
        //     //增减比例
        //     sssj[YsshssConstant.changeRatio] = 100;
        //
        //
        //     // //正常项变更为审删除
        //     // if(ObjectUtils.isNotEmpty(sdItemBillProject.ysshQdgl)){
        //     //     if(sdItemBillProject.ysshQdgl.type == 'add'){
        //     //         //处理清单关联数据
        //     //         let qingdan = {
        //     //             sdConstructId:unitId.constructId,
        //     //             sdSingleId:unitId.spId,
        //     //             sdUnitId:unitId.sequenceNbr,
        //     //             sdItemBillId:sdItemBillProject.sequenceNbr
        //     //         };
        //     //         this.shDelete(qingdan);
        //     //     }else{
        //     //         sdItemBillProject.ysshQdgl = null;
        //     //     }
        //     // }
        // }

        //增江说明手动修改后，按
        if (sdItemBillProject.changeExplain) {
            changeExplain = sdItemBillProject.changeExplain;
        }
        //增减说明
        sssj[YsshssConstant.changeExplain] = changeExplain;


        // //清单关联增加默认显示项目
        // if(ObjectUtils.isEmpty(sdItemBillProject.ysshQdgl)){
        //     sssj[YsshssConstant.qingdanGL] = {
        //         qdglId:null,
        //         qdglName:null,
        //         ysshQdglId:null,
        //         sortNo:null
        //     }
        // }else{
        //     sssj[YsshssConstant.qingdanGL] = sdItemBillProject.ysshQdgl;
        // }
        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]: sssj}, sdItemBillProject); //新对象
        assign.ysshSysj =  Object.assign(sssj, ssItemBillProject);
        return assign;
    }


    /**
     * 单价构成比对
     * @param args
     * @returns {Promise<void>}
     */
    async feeBuildComparison(args) {
        //ssId 送审ID ,sdId 审定ID
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, ssId, sdId} = args;

        //送审单价构成
        let ssdjgc = await this.service.unitPriceService.getPriceBuild(ssConstructId, ssSingleId, ssUnitId, ssId);
        //审定单价构成
        //如果查询的为定额单价构成  先查是否有缓存编辑
        let itemBillProject = PricingFileFindUtils.getUnit(constructId,singleId,unitId).itemBillProjects.filter(item => item.sequenceNbr==sdId)[0];
        let sddjgc = {};
        if (itemBillProject.kind == BranchProjectLevelConstant.de) {
            let newArgs = {};
            newArgs.deId = sdId;
            sddjgc = await this.service.danjiagouchengService.queryforDeId(newArgs);
            if (ObjectUtils.isEmpty(sddjgc)) {
                sddjgc = await this.service.unitPriceService.getPriceBuild(constructId, singleId, unitId, sdId);
            }
        }else {
            sddjgc = await this.service.unitPriceService.getPriceBuild(constructId, singleId, unitId, sdId);
        }

        //给前端返回的数据
        let resultList = [];

        if (ObjectUtils.isEmpty(ssdjgc) && ObjectUtils.isEmpty(sddjgc)) {
            return resultList;
        }


        //全为审删
        if (ObjectUtils.isEmpty(sddjgc)) {
            for (let sddjgcKey of ssdjgc) {
                let promise = await this.feeBuildSs(sddjgcKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //全为审增
        if (ObjectUtils.isEmpty(ssdjgc)) {
            for (let sddjgcKey of sddjgc) {
                let promise = await this.feeBuildSz(sddjgcKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //审改 或不发生变化
        if (!ObjectUtils.isEmpty(ssdjgc) && !ObjectUtils.isEmpty(sddjgc)) {
            for (let sddjgcKey of sddjgc) {
                //送审对象
                let find = ssdjgc.find(i => i.type == sddjgcKey.type);
                let promise = await this.feeBuildSg(sddjgcKey, find);
                resultList.push(promise);
            }
            return resultList;
        }

    }

    /**
     * 审 删 单价构成 (送审有 审定没有)
     * @param fee
     * @returns {Promise<void>}
     */
    async feeBuildSs(fee) {
        let sssj = {};
        //送审数据
        //计算基数 费率 单价 合价
        let {caculateBase, rate, displayUnitPrice, displayAllPrice} = fee;
        sssj.caculateBase = caculateBase;
        sssj.rate = rate;
        sssj.displayUnitPrice = displayUnitPrice;
        sssj.displayAllPrice = displayAllPrice;


        /*//增减金额
        sssj[YsshssConstant.changeTotal] = "-"+itemBillProject.total;
        //增减比例
        sssj[YsshssConstant.changeRatio] = -100;
        //数据状态
        sssj[YsshssConstant.change] = YsshssConstant.delete;*/

        let assign = {};
        assign[YsshssConstant.ysshSysj] = sssj;
        return assign;

    }

    /**
     * 审 增单价构成 (送审没有 审定有)
     * @param fee
     * @returns {Promise<void>}
     */
    async feeBuildSz(fee) {
        let sssj = {};
        /*//增减金额
        sssj[YsshssConstant.changeTotal] = itemBillProject.total;
        //增减比例
        sssj[YsshssConstant.changeRatio] = 100;
        //数据状态
        sssj[YsshssConstant.change] = YsshssConstant.insert;*/
        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]: sssj}, fee); //新对象

        return assign;
    }

    /**
     * 审 改 单价构成 (送审有 审定有)
     * @param sdfee 审定对象
     * @param ssfee 送审对象
     * @returns {Promise<any>}
     */
    async feeBuildSg(sdfee, ssfee) {
        let sssj = {};
        //送审数据
        //计算基数 费率 单价 合价
        let {caculateBase, rate, displayUnitPrice, displayAllPrice} = ssfee;
        sssj.caculateBase = caculateBase;
        sssj.rate = rate;
        sssj.displayUnitPrice = displayUnitPrice;
        sssj.displayAllPrice = displayAllPrice;

        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]: sssj}, sdfee); //新对象

        return assign;
    }


    /**
     * 清单定额人材机明细比对
     * @param args
     * @returns {Promise<void>}
     */
    async rcjComparison(args) {
        //ssId 送审ID ,sdId 审定ID branchType 1分部分项、2措施项目
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, ssId, sdId, branchType} = args;

        let ssRcj;
        let sdRcj;
        //送审人材机
        if (!ObjectUtils.isEmpty(ssId)) {
            try {
                ssRcj = await this.getYsRcj(ssConstructId, ssSingleId, ssUnitId, branchType, ssId);
            } catch (e) {
                ssRcj = null;
            }
        } else {
            ssRcj = null;
        }
        //审定人材机
        if (!ObjectUtils.isEmpty(sdId)) {
            try {
                sdRcj = await this.getYsRcj(constructId, singleId, unitId, branchType, sdId);
            } catch (e) {
                sdRcj = null;
            }
        } else {
            sdRcj = null;
        }


        //给前端返回的数据
        let resultList = [];

        if (ObjectUtils.isEmpty(ssRcj) && ObjectUtils.isEmpty(sdRcj)) {
            return resultList;
        }

        //全为审删
        if (ObjectUtils.isEmpty(sdRcj)) {
            for (let ssRcjKey of ssRcj) {
                let promise = await this.rcjSs(ssRcjKey);
                resultList.push(promise);
                let rcjDetails = await this.rcjDetailsComparison(ssRcjKey.rcjDetailsDTOs, null);
                promise.rcjDetailsDTOs = rcjDetails;
            }
            return resultList;
        }

        //全为审增
        if (ObjectUtils.isEmpty(ssRcj)) {
            for (let sdRcjKey of sdRcj) {
                let promise = await this.rcjSz(sdRcjKey);
                resultList.push(promise);
                let rcjDetails = await this.rcjDetailsComparison(null, sdRcjKey.rcjDetailsDTOs);
                promise.rcjDetailsDTOs = rcjDetails;
            }
            return resultList;
        }

        //审改 或不发生变化
        if (!ObjectUtils.isEmpty(ssRcj) && !ObjectUtils.isEmpty(sdRcj)) {
            //送审 id 集合
            let set = new Set();
            for (let sdRcjKey of sdRcj) {
                //人材机对象
                let find = ssRcj.find(i => sdRcjKey.materialCode == i.materialCode && !set.has(i.sequenceNbr));

                if (ObjectUtils.isEmpty(find)) {
                    let promise = await this.rcjSz(sdRcjKey);
                    resultList.push(promise);
                    //处理二级
                    let rcjDetails = await this.rcjDetailsComparison(null, sdRcjKey.rcjDetailsDTOs);
                    promise.rcjDetailsDTOs = rcjDetails;

                } else {
                    set.add(find.sequenceNbr);
                    let promise = await this.rcjSg(sdRcjKey, find);
                    resultList.push(promise);
                    //处理二级
                    let rcjDetails = await this.rcjDetailsComparison(find.rcjDetailsDTOs, sdRcjKey.rcjDetailsDTOs);
                    promise.rcjDetailsDTOs = rcjDetails;
                }
            }

            let filter = ssRcj.filter(i => !set.has(i.sequenceNbr));
            if (!ObjectUtils.isEmpty(filter)) {
                for (let filterElement of filter) {
                    let promise1 = await this.rcjSs(filterElement);
                    resultList.push(promise1);
                    let rcjDetails = await this.rcjDetailsComparison(filterElement.rcjDetailsDTOs, null);
                    promise1.rcjDetailsDTOs = rcjDetails;
                }
            }

            return resultList;
        }

    }

    /**
     * 人材机 二级材料对比
     * @param ssRcjDetails 送审二级材料
     * @param sdRcjDetails 审定二级材料
     * @returns {Promise<void>}
     */
    async rcjDetailsComparison(ssRcjDetails, sdRcjDetails) {
        //给前端返回的数据
        let resultList = [];
        if (ObjectUtils.isEmpty(ssRcjDetails) && ObjectUtils.isEmpty(sdRcjDetails)) {
            return resultList;
        }


        //全为审删
        if (ObjectUtils.isEmpty(sdRcjDetails)) {
            for (let ssRcjKey of ssRcjDetails) {
                let promise = await this.rcjSs(ssRcjKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //全为审增
        if (ObjectUtils.isEmpty(ssRcjDetails)) {
            for (let sdRcjKey of sdRcjDetails) {
                let promise = await this.rcjSz(sdRcjKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //审改 或不发生变化
        if (!ObjectUtils.isEmpty(ssRcjDetails) && !ObjectUtils.isEmpty(sdRcjDetails)) {
            //送审 id 集合
            let set = new Set();
            for (let sdRcjKey of sdRcjDetails) {
                //人材机对象
                let find = ssRcjDetails.find(i => sdRcjKey.materialCode == i.materialCode && !set.has(i.sequenceNbr));
                if (ObjectUtils.isEmpty(find)) {
                    let promise = await this.rcjSz(sdRcjKey);
                    resultList.push(promise);
                } else {
                    set.add(find.sequenceNbr);
                    let promise = await this.rcjSg(sdRcjKey,find);
                    resultList.push(promise);
                }
            }

            let filter = ssRcjDetails.filter(i => !set.has(i.sequenceNbr));
            if (!ObjectUtils.isEmpty(filter)) {
                for (let filterElement of filter) {
                    let promise1 = await this.rcjSs(filterElement);
                    resultList.push(promise1);
                }
            }

            return resultList;
        }


    }


    /**
     * 获取预算 人材机明细
     * @param onstructId
     * @param singleId
     * @param unitId
     * @param branchType
     * @param id
     * @returns {Promise<void>}
     */
    async getYsRcj(constructId, singleId, unitId, branchType, id) {
        let result = this.service.rcjProcess.listRcjData(branchType, id, constructId, singleId, unitId);
        if (result && result.length > 0) {
            let all = [];
            all = all.concat(PricingFileFindUtils.getFbFx(constructId, singleId, unitId).getAllNodes()).concat(PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes());
            let qdLine = this._findLineById(all, id);
            if (qdLine.rcjFlag === 1 && result[0].rcjDetailsDTOs /*&& result[0].rcjDetailsDTOs.length > 0*/) {
                result = result[0].rcjDetailsDTOs;
            }
        }

        if (result && result.length > 0) {
            for (let i = 0; i < result.length; ++i) {
                if (result[i].resQty && result[i].resQty != "") {
                    result[i].resQty = NumberUtil.numberScale(Number(result[i].resQty), 6);
                }
                if (result[i].totalNumber && result[i].totalNumber != "") {
                    result[i].totalNumber = NumberUtil.numberScale(Number(result[i].totalNumber), 4);
                }
            }
        }

        return result;

    }


    /**
     * 预算复制过来的
     * @param allData
     * @param id
     * @returns {*}
     * @private
     */
    _findLineById(allData, id) {
        for (let i = 0; i < allData.length; ++i) {
            if (id === allData[i].sequenceNbr) {
                return allData[i];
            }
        }
    }


    /**
     * 审 删 人材机 (送审有 审定没有)
     * @param rcj
     * @returns {Promise<void>}
     */
    async rcjSs(rcj) {
        let sssj = {};
        //送审数据 编码 名称 类别 规格型号,单位,定额单价,市场价,初始含量,含量
        let {materialCode, materialName, type, specification, unit, dePrice, marketPrice, initResQty, resQty, sequenceNbr} = rcj;
        sssj.materialCode = materialCode;
        sssj.materialName = materialName;
        sssj.type = type;
        sssj.specification = specification;
        sssj.unit = unit;
        sssj.dePrice = dePrice;
        sssj.marketPrice = marketPrice;
        sssj.initResQty = initResQty;
        sssj.resQty = resQty;

        //数据状态
        sssj[YsshssConstant.change] = YsshssConstant.delete;


        let assign = {};
        assign.materialCode = materialCode;
        assign.materialName = materialName;
        assign.type = type;
        assign.specification = specification;
        assign.unit = unit;

        assign[YsshssConstant.ysshSysj] = sssj;
        assign.sequenceNbr = sequenceNbr
        return assign;

    }

    /**
     * 审 增人材机 (送审没有 审定有)
     * @param rcj
     * @returns {Promise<void>}
     */
    async rcjSz(rcj) {
        let sssj = {};

        //数据状态
        sssj[YsshssConstant.change] = YsshssConstant.insert;
        sssj.dePrice = rcj.dePrice;
        sssj.initResQty = rcj.initResQty;
        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]: sssj}, rcj); //新对象

        return assign;
    }

    /**
     * 审 改 或不变 人材机 (送审有 审定有)
     * @param sdRcj 审定对象
     * @param ssRcj 送审对象
     * @returns {Promise<any>}
     */
    async rcjSg(sdRcj, ssRcj) {
        let sssj = {};
        //送审数据 编码 名称 类别 规格型号,单位,定额单价,市场价,初始含量,含量
        let {materialCode, materialName, type, specification, unit, dePrice, marketPrice, initResQty, resQty} = ssRcj;
        sssj.materialCode = materialCode;
        sssj.materialName = materialName;
        sssj.type = type;
        sssj.specification = specification;
        sssj.unit = unit;
        sssj.dePrice = dePrice;
        sssj.marketPrice = marketPrice;
        sssj.initResQty = initResQty;
        sssj.resQty = resQty;

        //数据状态
        if (marketPrice != sdRcj.marketPrice || resQty != sdRcj.resQty || ssRcj.total != sdRcj.total) {
            sssj[YsshssConstant.change] = YsshssConstant.update;
        } else {
            sssj[YsshssConstant.change] = YsshssConstant.noChange;
        }

        //再次判断数据状态
        //合计如果都不为O但是其中一方为0就会出现 审删 和 审增的第二种判断情况
        if ((sdRcj.total != ssRcj.total) && (sdRcj.total == 0 || ssRcj.total == 0)) {
            if (sdRcj.total == 0) {
                //审删
                sssj[YsshssConstant.change] = YsshssConstant.delete;

                //增减比例
                sssj[YsshssConstant.changeRatio] = 100;
            }

            if (ssRcj.total == 0) {
                //审增
                sssj[YsshssConstant.change] = YsshssConstant.insert;

                //增减比例
                sssj[YsshssConstant.changeRatio] = 100;
            }
        }

        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]: sssj}, sdRcj); //新对象

        return assign;
    }


    /**
     * 送审数据转审定  替换原有审定数据 与送审数据同步
     * type  "fbfx"/"csxm" 编辑区对应页面
     *
     * @returns {Promise<void>}
     */
    async ssToSdDataConvert(args) {
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, type, replaceIds} = args;
        //审定数据对应单位
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let unitProjectSs = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
        let ssData = [];
        let sdData = [];//选中的审定数据
        args['pageNum'] = 1;
        args['pageSize'] = 10000;
        args['isAllFlag'] = true;
        if (type == "fbfx") {
            sdData = (await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxDataMatching(args)).data.filter(item => replaceIds.includes(item.sequenceNbr));
            for (let i = 0; i < sdData.length; i++) {
                let sdDatum = sdData[i];
                let itemSs = unitProjectSs.itemBillProjects.filter(item => ObjectUtils.isNotEmpty(sdDatum.ysshSysj) && item.sequenceNbr == sdDatum.ysshSysj.sequenceNbr)[0];
                if (ObjectUtils.isNotEmpty(itemSs)) {
                    ssData.push(itemSs);
                }
            }
        } else {
            sdData = (await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(args)).filter(item => replaceIds.includes(item.sequenceNbr));
            for (let i = 0; i < sdData.length; i++) {
                let sdDatum = sdData[i];
                let itemSs = unitProjectSs.measureProjectTables.filter(item => ObjectUtils.isNotEmpty(sdDatum.ysshSysj) && item.sequenceNbr == sdDatum.ysshSysj.sequenceNbr)[0];
                if (ObjectUtils.isNotEmpty(itemSs)) {
                    ssData.push(itemSs);
                }
            }
        }
        for (let i = 0; i < sdData.length; i++) {
            let sdDatum = sdData[i];
            //todo 得到审定对应的送审数据 前端会对审增项的勾选进行限制  所以送审数据不应为空
            let ssDatum = ssData.filter(item => ObjectUtils.isNotEmpty(sdDatum.ysshSysj) && item.sequenceNbr == sdDatum.ysshSysj.sequenceNbr)[0];
            if (ObjectUtils.isEmpty(ssDatum)) continue;

            let newData = {};
            if (sdDatum.kind == "03") { //如果要替换的审定数据为清单
                let selectId = ssDatum.standardId;//源数据的standardId 即送审项
                let replaceId = sdDatum.sequenceNbr;//被替换的目标清单id
                let isEdit = 1;// 1编辑区 2明细区
                let unit = ssDatum.unit;
                let libraryCode = ssDatum.libraryCode;
                let conversionCoefficient = null;

                if (type == "fbfx") {
                    try {
                        newData = await this.service.itemBillProjectOptionService.replaceFromIndexPage(constructId, singleId, unitId, selectId, replaceId, isEdit, conversionCoefficient, unit, "03", false, libraryCode);

                        await this.service.dataReplaceService.replaceOriginToTarget(constructId, singleId, unitId, unitProjectSs, ssDatum.sequenceNbr, replaceId);
                        let upDateInfo = {};
                        upDateInfo['column'] = "projectAttr";
                        upDateInfo['value'] = ssDatum.projectAttr;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "fbfx");
                        upDateInfo['column'] = "quantityExpression";
                        upDateInfo['value'] = ssDatum.quantityExpression;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "fbfx");
                        upDateInfo['column'] = "name";
                        upDateInfo['value'] = ssDatum.name;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "fbfx");
                        upDateInfo['column'] = "quantities";
                        upDateInfo['value'] = ssDatum.quantities;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "fbfx");

                    } catch (e) {
                        console.log(e);
                    }
                } else {
                    try {
                        newData = await this.service.stepItemCostService.replaceFromIndexPage(constructId, singleId, unitId, selectId, replaceId, isEdit, conversionCoefficient, "03",unit, false, libraryCode);

                        await this.service.dataReplaceService.replaceOriginToTarget(constructId, singleId, unitId, unitProjectSs, ssDatum.sequenceNbr, replaceId);
                        let upDateInfo = {};
                        upDateInfo['column'] = "projectAttr";
                        upDateInfo['value'] = ssDatum.projectAttr;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "csxm");
                        upDateInfo['column'] = "quantityExpression";
                        upDateInfo['value'] = ssDatum.quantityExpression;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "csxm");
                        upDateInfo['column'] = "name";
                        upDateInfo['value'] = ssDatum.name;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "csxm");
                        upDateInfo['column'] = "quantities";
                        upDateInfo['value'] = ssDatum.quantities;
                        await this.service.baseBranchProjectOptionService.updateByList(constructId, singleId, unitId, replaceId, upDateInfo, "csxm");
                    } catch (e) {
                        console.log(e);
                    }
                }

            }
            if (sdDatum.kind == "04") {
                //得到原始定额
                let origin = [];
                let newDataList = [];
                //对定额进行删除
                if (type == "fbfx") {
                    //得到审定定额的父清单
                    let qd = unitProject.itemBillProjects.filter(item => item.sequenceNbr == sdDatum.parentId)[0];
                    origin = unitProject.itemBillProjects.filter(item => item.parentId == sdDatum.parentId).map(item => item.sequenceNbr);
                    await this.service.itemBillProjectOptionService.batchDelete(constructId, singleId, unitId, sdDatum.sequenceNbr);
                    let args = {
                        "constructId": constructId,
                        "singleId": singleId,
                        "unitId": unitId,//目标单位工程 id
                        "newUnitId": ssDatum.unitId,//源单位工程
                        "newConstructId": ssConstructId,//源工程项目id
                        "sequenceNbrArray": ssDatum.sequenceNbr,//源定额id
                        "pointLine": qd,
                        "dataType": "qdDe",
                        "qdType": "fbfx"
                    }
                    try {
                        await this.insertQdDe(args);
                    } catch (e) {
                        console.error(e);
                    }
                    //得到替换后的定额集合
                    newDataList = unitProject.itemBillProjects.filter(item => item.parentId == sdDatum.parentId);
                    newData = newDataList.filter(item => !origin.includes(item.sequenceNbr))[0];
                    //得到新数据后需挂审定与送审的关系
                    unitProject.itemBillProjects.filter(item => item.sequenceNbr==newData.sequenceNbr)[0].ysshGlId = ssDatum.sequenceNbr;
                } else {
                    let qd = unitProject.measureProjectTables.filter(item => item.sequenceNbr == sdDatum.parentId)[0];
                    origin = unitProject.measureProjectTables.filter(item => item.parentId == sdDatum.parentId).map(item => item.sequenceNbr);
                    await this.service.stepItemCostService.batchDelete(constructId, singleId, unitId, sdDatum.sequenceNbr);
                    let args = {
                        "constructId": constructId,
                        "singleId": singleId,
                        "unitId": unitId,//目标单位工程 id
                        "newUnitId": ssDatum.unitId,//源单位工程
                        "newConstructId": ssConstructId,//源工程项目id
                        "sequenceNbrArray": ssDatum.sequenceNbr,//源定额id
                        "pointLine": qd,
                        "dataType": "qdDe",
                        "qdType": "csxm"
                    }
                    try {
                        await this.insertQdDe(args);
                    } catch (e) {
                        console.error(e);
                    }
                    newDataList = unitProject.measureProjectTables.filter(item => item.parentId == sdDatum.parentId);
                    newData = newDataList.filter(item => !origin.includes(item.sequenceNbr))[0];
                    //得到新数据后需挂审定与送审的关系
                    unitProject.measureProjectTables.filter(item => item.sequenceNbr==newData.sequenceNbr)[0].ysshGlId = ssDatum.sequenceNbr;
                }
                //查询定额的单价构成  比对费率  审定费率同步为送审
                //--------------------------------------------
                let sdDj = this.service.unitPriceService.getPriceBuild(constructId, singleId, unitId, newData.sequenceNbr);
                let ssDj = this.service.unitPriceService.getPriceBuild(ssConstructId, ssSingleId, ssUnitId, ssDatum.sequenceNbr);
                let optionData = {
                    option:"UPDATE",
                    column:"rate",
                };
                if (ObjectUtils.isNotEmpty(sdDj)) {
                    for (let j = 0; j < sdDj.length; j++) {
                        if (sdDj[j].rate != ssDj[j].rate) {
                            optionData["sequenceNbr"] = sdDj[j].sequenceNbr;
                            optionData["value"] = ssDj[j].rate;

                            let argsParam = {
                                constructId:constructId,
                                singleId:singleId,
                                unitId:unitId,
                                deId:newData.sequenceNbr,
                                pageType:type,
                                optionData:optionData,
                            }
                            await this.service.danjiagouchengService.editor(argsParam);//编辑费率

                            let argsParam2 = {
                                constructId:constructId,
                                singleId:singleId,
                                unitId:unitId,
                                type:"useDe",
                                way:null,
                                deId:newData.sequenceNbr,
                                pageType:type
                            }
                            await this.service.danjiagouchengService.applyEditor(argsParam2);//应用到该定额
                        }
                    }
                }
                //--------------------------------------------
            }
        }
        try {
            await this.service.autoCostMathService.autoCostMath({
                constructId:constructId,
                singleId:singleId,
                unitId:unitId});
            await this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        } catch (e) {
            console.error(e);
        }


    }

    /**
     * 审定数据转送审  替换原有送审数据  与审定数据同步
     * @returns {Promise<void>}
     */
    async sdToSsDataConvert(args) {
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, type, replaceIds} = args;
        //送审数据对应单位
        let unitProject = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
        //审定数据对应单位
        let sdUnitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let ssData = [];
        let sdData = [];//选中的审定数据
        args['pageNum'] = 1;
        args['pageSize'] = 10000;
        args['isAllFlag'] = true;
        if (type == "fbfx") {
            sdData = (await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxDataMatching(args)).data.filter(item => replaceIds.includes(item.sequenceNbr));
            for (let i = 0; i < sdData.length; i++) {
                let sdDatum = sdData[i];
                let itemSs = unitProject.itemBillProjects.filter(item => ObjectUtils.isNotEmpty(sdDatum.ysshSysj) && item.sequenceNbr == sdDatum.ysshSysj.sequenceNbr)[0];
                if (ObjectUtils.isNotEmpty(itemSs)) {
                    ssData.push(itemSs);
                }
            }
        } else {
            sdData = (await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(args)).filter(item => replaceIds.includes(item.sequenceNbr));
            for (let i = 0; i < sdData.length; i++) {
                let sdDatum = sdData[i];
                let itemSs = unitProject.measureProjectTables.filter(item => ObjectUtils.isNotEmpty(sdDatum.ysshSysj) && item.sequenceNbr == sdDatum.ysshSysj.sequenceNbr)[0];
                if (ObjectUtils.isNotEmpty(itemSs)) {
                    ssData.push(itemSs);
                }
            }
        }
        for (let i = 0; i < sdData.length; i++) {
            let sdDatum = sdData[i];
            //todo 得到审定对应的送审数据 前端会对审增项的勾选进行限制  所以送审数据不应为空
            let ssDatum = ssData.filter(item => ObjectUtils.isNotEmpty(sdDatum.ysshSysj) && item.sequenceNbr == sdDatum.ysshSysj.sequenceNbr)[0];
            if (ObjectUtils.isEmpty(ssDatum)) continue;

            let newData = {};
            if (ssDatum.kind == "03") { //如果要替换的送审数据为清单
                let selectId = sdDatum.standardId;//源数据的standardId 即审定项
                let replaceId = ssDatum.sequenceNbr;//被替换的目标清单id
                let isEdit = 1;// 1编辑区 2明细区
                let unit = sdDatum.unit;
                let libraryCode = sdDatum.libraryCode;
                let conversionCoefficient = null;

                if (type == "fbfx") {
                    try {
                        newData = await this.service.itemBillProjectOptionService.replaceFromIndexPage(ssConstructId, ssSingleId, ssUnitId, selectId, replaceId, isEdit, conversionCoefficient, unit, "03", false, libraryCode);
                        await this.service.dataReplaceService.replaceOriginToTarget(ssConstructId, ssSingleId, ssUnitId, sdUnitProject, sdDatum.sequenceNbr, replaceId);
                        let upDateInfo = {};
                        upDateInfo['column'] = "projectAttr";
                        upDateInfo['value'] = sdDatum.projectAttr;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "fbfx");
                        upDateInfo['column'] = "quantityExpression";
                        upDateInfo['value'] = sdDatum.quantityExpression;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "fbfx");
                        upDateInfo['column'] = "name";
                        upDateInfo['value'] = sdDatum.name;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "fbfx");
                        upDateInfo['column'] = "quantities";
                        upDateInfo['value'] = sdDatum.quantities;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "fbfx");
                    } catch (e) {
                        console.error(e);
                    }
                } else {
                    try {
                        newData = await this.service.stepItemCostService.replaceFromIndexPage(ssConstructId, ssSingleId, ssUnitId, selectId, replaceId, isEdit, conversionCoefficient, "03", unit,false, libraryCode);
                        await this.service.dataReplaceService.replaceOriginToTarget(ssConstructId, ssSingleId, ssUnitId, sdUnitProject, sdDatum.sequenceNbr, replaceId);
                        let upDateInfo = {};
                        upDateInfo['column'] = "projectAttr";
                        upDateInfo['value'] = sdDatum.projectAttr;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "csxm");
                        upDateInfo['column'] = "quantityExpression";
                        upDateInfo['value'] = sdDatum.quantityExpression;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "csxm");
                        upDateInfo['column'] = "name";
                        upDateInfo['value'] = sdDatum.name;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "csxm");
                        upDateInfo['column'] = "quantities";
                        upDateInfo['value'] = sdDatum.quantities;
                        await this.service.baseBranchProjectOptionService.updateByList(ssConstructId, ssSingleId, ssUnitId, replaceId, upDateInfo, "csxm");
                    } catch (e) {
                        console.error(e);
                    }
                }
            }
            if (ssDatum.kind == "04") {
                //得到原始定额
                let origin = [];
                let newDataList = [];
                //对定额进行删除
                if (type == "fbfx") {
                    //得到送审定额的父清单
                    let qd = unitProject.itemBillProjects.filter(item => item.sequenceNbr == ssDatum.parentId)[0];
                    origin = unitProject.itemBillProjects.filter(item => item.parentId == ssDatum.parentId).map(item => item.sequenceNbr);
                    await this.service.itemBillProjectOptionService.batchDelete(ssConstructId, ssSingleId, ssUnitId, ssDatum.sequenceNbr);
                    let args = {
                        "constructId": ssConstructId,
                        "singleId": ssSingleId,
                        "unitId": ssUnitId,//目标单位工程 id
                        "newUnitId": sdDatum.unitId,//源单位工程
                        "newConstructId": constructId,//源工程项目id
                        "sequenceNbrArray": sdDatum.sequenceNbr,//源定额id
                        "pointLine": qd,
                        "dataType": "qdDe",
                        "qdType": "fbfx"
                    }
                    try {
                        await this.insertQdDe(args);
                    } catch (e) {
                        console.error(e);
                    }
                    //得到替换后的定额集合
                    newDataList = unitProject.itemBillProjects.filter(item => item.parentId == ssDatum.parentId);
                    newData = newDataList.filter(item => !origin.includes(item.sequenceNbr))[0];
                    //得到新数据后需挂审定与送审的关系
                    sdUnitProject.itemBillProjects.filter(item => item.sequenceNbr == sdDatum.sequenceNbr)[0].ysshGlId = newData.sequenceNbr;
                } else {
                    let qd = unitProject.measureProjectTables.filter(item => item.sequenceNbr == ssDatum.parentId)[0];
                    origin = unitProject.measureProjectTables.filter(item => item.parentId == ssDatum.parentId).map(item => item.sequenceNbr);
                    await this.service.stepItemCostService.batchDelete(ssConstructId, ssSingleId, ssUnitId, ssDatum.sequenceNbr);
                    let args = {
                        "constructId": ssConstructId,
                        "singleId": ssSingleId,
                        "unitId": ssUnitId,//目标单位工程 id
                        "newUnitId": sdDatum.unitId,//源单位工程
                        "newConstructId": constructId,//源工程项目id
                        "sequenceNbrArray": sdDatum.sequenceNbr,//源定额id
                        "pointLine": qd,
                        "dataType": "qdDe",
                        "qdType": "csxm"
                    }
                    try {
                        await this.insertQdDe(args);
                    } catch (e) {
                        console.error(e);
                    }
                    newDataList = unitProject.measureProjectTables.filter(item => item.parentId == ssDatum.parentId);
                    newData = newDataList.filter(item => !origin.includes(item.sequenceNbr))[0];
                    //得到新数据后需挂审定与送审的关系
                    sdUnitProject.measureProjectTables.filter(item => item.sequenceNbr == sdDatum.sequenceNbr)[0].ysshGlId = newData.sequenceNbr;
                }
                //查询定额的单价构成  比对费率  送审费率同步为审定
                //--------------------------------------------
                let sdDj = this.service.unitPriceService.getPriceBuild(constructId, singleId, unitId, sdDatum.sequenceNbr);
                let ssDj = this.service.unitPriceService.getPriceBuild(ssConstructId, ssSingleId, ssUnitId, newData.sequenceNbr);
                let optionData = {
                    option:"UPDATE",
                    column:"rate",
                };
                if (ObjectUtils.isNotEmpty(sdDj)) {
                    for (let j = 0; j < sdDj.length; j++) {
                        if (sdDj[j].rate != ssDj[j].rate) {
                            optionData["sequenceNbr"] = ssDj[j].sequenceNbr;
                            optionData["value"] = sdDj[j].rate;

                            let argsParam = {
                                constructId:ssConstructId,
                                singleId:ssSingleId,
                                unitId:ssUnitId,
                                deId:newData.sequenceNbr,
                                pageType:type,
                                optionData:optionData,
                            }
                            await this.service.danjiagouchengService.editor(argsParam);//编辑费率

                            let argsParam2 = {
                                constructId:ssConstructId,
                                singleId:ssSingleId,
                                unitId:ssUnitId,
                                type:"useDe",
                                way:null,
                                deId:newData.sequenceNbr,
                                pageType:type
                            }
                            await this.service.danjiagouchengService.applyEditor(argsParam2);//应用到该定额
                        }
                    }
                }
                //--------------------------------------------
            }

        }
        try {
            await this.service.autoCostMathService.autoCostMath({
                constructId:ssConstructId,
                singleId:ssSingleId,
                unitId:ssUnitId});
            await this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: ssConstructId,
                singleId: ssSingleId,
                unitId: ssUnitId,
            });
        } catch (e) {
            console.error(e);
        }
        // await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxDataMatching(args);

    }

    /**
     * 插入数据（批量提取已有清单）
     * type 当前项目/历史文件
     * sequenceNbrArray 页面选择的清单定额sequenceNbrArray
     * pointLine 选中行
     * dataType  (qd/qdDe)       清单/清单+定额
     * constructId, singleId, unitId 选中清单对应的工作台主键
     * newUnitId 弹窗中 中间清单数据对应的单位工程
     * qdType   "fbfx"/"csxm" 选中行编辑区对应页面
     * @returns {Promise<void>}
     */
    async insertQdDe(args) {
        let {constructId, singleId, unitId, newUnitId, newConstructId, sequenceNbrArray, pointLine, dataType, qdType} = args;
        let constructObj = await PricingFileFindUtils.getProjectObjById(newConstructId);
        let constructProject = _.cloneDeep(constructObj)
        let unitLists = await PricingFileFindUtils.getUnitListByConstructObj(constructProject);


        //根据选择页面选择的清单定额主键筛选

        let coverData = {
            itemBillProjects: [],//分部分项
            measureProjectTables: [],//措施项目
            otherProjects: [],//其他项目
            unitCostSummarys: []
        }
        let unit = unitLists.find(i => i.sequenceNbr == newUnitId);

        if (dataType === 'qd') {
            //过滤定额
            unit.itemBillProjects = unit.itemBillProjects.filter(i => i.kind !== '04')
            unit.measureProjectTables = unit.measureProjectTables.filter(i => i.kind !== '04')
        }
        let filterItemBillProjects = unit.itemBillProjects.filter(obj => sequenceNbrArray.includes(obj.sequenceNbr))

        //拼接单价措施清单定额数据
        let filterMeasureProjectTables = unit.measureProjectTables.filter(obj => sequenceNbrArray.includes(obj.sequenceNbr))

        let filterArray = [...filterItemBillProjects, ...filterMeasureProjectTables]

        let filterArrayKey = _.map(filterArray, (item) => {
            return item.sequenceNbr;
        });
        for (let i = 0; i < filterArray.length; i++) {
            let item = filterArray[i];
            //如果是 HSGCL 额外处理
            if (item.quantityExpression == "HSGCL" && !_.includes(filterArrayKey, item.relationDeId)) {
                item.quantityExpression = item.quantityExpressionNbr + "";
            }
        }
        //filterArray 根据传参只会有一个数组 所以这里两个都进行赋值不是很合适
        coverData.itemBillProjects = filterArray
        coverData.measureProjectTables = filterArray
        // PricingFileFindUtils.setInsertQdDe(Object.assign({}, unit, coverData));

        let copeUnitProject = Object.assign({}, unit, coverData);
        //取消源单位工程的主取费文件标识
        for (let i = 0; i < copeUnitProject.feeFiles.length; i++) {
            let feeFile = copeUnitProject.feeFiles[i];
            if (feeFile.defaultFeeFlag == 1) {
                feeFile.defaultFeeFlag = 0;
            }
        }
        if (qdType == 'csxm') {
            //调用插入新增
            await this.service.stepItemCostService.addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine)
        } else {
            await this.service.itemBillProjectOptionService.addDataByQdDe(copeUnitProject, constructId, singleId, unitId, pointLine)
        }

        //编码重刷
        await this.service.commonService.batchRefresh(args);

    }


    /**
     * 匹配分部分项数据   新建项目的时候根据项目的匹配设置进行清单定额数据匹配
     * @param {*} itemBillProjects
     * @param {*} sshItemBillProjectDst
     * @param {*} matchingSettingArr
     * @returns
     */
    async matchBillProject(args) {
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, matchingSettingArr} = args;
        //获取审定的单位
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //获取送审单位
        let ssUnitProject = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
        //分布分项的匹配
        let itemBillProjects = unitProject.itemBillProjects.getAllNodes();
        let ssItemBillProjectDst = ssUnitProject.itemBillProjects.getAllNodes();

        if (ObjectUtils.isEmpty(itemBillProjects) || ObjectUtils.isEmpty(ssItemBillProjectDst)) {
            return;
        }
        let sshItemBillProjects = ConvertUtil.deepCopy(ssItemBillProjectDst);
        let billMap = this.resetSortQdde(itemBillProjects);
        let sshBillMap = this.resetSortQdde(sshItemBillProjects);
        for (let i = 0; i < itemBillProjects.length; i++) {
            let item = itemBillProjects[i];
            let matchArr = new Array();
            if (item.kind === BranchProjectLevelConstant.qd) {
                let nullQd;
                if (ObjectUtils.isEmpty(item.bdCode) && ObjectUtils.isEmpty(item.name) && ObjectUtils.isEmpty(item.projectAttr)) {
                    nullQd = true;
                }
                //清单的匹配
                let sshItemProjects = sshItemBillProjects.filter(sshItem => {
                    // if(ObjectUtils.isEmpty(item.bdCode) || ObjectUtils.isEmpty(sshItem.bdCode)
                    //     || ObjectUtils.isEmpty(item.bdName) || ObjectUtils.isEmpty(sshItem.bdName)){
                    //     return false;
                    // }
                    if (sshItem.kind === BranchProjectLevelConstant.qd) {
                        // 处理空数据   空清单行/空定额行的判断依据为清单编码、名称、项目特征同时为空数据
                        if (nullQd) {
                            if (ObjectUtils.isEmpty(sshItem.bdCode) && ObjectUtils.isEmpty(sshItem.name) && ObjectUtils.isEmpty(sshItem.projectAttr)) {
                                return true;
                            } else {
                                //空数据只需要和空数据比较  不用考虑匹配设置条件
                                return false;
                            }
                        }
                        if (matchingSettingArr.indexOf('01') > -1) {
                            if (ObjectUtils.isEmpty(item.bdCode) || ObjectUtils.isEmpty(sshItem.bdCode)) {
                                return false;
                            }
                            matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(item.bdCode, sshItem.bdCode));
                        }
                        if (matchingSettingArr.indexOf('02') > -1) {
                            if (ObjectUtils.isEmpty(item.bdCode) || ObjectUtils.isEmpty(sshItem.bdCode)) {
                                return false;
                            }
                            let str1 = item.bdCode.replace(/\s/g, '');
                            let str2 = sshItem.bdCode.replace(/\s/g, '');

                            if (str1.length < 9) {
                                matchArr.push(str2.indexOf(str1) > -1);
                            } else {
                                matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(str1.substring(0, 9), str2.substring(0, 9)));
                            }
                        }
                        if (matchingSettingArr.indexOf('03') > -1) {
                            if (ObjectUtils.isEmpty(item.name) || ObjectUtils.isEmpty(sshItem.name)) {
                                return false;
                            }
                            matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(item.name, sshItem.name));
                        }
                        if (matchingSettingArr.indexOf('04') > -1) {
                            if (ObjectUtils.isEmpty(item.projectAttr) && ObjectUtils.isEmpty(sshItem.projectAttr)) {
                                return false;
                            }
                            matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(item.projectAttr, sshItem.projectAttr));
                        }
                        let overForeach = true;
                        //没有不匹配的就返回true
                        matchArr.forEach(match => {
                            if (!match) {
                                overForeach = false;
                            }
                        });
                        matchArr = [];
                        return overForeach;
                    } else {
                        return false;
                    }
                });
                let sshItemProject = null;
                if (ObjectUtils.isNotEmpty(sshItemProjects)) {
                    //获取第一个没有匹配过的清单数据
                    sshItemProject = sshItemProjects.find(itemSshItem => ObjectUtils.isEmpty(itemSshItem.ysshMatch));
                }
                if (ObjectUtils.isNotEmpty(sshItemProject)) {
                    item.ysshGlId = sshItemProject.sequenceNbr;
                    sshItemProject.ysshMatch = true;
                    //清单下的定额匹配
                    let dingEE = billMap.get(item.sequenceNbr);
                    let sshDingEE = sshBillMap.get(sshItemProject.sequenceNbr);
                    if (ObjectUtils.isNotEmpty(dingEE) && ObjectUtils.isNotEmpty(sshDingEE)) {
                        dingEE.forEach(dingEEItem => {
                            let sshItem = sshDingEE.filter(sshItem => {
                                let dingEEItemName = ObjectUtils.isNotEmpty(dingEEItem.orhName) ? dingEEItem.orhName : dingEEItem.bdName;
                                let sshItemName = ObjectUtils.isNotEmpty(sshItem.orhName) ? sshItem.orhName : sshItem.bdName;

                                if (ObjectUtils.isEmpty(dingEEItem.bdCode) || ObjectUtils.isEmpty(sshItem.bdCode)
                                    || ObjectUtils.isEmpty(dingEEItemName) || ObjectUtils.isEmpty(sshItemName)) {
                                    return false;
                                }
                                if (ObjectUtils.compareStringsIgnoringSpaces(dingEEItem.bdCode, sshItem.bdCode)
                                    && ObjectUtils.compareStringsIgnoringSpaces(dingEEItemName, sshItemName)
                                    && ((ObjectUtils.isEmpty(dingEEItem.unit) && ObjectUtils.isEmpty(sshItem.unit))
                                        || ObjectUtils.compareStringsIgnoringSpaces(dingEEItem.unit, sshItem.unit))
                                ) {
                                    return true;
                                }
                                return false;
                            });
                            let sshItemBill = null;
                            if (ObjectUtils.isNotEmpty(sshItem)) {
                                sshItemBill = sshItem.find(sshItemItem => ObjectUtils.isEmpty(sshItemItem.ysshMatch));
                            }
                            if (ObjectUtils.isNotEmpty(sshItemBill)) {
                                sshItemBill.ysshMatch = true;
                                dingEEItem.ysshGlId = sshItemBill.sequenceNbr;
                            }
                        });
                    }
                }
            }
        }
        return;
    }


    resetSortQdde(itemBillProjects) {
        let billMap = new Map();
        itemBillProjects.forEach(item => {
            if (item.kind === BranchProjectLevelConstant.de) {
                let iBillArray = billMap.get(item.parentId);
                if (ObjectUtils.isEmpty(iBillArray)) {
                    iBillArray = new Array();
                    billMap.set(item.parentId, iBillArray);
                }
                iBillArray.push(item);
            }
        });
        return billMap;
    }


    async changeFbfxGLGuanXi(arg) {
        let {detailId, matchingId, constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId} = arg;

        //获取所有单位数据
        //审定数据
        let unitProject = unitId ? PricingFileFindUtils.getUnit(constructId, singleId, unitId) : null;
        //送审数据
        let sshUnitProject = ssUnitId ? PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId) : null;
        if (ObjectUtils.isEmpty(unitProject) || ObjectUtils.isEmpty(sshUnitProject)) {
            return ResponseData.fail('数据不存在');
        }

        let itemBillProjects = unitProject.itemBillProjects.getAllNodes() || [];
        let sshItemBillProjects = sshUnitProject.itemBillProjects.getAllNodes() || [];

        return this.changeFbfxData(detailId, matchingId, itemBillProjects, sshItemBillProjects, constructId, singleId, unitId);


    }


    /**
     * 更换分部分项数据
     * @param {*} itemBillProjects
     * @param {*} sshItemBillProjects
     * @returns
     */
    changeFbfxData(detailId, matchingId, itemBillProjects, sshItemBillProjects, constructId, spId, unitId) {
        // 重新排列审定数据
        let billMap = new Map();
        let detailItem = this.filterQdData(detailId, itemBillProjects, billMap);
        // 重新排列送审数据
        let sshBillMap = new Map();
        let sshDetailItem = this.filterQdData(matchingId, sshItemBillProjects, sshBillMap);

        if (ObjectUtils.isEmpty(detailItem) || ObjectUtils.isEmpty(sshDetailItem)) {
            return ResponseData.fail('清单或定额数据不存在');
        }
        //处理清单关联数据
        if (sshDetailItem.kind === BranchProjectLevelConstant.qd && sshDetailItem.ysshGlId !== matchingId && ObjectUtils.isNotEmpty(detailItem.ysshQdgl)) {
            let qingdan = {
                sdConstructId: constructId,
                sdSingleId: spId,
                sdUnitId: unitId,
                sdItemBillId: detailItem.sequenceNbr
            };
            this.shDelete(qingdan);
        }

        if (detailItem.kind != sshDetailItem.kind || (detailItem.kind != BranchProjectLevelConstant.de && detailItem.kind != BranchProjectLevelConstant.qd)) {
            return ResponseData.fail('替换清单或定额类型不一致');
        }
        if (detailItem.kind === BranchProjectLevelConstant.de) {
            let billItem = itemBillProjects.find(k => k.sequenceNbr == detailItem.parentId);
            if (sshDetailItem.parentId !== billItem.ysshGlId) {
                return ResponseData.fail('不允许跨清单匹配定额');
            }
        }

        //清空送审关系
        itemBillProjects.forEach(item => {
            if (item.ysshGlId === sshDetailItem.sequenceNbr) {
                item.ysshGlId = null;
                item.ysshSingleId = null;
                //不允许跨清单匹配定额，所有此处理不考虑跨清单的情况
                if (detailItem.kind === BranchProjectLevelConstant.qd) {
                    let dingEE = billMap.get(item.sequenceNbr);
                    if (ObjectUtils.isNotEmpty(dingEE)) {
                        dingEE.forEach(dingEEitem => {
                            dingEEitem.ysshGlId = null;
                            dingEEitem.ysshSingleId = null;
                        });
                    }
                }
            }
        });
        //重新匹配
        detailItem.ysshGlId = sshDetailItem.sequenceNbr;
        //清单匹配需要把定额也匹配
        if (detailItem.kind === BranchProjectLevelConstant.qd) {

            //清空审定定额关系
            let dingEE = billMap.get(detailItem.sequenceNbr);
            if (ObjectUtils.isNotEmpty(dingEE)) {
                dingEE.forEach(item => {
                    item.ysshGlId = null;
                    item.ysshSingleId = null;
                });
            }
            //清单下的定额匹配
            let sshDingEE = sshBillMap.get(sshDetailItem.sequenceNbr);

            if (ObjectUtils.isNotEmpty(dingEE) && ObjectUtils.isNotEmpty(sshDingEE)) {
                dingEE.forEach(item => {
                    let sshItem = sshDingEE.find(sshItem => {

                        let itemName = ObjectUtils.isNotEmpty(item.orhName) ? item.orhName : item.bdName;
                        let sshItemName = ObjectUtils.isNotEmpty(sshItem.orhName) ? sshItem.orhName : sshItem.bdName;
                        if (ObjectUtils.isEmpty(item.bdCode) || ObjectUtils.isEmpty(sshItem.bdCode)
                            || ObjectUtils.isEmpty(itemName) || ObjectUtils.isEmpty(sshItemName)) {
                            return false;
                        }
                        if (ObjectUtils.compareStringsIgnoringSpaces(item.bdCode, sshItem.bdCode)
                            && ObjectUtils.compareStringsIgnoringSpaces(itemName, sshItemName)
                            && ((ObjectUtils.isEmpty(item.unit) && ObjectUtils.isEmpty(sshItem.unit))
                                || ObjectUtils.compareStringsIgnoringSpaces(item.unit, sshItem.unit))) {
                            return true;
                        }
                        return false;
                    });
                    sshItem ? item.ysshGlId = sshItem.sequenceNbr : null;
                });
            }
        }

        return ResponseData.success(true);
    }


    filterQdData(detailId, itemBillProjects, billMap) {
        let detailItem = null;
        if (ObjectUtils.isNotEmpty(itemBillProjects)) {
            itemBillProjects.forEach(item => {
                if (item.kind === BranchProjectLevelConstant.de) {
                    let t = billMap.get(item.parentId);
                    if (ObjectUtils.isEmpty(t)) {
                        t = new Array();
                        billMap.set(item.parentId, t);
                    }
                    t.push(item);
                }
                if (item.sequenceNbr === detailId) {
                    detailItem = item;
                }
            });
        }
        return detailItem;
    }


    /**
     * 删除清单上的关联
     */

    shDelete(args) {
        let {sdConstructId, sdSingleId, sdUnitId, sdItemBillId} = args;
        let sdFbfxList = PricingFileFindUtils.getQdByfbfx(sdConstructId, sdSingleId, sdUnitId);
        if (ObjectUtils.isEmpty(sdFbfxList)) {
            return ResponseData.fail('未查询到审定数据！');
        }
        let fbfxDeal = sdFbfxList.find(item => item.sequenceNbr === sdItemBillId);


        if (ObjectUtils.isNotEmpty(fbfxDeal) && ObjectUtils.isNotEmpty(fbfxDeal.ysshQdgl)) {
            //如果是最后的一个需要减一
            let unit = PricingFileFindUtils.getUnit(sdConstructId, sdSingleId, sdUnitId);
            if (fbfxDeal.ysshQdgl.sortNo === unit.ysshQdglCounter) {
                let otherQdgl = sdFbfxList.filter(item => item.sequenceNbr !== fbfxDeal.sequenceNbr && ObjectUtils.isNotEmpty(item.ysshQdgl) && item.ysshQdgl.qdglId === fbfxDeal.ysshQdgl.qdglId);
                if (ObjectUtils.isEmpty(otherQdgl)) {
                    unit.ysshQdglCounter--;
                } else {
                    if (fbfxDeal.kind === BranchProjectLevelConstant.qd && ObjectUtils.isNotEmpty(fbfxDeal.ysshGlId)) {
                        otherQdgl.forEach(otherItem => otherItem.ysshQdgl = null);
                    }
                }
            }

            fbfxDeal.ysshQdgl = null;
        }
        return ResponseData.success();
    }


    /**
     * 重点项过滤查询
     * @param constructId 审定 工程项目ID
     * @param singleId 单项工程id
     * @param unitId 单位工程id
     * @param list 清单定额集合
     * @returns {Promise<*[]>}
     */
    async focusFiltering(constructId, singleId, unitId,list){

        if (ObjectUtils.isEmpty(list)){
            return list;
        }

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //重点项过滤设置
        let zdxgls = unit.ysshZdxgls;
        if (ObjectUtils.isEmpty(zdxgls)){
            return list;
        }

        let result =[];
        let parentIdSet = new Set();
        for (let listElement of list) {
            //if (listElement.kind == BranchProjectLevelConstant.qd || listElement.kind == BranchProjectLevelConstant.de) {
            if (listElement.kind == BranchProjectLevelConstant.qd) {

                //并集
                /*for (let gl of zdxgls) {
                    let boole = false;
                    //送审过滤
                    if (gl.type == 1) {
                        if (!ObjectUtils.isEmpty(listElement[gl.filtration])) {
                            boole = eval(listElement[gl.filtration] + gl.value + gl.number)
                        }
                        //审定过滤
                    } else if (gl.type == 2) {
                        let property = listElement[YsshssConstant.ysshSysj][gl.filtration];
                        if (!ObjectUtils.isEmpty(property)) {
                            boole = eval(property + gl.value + gl.number)
                        }
                        //对比过滤
                    } else if (gl.type == 3) {
                        let property = listElement[YsshssConstant.ysshSysj][gl.filtration];
                        if (!ObjectUtils.isEmpty(property)) {
                            boole = eval(property + gl.value + gl.number)
                        }
                    }

                    if (boole){
                        result.push(listElement);
                    }
                    break;
                }*/

                let set = new Set();
                //交集
                for (let gl of zdxgls) {
                    //审定过滤
                    if (gl.type == 2) {
                        if (!ObjectUtils.isEmpty(listElement[gl.filtration])) {
                            let boole = eval(listElement[gl.filtration] + gl.value + gl.number);
                            set.add(boole);
                        }
                        //送审过滤
                    } else if (gl.type == 1) {
                        let property = listElement[YsshssConstant.ysshSysj][gl.filtration];
                        if (!ObjectUtils.isEmpty(property)) {
                            let boole = eval(property + gl.value + gl.number);
                            set.add(boole);
                        }
                        //对比过滤
                    } else if (gl.type == 3) {
                        let property = listElement[YsshssConstant.ysshSysj][gl.filtration];
                        if (!ObjectUtils.isEmpty(property)) {
                            let boole = eval(property + gl.value + gl.number);
                            set.add(boole);
                        }
                    }
                }

                if (set.size == 1 && set.has(true)){
                    result.push(listElement);
                    parentIdSet.add(listElement.sequenceNbr)
                }
            }else if (listElement.kind == BranchProjectLevelConstant.de
                && !ObjectUtils.isEmpty(listElement.parentId) && parentIdSet.has(listElement.parentId)){

                result.push(listElement);
            }
        }
        return result;
    }


}

YsshFbfxService.toString = () => '[class YsshFbfxService]';
module.exports = YsshFbfxService;

