const YsshssConstant = require("../enum/YsshssConstant");
const {NumberUtil} = require("../../../common/NumberUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {Service} = require("../../../core");
const zdxgl = require("../jsonData/zdxgl.json");
const shjbxx = require("../jsonData/shjbxx.json");
const projectLevelConstant = require("../../../electron/enum/ProjectLevelConstant");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {ProjectOverview} = require("../../../electron/model/ProjectOverview");
const {Snowflake} = require("../../../electron/utils/Snowflake");


class YsshZdxglService extends Service{

    constructor(ctx) {
        super(ctx);
    }


    /**
     * 获取过滤项默认数据
     * @returns {Promise<void>}
     */
    async getDefaultZdxglData(){
        return zdxgl;
    }

    /**
     * 单位工程的重点项过滤 修改
     * @param args
     * @returns {Promise<Array<Zdxgl>>}
     */
    async updateUnitZdxglData(args){
        const {constructId, singleId, unitId,zdxgls} = args;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        unit.ysshZdxgls = zdxgls;
    }

    /**
     * 获取单位工程的重点项过滤设置
     * @param args
     * @returns {Promise<Array<Zdxgl>>}
     */
    async getUnitZdxglData(args){
        const {constructId, singleId, unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

        return unit.ysshZdxgls;
    }

    /**
     * 单位工程的重点项过滤 清除
     * @param args
     * @returns {Promise<Array<Zdxgl>>}
     */
    async deleteUnitZdxglData(args){
        const {constructId, singleId, unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        delete unit.ysshZdxgls;
    }

    /**
     * 查询工程特征和基本信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getYsshProjectOverview(args){
        let project = null;
        if(args.levelType === projectLevelConstant.construct){
            //工程项目  只有基本信息
            project = PricingFileFindUtils.getConstructProjectJBXX(args.constructId);
        }else {
            project = PricingFileFindUtils.getUnitProjectJBXXOrXMTZ(args.constructId,args.singleId,args.unitId,args.type)
        }
        //审定项目增加审核信息数据
        let arraySH = new Array();
        let notInArray1 = [];
        if(project[0].name !=='审核信息'){
            for (let i in shjbxx) {
                shjbxx[i].type = 0;
                shjbxx[i].addFlag = 0;
                shjbxx[i].lockFlag = 0;
                let listProjectOverviewXX = new ProjectOverview();
                ConvertUtil.setDstBySrc(shjbxx[i], listProjectOverviewXX);
                if('审核信息'===shjbxx[i].name){
                    listProjectOverviewXX.type = "title";
                }
                listProjectOverviewXX.sequenceNbr = Snowflake.nextId();
                arraySH.push(listProjectOverviewXX);
            }
            //对比下审定中没有的数据默认加进去
            /* notInArray1 = arraySH.filter(item1 => !project.some(item2 => item1.name === item2.name));*/
            if(ObjectUtils.isNotEmpty(arraySH)){
                for(let i = 0; i < project.length; i++){
                    if(!shjbxx.some(obj => obj.name === project[i].name)){
                        //dispNo加十四是因为shjbxx审核新增14个表单项   groupCode 分类增加一类
                        project[i].groupCode = project[i].groupCode+1;
                        project[i].dispNo = project[i].dispNo+14;
                    }
                    arraySH.push(project[i]);
                }
                project = arraySH;
            }
        }

        if (args.type == 0 && (args.levelType == projectLevelConstant.construct)){
            const groups = project.reduce((acc, item) => {
                const key = item.groupCode;
                if (!acc[key]) {
                    acc[key] = [];
                }
                acc[key].push(item);
                return acc;
            }, {});

            let array = new Array();
            //此处是为了不让修改内存数据
            let deepCopy = ConvertUtil.deepCopy(groups);
            for (const group in deepCopy) {
                let group1 = deepCopy[group];
                let group1Element1 = group1[0];
                group1.shift();
                group1Element1.childrenList = group1;
                array.push(group1Element1);
            }
            return array;
        }
        return project;
    }


}


YsshZdxglService.toString = () => '[class YsshZdxglService]';
module.exports = YsshZdxglService;