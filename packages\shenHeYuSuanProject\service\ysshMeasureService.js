const {Service} = require('../../../core');
const StepItemCostLevelConstant = require("../../../electron/enum/StepItemCostLevelConstant");
const YsshssConstant = require("../enum/YsshssConstant");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const {ResponseData} = require("../../../common/ResponseData");
const {treeToArray} = require("../../../electron/main_editor/tree");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");


/**
 * 预算审核-措施项目Service
 */
class ysshMeasureService extends Service {

    constructor(ctx) {
        super(ctx);
    }


    filterQdData(detailId, itemBillProjects, billMap){
        let detailItem = null;
        if (ObjectUtils.isNotEmpty(itemBillProjects)){
            itemBillProjects.forEach(item => {
                if(item.kind === StepItemCostLevelConstant.de){
                    let t = billMap.get(item.parentId);
                    if(ObjectUtils.isEmpty(t)){
                        t = new Array();
                        billMap.set(item.parentId,t);
                    }
                    t.push(item);
                }
                if(item.sequenceNbr === detailId){
                    detailItem = item;
                }
            });
        }
        return detailItem;
    }

    initMatchMeasureProject(args){

        let { ssConstructId, ssSingleId, ssUnitId,constructId, singleId, unitId,matchingSettingArr} = args;
        //获取审定
        let  measureProjects = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes();
        //获取送审
        let  ssMeasureProjects  = PricingFileFindUtils.getCSXM(ssConstructId, ssSingleId, ssUnitId).getAllNodes();
        if(ObjectUtils.isEmpty(measureProjects)|| ObjectUtils.isEmpty(ssMeasureProjects)){
            return;
        }
        let ssMeasureProjectTables = ConvertUtil.deepCopy(ssMeasureProjects);

        let billMap = this.service.shenHeYuSuanProject.ysshFbfxService.resetSortQdde(measureProjects);
        let sshBillMap = this.service.shenHeYuSuanProject.ysshFbfxService.resetSortQdde(ssMeasureProjectTables);

        for(let i=0;i<measureProjects.length;i++){
            let item = measureProjects[i];
            let matchArr = new Array();
            if(item.kind === StepItemCostLevelConstant.qd){
                let nullQd;
                if (ObjectUtils.isEmpty(item.bdCode) && ObjectUtils.isEmpty(item.bdName) && ObjectUtils.isEmpty(item.projectAttr)) {
                    nullQd = true;
                }
                //清单的匹配
                let sshItemProjects = ssMeasureProjectTables.filter(ssItem=>{
                    // if(ObjectUtils.isEmpty(item.fxCode) || ObjectUtils.isEmpty(ssItem.fxCode)
                    //     || ObjectUtils.isEmpty(item.name) || ObjectUtils.isEmpty(ssItem.name)){
                    //     return false;
                    // }

                    if(ssItem.kind === StepItemCostLevelConstant.qd){
                        if(ObjectUtils.isNotEmpty(item.parent) && ObjectUtils.isNotEmpty(ssItem.parent)){
                            if(item.parent.kind =='01' && ssItem.parent.kind =='01'){
                                if(item.parent.constructionMeasureType != ssItem.parent.constructionMeasureType){
                                    return false;
                                }
                            }
                            if(item.parent.kind =='02' && ssItem.parent.kind =='01'){
                                if(item.parent.parent.constructionMeasureType != ssItem.parent.constructionMeasureType){
                                    return false;
                                }
                            }
                            if(item.parent.kind =='01' && ssItem.parent.kind =='02'){
                                if(item.parent.constructionMeasureType != ssItem.parent.parent.constructionMeasureType){
                                    return false;
                                }
                            }


                        }

                        // 处理空数据   空清单行/空定额行的判断依据为清单编码、名称、项目特征同时为空数据
                        if (nullQd) {
                            if (ObjectUtils.isEmpty(ssItem.bdCode) && ObjectUtils.isEmpty(ssItem.bdName) && ObjectUtils.isEmpty(ssItem.projectAttr) && ObjectUtils.isEmpty(ssItem.ysshMatch)) {
                                return true;
                            } else {
                                //空数据只需要和空数据比较  不用考虑匹配设置条件
                                return false;
                            }
                        }





                        if(matchingSettingArr.indexOf('01')>-1){
                            matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(item.fxCode, ssItem.fxCode));
                        }
                        if(matchingSettingArr.indexOf('02')>-1){
                            if (ObjectUtils.isEmpty(item.bdCode) || ObjectUtils.isEmpty(ssItem.bdCode)) {
                                return false;
                            }
                            let str1 = item.fxCode.replace(/\s/g, '');
                            let str2 = ssItem.fxCode.replace(/\s/g, '');

                            if(str1.length < 9){
                                matchArr.push(str2.indexOf(str1)>-1);
                            }else{
                                matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(str1.substring(0,9), str2.substring(0,9)));
                            }
                        }
                        if(matchingSettingArr.indexOf('03')>-1){
                            if (ObjectUtils.isEmpty(item.bdName)) {
                                return false;
                            }
                            matchArr.push(ObjectUtils.compareStringsIgnoringSpaces(item.name,ssItem.name));
                        }
                        if(matchingSettingArr.indexOf('04')>-1){

                            matchArr.push(
                                (ObjectUtils.isEmpty(item.projectAttr)&&ObjectUtils.isEmpty(ssItem.projectAttr))
                                || ObjectUtils.compareStringsIgnoringSpaces(item.projectAttr, ssItem.projectAttr)
                            );
                        }
                        //没有不匹配的就返回true
                        let overForeach = true;
                        //没有不匹配的就返回true
                        matchArr.forEach(match=>{
                            if(!match){
                                overForeach = false;
                            }
                        });
                        matchArr = [];
                        return overForeach;
                    }else{
                        return false;
                    }
                });
                let  sshItemProject =  null;
                if(ObjectUtils.isNotEmpty(sshItemProjects)){
                    sshItemProject = sshItemProjects.find(itemSshItem=>ObjectUtils.isEmpty(itemSshItem.ysshMatch));
                }
                if(ObjectUtils.isNotEmpty(sshItemProject)){
                    item.ysshGlId = sshItemProject.sequenceNbr
                    sshItemProject.ysshMatch = true;
                    //清单下的定额匹配
                    let dingEE = billMap.get(item.sequenceNbr);
                    let sshDingEE = sshBillMap.get(sshItemProject.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(dingEE) && ObjectUtils.isNotEmpty(sshDingEE)){
                        dingEE.forEach(dingEEItem=>{
                            let ssItem = sshDingEE.filter(ssItem=>{

                                let dingEEItemName = ObjectUtils.isNotEmpty(dingEEItem.orhName)?dingEEItem.orhName:dingEEItem.name;
                                let sshItemName = ObjectUtils.isNotEmpty(ssItem.orhName)?ssItem.orhName:ssItem.name;

                                if(ObjectUtils.isEmpty(dingEEItem.fxCode) || ObjectUtils.isEmpty(ssItem.fxCode)
                                    || ObjectUtils.isEmpty(dingEEItemName) || ObjectUtils.isEmpty(sshItemName)){
                                    return false;
                                }
                                if(ObjectUtils.compareStringsIgnoringSpaces(dingEEItem.fxCode,ssItem.fxCode)
                                    && ObjectUtils.compareStringsIgnoringSpaces(dingEEItemName,sshItemName)
                                    && ((ObjectUtils.isEmpty(dingEEItem.unit)&&ObjectUtils.isEmpty(ssItem.unit))||ObjectUtils.compareStringsIgnoringSpaces(dingEEItem.unit,ssItem.unit))){
                                    return true;
                                }
                                return false;
                            });
                            let sshItemMeasure = null;
                            if(ObjectUtils.isNotEmpty(ssItem)){
                                sshItemMeasure = ssItem.find(sshItemItem=>ObjectUtils.isEmpty(sshItemItem.ysshMatch));
                            }
                            if(ObjectUtils.isNotEmpty(sshItemMeasure)){
                                dingEEItem.ysshGlId = sshItemMeasure.sequenceNbr;
                                sshItemMeasure.ysshMatch = true;
                            }
                        });
                    }
                }
            }
        }
        return;
    }



    async changeMeasureRelation(args){
        let {detailId,matchingId, constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId} = args;
        //获取审定
        let  measureProjects = PricingFileFindUtils.getCSXM(constructId, singleId, unitId).getAllNodes();
        //获取送审
        let  ssMeasureProjects  = PricingFileFindUtils.getCSXM(ssConstructId, ssSingleId, ssUnitId).getAllNodes();
        if(ObjectUtils.isEmpty(measureProjects)|| ObjectUtils.isEmpty(ssMeasureProjects)){
            return;
        }

        // 重新排列审定数据
        let billMap = new Map();
        let detailItem = await this.filterQdData(detailId, measureProjects, billMap);


        // 重新排列送审数据
        let sshBillMap = new Map();
        let sshDetailItem =await this.filterQdData(matchingId, ssMeasureProjects, sshBillMap);

        if(ObjectUtils.isEmpty(detailItem) || ObjectUtils.isEmpty(sshDetailItem)){
            return ResponseData.fail('清单或定额数据不存在');
        }
        if(detailItem.kind != sshDetailItem.kind || (detailItem.kind != StepItemCostLevelConstant.de && detailItem.kind != StepItemCostLevelConstant.qd)){
            return ResponseData.fail('替换清单或定额类型不一致');
        }
        if(detailItem.kind === StepItemCostLevelConstant.de){
            let billItem  = measureProjects.find(k=>k.sequenceNbr == detailItem.parentId);
            if(sshDetailItem.parentId !== billItem.ysshGlId){
                return ResponseData.fail('不允许跨清单匹配定额');
            }
        }

        if(detailItem.kind === StepItemCostLevelConstant.qd){
            let detailParentItem = measureProjects.find(item=>item.sequenceNbr == detailItem.parentId);
            let sshParentItem = ssMeasureProjects.find(item=>item.sequenceNbr == sshDetailItem.parentId);

            if(detailParentItem.constructionMeasureType != sshParentItem.constructionMeasureType){
                return ResponseData.fail('不允许跨标题匹配')
            }
        }
        //清空送审关系
        measureProjects.forEach(item =>{
            if(item.ysshGlId===sshDetailItem.sequenceNbr){
                item.ysshGlId = null;
                item.ysshSingleId = null;
                //不允许跨清单匹配定额，所有此处理不考虑跨清单的情况
                if(detailItem.kind === StepItemCostLevelConstant.qd){
                    let dingEE  = billMap.get(item.sequenceNbr);
                    if(ObjectUtils.isNotEmpty(dingEE)){
                        dingEE.forEach(dingEEitem => {
                            dingEEitem.ysshGlId = null;
                            dingEEitem.ysshSingleId = null;
                        });
                    }
                }
            }
        });
        //重新匹配
        detailItem.ysshGlId = sshDetailItem.sequenceNbr;
        //清单匹配需要把定额也匹配
        if(detailItem.kind === StepItemCostLevelConstant.qd){

            //清空送审关系
            let dingEE  = billMap.get(detailItem.sequenceNbr);
            if(ObjectUtils.isNotEmpty(dingEE)){
                dingEE.forEach(item => {
                    item.ysshGlId = null;
                    item.ysshSingleId = null;
                });
            }
            //清单下的定额匹配
            let sshDingEE = sshBillMap.get(sshDetailItem.sequenceNbr);
            if(ObjectUtils.isNotEmpty(dingEE) && ObjectUtils.isNotEmpty(sshDingEE)){
                dingEE.forEach(item=>{
                    let sshItem = sshDingEE.find(sshItem=>{
                        let itemName = ObjectUtils.isNotEmpty(item.orhName)?item.orhName:item.name;
                        let sshItemName = ObjectUtils.isNotEmpty(sshItem.orhName)?sshItem.orhName:sshItem.name;

                        if(ObjectUtils.isEmpty(item.fxCode) || ObjectUtils.isEmpty(sshItem.fxCode)
                            || ObjectUtils.isEmpty(itemName) || ObjectUtils.isEmpty(sshItemName)){
                            return false;
                        }
                        if(ObjectUtils.compareStringsIgnoringSpaces(item.fxCode,sshItem.fxCode)
                            && ObjectUtils.compareStringsIgnoringSpaces(itemName,sshItemName)
                            && ((ObjectUtils.isEmpty(item.unit) && ObjectUtils.isEmpty(sshItem.unit)) || ObjectUtils.compareStringsIgnoringSpaces(item.unit,sshItem.unit))){
                            return true;
                        }
                        return false;
                    });
                    sshItem?item.ysshGlId = sshItem.sequenceNbr:null;
                });
            }
        }
        return ResponseData.success();
    }
    /**
     * 查找数据
     * @param args:ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId
     * @param isDisplayAllData  表示查询的是全量数据  而非 displaySign 状态为open的数据
     * @returns {*}
     */
    async listSearch(args) {
        //请求参数   type:重点项过滤
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag, type,isDisplayAllData} = args;
        //进行数据比对
        let resultList = await this.getCompareResultList(this.getSearchIds(ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag, type),isDisplayAllData);

        //获取 审定 对应单位工程的 重点项过滤设置
        let zdxgl = await this.service.shenHeYuSuanProject.ysshZdxglService.getUnitZdxglData(args);

        //重点项过滤处理
        if (!ObjectUtils.isEmpty(zdxgl)){

            resultList = await this.service.shenHeYuSuanProject.ysshFbfxService.focusFiltering(constructId, singleId, unitId,resultList);
        }

        //处理标题 增减金额增减比例
        if(ObjectUtils.isNotEmpty(resultList)){
            for (let i = 0; i < resultList.length; i++) {
                if(resultList[i].kind ==StepItemCostLevelConstant.bt || resultList[i].kind ==StepItemCostLevelConstant.top || resultList[i].kind ==StepItemCostLevelConstant.zx ){
                    resultList[i].ysshSysj.changeTotal =NumberUtil.numberScale2( NumberUtil.subtract(resultList[i].total,resultList[i].ysshSysj.total)) //审定减送审
                    resultList[i].ysshSysj.changeRatio = await this._calculateDiffTotalRate(resultList[i].ysshSysj.changeTotal, resultList[i].ysshSysj.total);
                }

            }

        }
        return resultList;
    }

    /**
     * 重新排序
     * @param resultList
     */
    async reSortResultList(resultList){
        if(ObjectUtils.isEmpty(resultList)){
            return resultList;
        }
        let sortResultList = [];
        let top = resultList.find(item => item.kind === StepItemCostLevelConstant.top);
        sortResultList.push(top);
        let btList = resultList.filter(item => item.kind === StepItemCostLevelConstant.bt).sort((a, b) => this._sortCompare(a, b));
        for (let i = 0; i < btList.length; i++) {
            const bt = btList[i];
            sortResultList.push(bt);
            //清单数据
            let qdList =await this._getChildren(bt.sequenceNbr, resultList);
            for (let j = 0; j < qdList.length; j++) {
                const qd = qdList[j];
                sortResultList.push(qd);
                //定额数据
                let deList =await this._getChildren(qd.sequenceNbr, resultList);
                for (let k = 0; k < deList.length; k++) {
                    const de = deList[k];
                    sortResultList.push(de);
                }
            }
        }
        //新增措施部分
        let sequenceNbr = Snowflake.nextId()
        let newCsBfParent ={
            "sequenceNbr": sequenceNbr,
            "kind": StepItemCostLevelConstant.bt,
            "name": '新增措施分部',
            "itemCategory": this.service.stepItemCostService.DEFAULT_MEASURE_TYPES[2],
            "constructionMeasureType": this.service.stepItemCostService.DEFAULT_MEASURE_TYPES_ENUM[this.service.stepItemCostService.DEFAULT_MEASURE_TYPES[2]],
            "measureType": "3",
            "ysshSysj":{}
        }

        // 增删改状态：change：0 正常，1：增项 2：删项 3：改项
        let delArray=[];//删项
        delArray.push(newCsBfParent)
        let rightArray=[];//非删项
        let awfSequenceNbr ; //安稳费主键
        let delQdSequenceNbr =''; //删项清单主键
        for (let i = 0; i < sortResultList.length; i++) {
            let item =sortResultList[i]
            if(item.constructionMeasureType== 2){
                awfSequenceNbr = item.sequenceNbr;
            }
            if(StepItemCostLevelConstant.de==item.kind){
                //定额
                if(item.parentId ==delQdSequenceNbr){
                    delete item.dispNo
                    delArray.push(item)
                }else {
                    rightArray.push(item)
                }
            }else {
                if(item.ysshSysj.change==2){
                    //删项
                    if(item.parentId==awfSequenceNbr){
                        //安稳费清单不处理删项下移
                        rightArray.push(item)
                    }else {
                        delQdSequenceNbr = item.sequenceNbr
                        item.parentId =sequenceNbr;
                        if (ObjectUtils.isEmpty(item.ysshSysj.fxCode) && ObjectUtils.isEmpty(item.ysshSysj.name) && ObjectUtils.isEmpty(item.ysshSysj.projectAttr)) {
                            item.ysshSysj.change = 0;//特殊处理送审空清单有审定没有显示到新增措施分部 切为无标示 （少雄说）
                        }
                        delete item.dispNo
                        delArray.push(item)
                    }
                }else {
                    rightArray.push(item)
                }
            }

        }

        if(delArray.length==1){
            return rightArray
        }else {
            return rightArray.concat(delArray);
        }

    }
    _getChildren(parentId, items) {
        return items.filter(item => item.parentId === parentId);
        // return items.filter(item => item.parentId === parentId).sort((a, b) => this._sortCompare(a, b));
    }

    _sortCompare(a, b) {
        let sortA = a.dispNo || "0";
        let sortB = b.dispNo || "0";
        if (sortA.length !== sortB.length) {
            return sortA.length - sortB.length;
        }
        return (sortA+'').localeCompare(sortB+'');
    }

    /**
     * 组装六个ID
     * @returns {{}}
     * @param ssConstructId
     * @param ssSingleId
     * @param ssUnitId
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr
     * @param pageNum
     * @param pageSize
     * @param isAllFlag
     */
    getSearchIds(ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId,
                 sequenceNbr, pageNum, pageSize, isAllFlag, type) {
        let searchIds = {};
        searchIds.constructId = constructId;
        searchIds.singleId = singleId;
        searchIds.unitId = unitId;
        searchIds.ssConstructId = ssConstructId;
        searchIds.ssSingleId = ssSingleId;
        searchIds.ssUnitId = ssUnitId;
        searchIds.sequenceNbr = sequenceNbr;
        searchIds.pageNum = pageNum;
        searchIds.pageSize = pageSize;
        searchIds.isAllFlag = isAllFlag;
        searchIds.type = type;
        return searchIds;
    }

    /**
     * 查询对比送审和审定数据
     * @returns {Promise<*[]>}
     * @param searchIds
     */
    async getCompareResultList(searchIds,isDisplayAllData) {
        //送审数据
        let ssCSXMPageDatas = [];
        let sdCSXMPageDatas = [];
        if (isDisplayAllData) {
            ssCSXMPageDatas = searchIds.ssUnitId ? await this.service.stepItemCostService.pageSearchForAll(searchIds.ssConstructId, searchIds.ssSingleId, searchIds.ssUnitId, searchIds.sequenceNbr, searchIds.pageNum, searchIds.pageSize, searchIds.isAllFlag) : [];
            /*审定数据*/
            sdCSXMPageDatas = searchIds.unitId ? await this.service.stepItemCostService.pageSearchForAll(searchIds.constructId, searchIds.singleId, searchIds.unitId, searchIds.sequenceNbr, searchIds.pageNum, searchIds.pageSize, searchIds.isAllFlag) : [];
        }else {
            ssCSXMPageDatas = searchIds.ssUnitId ? await this.getPageDatas(searchIds.ssConstructId, searchIds.ssSingleId, searchIds.ssUnitId, searchIds.sequenceNbr, searchIds.pageNum, searchIds.pageSize, searchIds.isAllFlag) : [];
            /*审定数据*/
            sdCSXMPageDatas = searchIds.unitId ? await this.getPageDatas(searchIds.constructId, searchIds.singleId, searchIds.unitId, searchIds.sequenceNbr, searchIds.pageNum, searchIds.pageSize, searchIds.isAllFlag) : [];
        }

        if(searchIds.unitId && searchIds.ssUnitId){
            let sdUnit = PricingFileFindUtils.getUnit(searchIds.constructId, searchIds.singleId, searchIds.unitId);
            let ssUnit = PricingFileFindUtils.getUnit(searchIds.ssConstructId, searchIds.ssSingleId, searchIds.ssUnitId);
            //如果两个单位工程数据都存在且审定绑定ID与送审ID不一致则认为此数据为送审单位工程数据（送审有，审定没有）
            if(ssUnit && sdUnit && sdUnit.ysshUnitId
                && sdUnit.ysshUnitId !== ssUnit.sequenceNbr ){
                sdCSXMPageDatas.data = [];
            }
        }

        //针对删项且重复ID重置审定数据ID
        if(ObjectUtils.isNotEmpty(sdCSXMPageDatas.data) && ObjectUtils.isNotEmpty(ssCSXMPageDatas.data)){
            await this.resetSdIds(sdCSXMPageDatas.data, ssCSXMPageDatas.data, searchIds);
        }

        //审核结果集
        return await this.compareCSXMUnitData(ssCSXMPageDatas.data, sdCSXMPageDatas.data, searchIds);
    }

    /**
     * 调用预算获取措施项数据，避免更改结构
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr
     * @param pageNum
     * @param pageSize
     * @param isAllFlag
     * @returns {*}
     */
    async getPageDatas(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag) {
        return this.service.stepItemCostService.pageSearch(constructId, singleId, unitId, sequenceNbr, pageNum, pageSize, isAllFlag);
    }

    /**
     * 单位工程级别数据比对，只比较清单和定额
     * kind: 0:顶级数据 01：分部；02 子分部 03：清单；04：定额
     * constructionMeasureType：标题类别存数字 1单价措施 2安文费 3其他总价措施
     * @param ssCSXMUnitDatas 送审单位工程数据
     * @param sdCSXMUnitDatas 审定单位工程数据
     * @param searchIds
     * @returns {Promise<*[]>}
     */
    async compareCSXMUnitData(ssCSXMUnitDatas, sdCSXMUnitDatas, searchIds){
        //返回结果集对象
        let resultList = [];
        //以审定数据为基准比较，记录数据状态
        if (ObjectUtils.isNotEmpty(sdCSXMUnitDatas)) {
            /*
                子数据为 分类数据、清单数据、定额数据
             */
            for (let i = 0; i < sdCSXMUnitDatas.length; i++) {
                let sdItemData = sdCSXMUnitDatas[i];
                //审核结果数据
                let shjgData = {};
                /*
                YsshssConstant
                增删改状态：change：0 正常，1：增项 2：删项 3：改项
                工程量差：changeQuantity
                增减金额（综合合价）：changeTotal
                增减比例：changeRatio
                增减说明：changeExplain：[调量]、[调价]、[调量，调价]、[增项]、[删项]、其它（手动录入）
                 */
                //先初始化字段
                shjgData[YsshssConstant.change] = YsshssConstant.noChange;//未更改

                /*
                    只处理清单和定额数据
                 */
                if (sdItemData.kind === BranchProjectLevelConstant.qd || sdItemData.kind ===BranchProjectLevelConstant.de) {
                    shjgData[YsshssConstant.changeQuantity] = 0;//增减工程量
                    shjgData[YsshssConstant.changeTotal] = 0;//增减金额
                    shjgData[YsshssConstant.changeRatio] = 0;//增减比例
                    shjgData[YsshssConstant.changePrice] = 0;//增减单价
                    shjgData[YsshssConstant.changeExplain] = " ";//增减说明

                    if (ObjectUtils.isEmpty(sdItemData.ysshGlId)) {
                        //未绑定送审数据且审定数据total不为空或0为：增项
                        await this.compareQdResultAdd(sdItemData, shjgData, null);
                        //审定为正常项
                        if (ObjectUtils.isEmpty(sdItemData.total) || sdItemData.total === 0) {
                            shjgData[YsshssConstant.change] = YsshssConstant.noChange;//未更改
                            shjgData[YsshssConstant.changeQuantity] = 0;//增减工程量
                            shjgData[YsshssConstant.changeTotal] = 0;//增减金额
                            shjgData[YsshssConstant.changeRatio] = 0;//增减比例
                            shjgData[YsshssConstant.changePrice] = 0;//增减单价
                            shjgData[YsshssConstant.changeExplain] = " ";//增减说明
                        }
                    } else {
                        //审定综合合价
                        const sdTotal = sdItemData.total;
                        //送审数据中查找对应数据
                        let ssItemData = ssCSXMUnitDatas ? ssCSXMUnitDatas.find(ssQdData => ssQdData.sequenceNbr === sdItemData.ysshGlId) : null;
                        if (ObjectUtils.isNotEmpty(ssItemData)) {
                            //送审综合合价
                            const ssTotal = ssItemData.total;
                            /*
                                增项：送审综合合价为0，审定综合合价不为0;
                             */
                            if ((ObjectUtils.isEmpty(ssTotal) || ssTotal === 0) && ObjectUtils.isNotEmpty(sdTotal) && sdTotal !== 0) {
                                //增项
                                await this.compareQdResultAdd(sdItemData, shjgData, ssItemData);
                            } else if ((ObjectUtils.isEmpty(sdTotal) || sdTotal === 0) && ObjectUtils.isNotEmpty(ssTotal) && ssTotal !== 0) {
                                /*
                                    删项：送审综合合价不为0，审定为空或综合合价为0;
                                        在送审数据循环时处理，此处跳过不做处理;
                                 */
                                continue;
                            } else {
                                //改项数据对比
                                await this.compareQdResultChange(ssItemData, sdItemData, searchIds, shjgData);
                                //设置字段颜色
                                await this.initColorArray(sdItemData, ssItemData, shjgData);
                            }
                        } else if(sdTotal !== 0 ){
                            //送审数据为空且审定综合合价不为0：增项
                            await this.compareQdResultAdd(sdItemData, shjgData);
                        }
                    }

                }else{
                    //分部数据内容
                    //对比匹配处只对清单和定额做了关联关系，因此此处需自己匹配：kind、name、constructionMeasureType、type
                    let ssItemData =await  this.getSsItemData(sdItemData, ssCSXMUnitDatas);
                    await this.syncUpdateShjgData(shjgData, ssItemData);
                }
                //将比对结果装入结果集
                resultList.push(this.getYsshObject(shjgData, sdItemData));
            }
        }

        /*
            计算删项数据:
         */
        let needReSort = false;//是否需要重新排序
        if (ObjectUtils.isNotEmpty(ssCSXMUnitDatas)) {
            //前端查询列表type=1在删项数据审定为空时，界面按送审数据显示
            const type = searchIds.type || 0;
            for (let i = 0; i < ssCSXMUnitDatas.length; i++) {
                let ssItemData = ssCSXMUnitDatas[i];//送审数据
                if(ObjectUtils.isEmpty(sdCSXMUnitDatas)){
                    //送审综合合价
                    const ssTotal = ssItemData.total;
                    //删项
                    if(ObjectUtils.isNotEmpty(ssTotal) && ssTotal !== 0
                        && (ssItemData.kind === BranchProjectLevelConstant.qd
                            || ssItemData.kind === BranchProjectLevelConstant.de)){
                        let sdItemData = null;
                        await this.compareQdResultDelete(resultList, ssItemData, sdItemData, ssCSXMUnitDatas, type);
                    }else{
                        //审定不存在，送审综合合价为空或0的数据，为正常项
                        await this.compareQdResultForSsTotalIsZero(resultList, ssItemData, ssCSXMUnitDatas, type);
                    }
                }
                //只处理清单和定额数据
                else if (ssItemData.kind ===BranchProjectLevelConstant.qd || ssItemData.kind ===BranchProjectLevelConstant.de) {
                    //送审综合合价
                    const ssTotal = ssItemData.total;
                    //未在审定数据中找到为删项
                    const sdItemData = sdCSXMUnitDatas ? sdCSXMUnitDatas.find(sdItem => sdItem.ysshGlId === ssItemData.sequenceNbr) : null;
                    if(ObjectUtils.isEmpty(sdItemData)){
                        //审定数据为空则需重新排序
                        needReSort = true;
                    }
                    if((ObjectUtils.isEmpty(ssTotal) || ssTotal === 0) && ObjectUtils.isEmpty(sdItemData)){
                        //审定数据为空，且送审综合合价为空或0，为正常数据
                        await this.compareQdResultForSsTotalIsZero(resultList, ssItemData, ssCSXMUnitDatas, type);
                    }
                    else if (ObjectUtils.isNotEmpty(ssTotal) && ssTotal !== 0 //送审有数据且综合合价不为0
                            && (ObjectUtils.isEmpty(sdItemData) || ObjectUtils.isEmpty(sdItemData.total) || sdItemData.total === 0)) {//绑定的审定数据综合合价为0
                        //删项
                        await this.compareQdResultDelete(resultList, ssItemData, sdItemData, ssCSXMUnitDatas, type);
                    }
                }
            }
        }


        // if(needReSort){
            //对数据重新排序
        resultList = await this.reSortResultList(resultList);
        // }
        return resultList;
    }

    /**
     * 组装审核结果数据
     * @param shjgData
     * @param sdItemData
     * @returns {any}
     */
    getYsshObject(shjgData, sdItemData){
        //深度拷贝不影响原数据
        let copySdItemData = ConvertUtil.deepCopy(sdItemData);
        //增减说明
        if(copySdItemData && copySdItemData.changeExplain){
            shjgData[YsshssConstant.changeExplain] = copySdItemData.changeExplain;
        }
        //审定数据是否为空标识
        if(!copySdItemData.isEmpty){
            copySdItemData.isEmpty = false;
        }
        return Object.assign({[YsshssConstant.ysshSysj]:shjgData}, copySdItemData);
    }

    /**
     * 改项数据比对，只比较清单和定额
     *
     * 此处先不考虑已绑定数据综合合价改为0的情况
     *
     * kind: 0:顶级数据 01：分部；02 子分部 03：清单；04：定额
     * constructionMeasureType：标题类别存数字 1单价措施 2安文费 3其他总价措施
     * @param ssItemData 审定数据
     * @param sdItemData 送审数据
     * @param searchIds 送审、审定关键数据ID用户人材机处理
     * @param shjgData 比对结果数据
     */
    async compareQdResultChange(ssItemData, sdItemData, searchIds, shjgData){
        sdItemData.ysshGlId = ssItemData.sequenceNbr;//绑定送审数据Id
        //同步送审数据到审核结果数据
        this.syncUpdateShjgData(shjgData, ssItemData);
        shjgData[YsshssConstant.change] = YsshssConstant.noChange;//先重置为正常
        shjgData[YsshssConstant.changeExplain] = " ";//增减说明重置
        /*
            分类数据统一为正常；
            先对对象数据中的清单数据做对比；
            如是定额数据则对比完定额数据后，再对比其人材机，换算等数据，并同步修改上级数据；
            03：清单  04；定额
         */
        /*
            YsshssConstant
            增删改状态：change：0 正常，1：增项 2：删项 3：改项
            工程量差：changeQuantity
            增减金额（综合合价）：changeTotal
            增减比例：changeRatio
            增减说明：changeExplain：[调量]、[调价]、[调量，调价]、[增项]、[删项]、其它（手动录入）
         */
        if(sdItemData.kind === StepItemCostLevelConstant.qd
            || sdItemData.kind === StepItemCostLevelConstant.de){
            //清单数据:使用NumberUtil计算
            //3、单价差 审定-送审
            const changePrice = NumberUtil.subtract(sdItemData.price, ssItemData.price);
            //1、调量：计算工程量量差：审定-送审
            const changeQuantity = NumberUtil.subtract(sdItemData.quantity, ssItemData.quantity);
            //2、调价：计算综合合价增减金额：审定-送审；计算综合合价增减比例：增减金额/送审金额
            const changeTotal = NumberUtil.subtract(sdItemData.total, ssItemData.total);
            const changeRatio = this._calculateDiffTotalRate(changeTotal, ssItemData.total);

            // if(changeQuantity !== 0 || changePrice !== 0){//工程量差 + 综合单价差
            // if(changeQuantity !== 0 || changeTotal !== 0){//工程量差 + 综合合价差
            if(changeTotal !== 0 || changeQuantity !== 0 || changeTotal !== 0){//
                shjgData[YsshssConstant.change] = YsshssConstant.update;//修改状态为改项
                //获取增减说明
                const changeExplain = this._getDiffReason(changeQuantity, changePrice);
                shjgData[YsshssConstant.changeExplain] = changeExplain;
            }
            shjgData[YsshssConstant.changePrice] = changePrice;
            shjgData[YsshssConstant.changeQuantity] = changeQuantity;
            shjgData[YsshssConstant.changeTotal] = changeTotal;
            shjgData[YsshssConstant.changeRatio] = changeRatio;
        }
    }

    /**
     * 设置【增项】字段数据
     * @param sdUnitData
     * @param shjgData 审核结果数据
     * @param ssItemData
     */
    async compareQdResultAdd(sdUnitData, shjgData, ssItemData){
        //审核结果数据
        this.syncUpdateShjgData(shjgData, ssItemData);
        // this.syncUpdateShjgData(shjgData, null);
        shjgData[YsshssConstant.change]= YsshssConstant.insert;
        if(sdUnitData.kind === BranchProjectLevelConstant.qd
            || sdUnitData.kind === BranchProjectLevelConstant.de){
            shjgData[YsshssConstant.changeQuantity] = sdUnitData.quantity;
            shjgData[YsshssConstant.changeTotal] = sdUnitData.total;
            shjgData[YsshssConstant.changeRatio] = 100;
            shjgData[YsshssConstant.changePrice] = sdUnitData.price;
            shjgData[YsshssConstant.changeExplain]= YsshssConstant.insertExplain;
        }
        //设置字段颜色
        await this.initColorArray(sdUnitData, ssItemData, shjgData);
    }

    /**
     * 送审数据综合合价为空或0且审定数据为空数据处理
     * @param resultList
     * @param ssItemData
     * @param ssCSXMUnitDatas
     */
    async compareQdResultForSsTotalIsZero(resultList, ssItemData, ssCSXMUnitDatas, type){
        if(!ssItemData){
            return;
        }
        //审核结果数据
        let shjgData = {};
        shjgData[YsshssConstant.change] = YsshssConstant.noChange;//未更改
        if(ssItemData.kind === BranchProjectLevelConstant.qd
            || ssItemData.kind === BranchProjectLevelConstant.de ){
            shjgData[YsshssConstant.changeQuantity] = 0;//增减工程量
            shjgData[YsshssConstant.changeTotal] = 0;//增减金额
            shjgData[YsshssConstant.changeRatio] = 0;//增减比例
            shjgData[YsshssConstant.changePrice] = 0;//增减单价
            shjgData[YsshssConstant.changeExplain] = " ";//增减说明
        }
        //同步送审数据到审核数据
        await this.syncUpdateShjgData(shjgData, ssItemData);
        //复制审定数据
        let sdItemData =await this.createSdDataFromSdData(ssItemData, ssCSXMUnitDatas, resultList, type);
        //将比对结果装入结果集
        resultList.push(this.getYsshObject(shjgData, sdItemData));
    }

    /**
     * 处理【删项】数据
     * @param resultList 审定数据集合
     * @param ssItemData 送审数据：转为审定数据并添加进审定数据集合
     * @param sdItemData 用于人才加处理使用六个ID
     * @param ssCSXMUnitDatas 送审数据集合
     * @param sdCSXMUnitDatas 审定数据集合
     */
    async compareQdResultDelete(resultList, ssItemData, sdItemData, ssCSXMUnitDatas, type){
        if(!ssItemData){
            return;
        }
        //审核结果数据
        let shjgData =await this.getDeleteShjgData(ssItemData);
        //审定数据
        if(ObjectUtils.isEmpty(sdItemData)){
            sdItemData =await this.createSdDataFromSdData(ssItemData, ssCSXMUnitDatas, resultList, type);
        }
        //设置字段颜色
        await this.initColorArray(sdItemData, ssItemData, shjgData);
        //将比对结果装入结果集
        resultList.push(this.getYsshObject(shjgData, sdItemData));
    }

    /**
     * 审定数据为空复制送审数据到审定数据
     * @param ssItemData
     * @param ssCSXMUnitDatas
     * @param resultList
     */
    async createSdDataFromSdData(ssItemData, ssCSXMUnitDatas, resultList, type){
        let sdItemData = {};
        sdItemData.isEmpty = true;//数据为空标识
        if(ObjectUtils.isNotEmpty(type) && type === 1){
            sdItemData.type = ssItemData.type;
            sdItemData.fxCode = ssItemData.fxCode;
            sdItemData.name = ssItemData.name;
            sdItemData.blackArray = ssItemData.blackArray;
            sdItemData.redArray = ssItemData.redArray;
        }
        sdItemData.sequenceNbr = ssItemData.sequenceNbr;
        sdItemData.kind = ssItemData.kind;
        sdItemData.dispNo = ssItemData.dispNo;//用于删项重排
        sdItemData.displayStatu = ssItemData.displayStatu;
        sdItemData.displaySign = ssItemData.displaySign;
        sdItemData.optionMenu = [];

        //特殊处理parentId
        const parentId =await this.getParentId(ssItemData, ssCSXMUnitDatas, resultList);
        sdItemData.parentId = parentId;
        return sdItemData;
    }

    /**
     * 获取【删项】数据
     * @param ssItemData
     * @returns {{}}
     */
    async getDeleteShjgData(ssItemData) {
        let shjgData = {};
        shjgData[YsshssConstant.change] = YsshssConstant.delete;//删项
        if (ssItemData.kind === BranchProjectLevelConstant.qd
            || ssItemData.kind === BranchProjectLevelConstant.de) {
            shjgData[YsshssConstant.changeQuantity] = -ssItemData.quantity;
            shjgData[YsshssConstant.changeTotal] = -ssItemData.total;
            shjgData[YsshssConstant.changeRatio] = -100;
            shjgData[YsshssConstant.changePrice] = -ssItemData.price;
            shjgData[YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
            shjgData['type'] = ssItemData.type;
            shjgData['optionMenu'] = ssItemData.optionMenu;
        }
        //同步送审数据到审核数据
        await this.syncUpdateShjgData(shjgData, ssItemData);
        return shjgData;
    }

    /**
     * 同步信息到审核结果数据
     * @param shjgData
     * @param itemData
     */
    syncUpdateShjgData(shjgData, itemData){
        //序号，编码,名称,单位,项目特征,工程量表达式，工程量,综合单价,综合合价, 单价，合价，主键
        if(ObjectUtils.isNotEmpty(itemData)){
            const {dispNo, fxCode, name, kind, unit, projectAttr, quantityVariableName, quantity,
                price, total, zjfPrice, zjfTotal,sequenceNbr, blackArray, redArray,optionMenu} = itemData;
            shjgData.dispNo = dispNo;
            shjgData.fxCode = fxCode;
            shjgData.name = name;
            shjgData.kind = kind;
            shjgData.unit = unit;
            shjgData.projectAttr = projectAttr;
            shjgData.quantityVariableName = quantityVariableName;
            shjgData.quantity = quantity;
            shjgData.price = price;
            shjgData.total = total;
            shjgData.zjfPrice = zjfPrice;//单价
            shjgData.zjfTotal = zjfTotal;//合价
            shjgData.sequenceNbr = sequenceNbr;//主键
            shjgData.blackArray = blackArray;
            shjgData.redArray = redArray;
            shjgData.optionMenu = optionMenu;
        }else{
            shjgData.kind = null;
            shjgData.dispNo = null;
            shjgData.fxCode = null;
            shjgData.name = null;
            shjgData.unit = null;
            shjgData.projectAttr = null;
            shjgData.quantityVariableName = null;
            shjgData.quantity = null;
            shjgData.price = null;
            shjgData.total = null;
            shjgData.zjfPrice = null;//单价
            shjgData.zjfTotal = null;//合价
            shjgData.sequenceNbr = null;//主键
            shjgData.blackArray = [];
            shjgData.redArray = [];
        }
    }

    /**
     * 获取增减说明
     * @param changeQuantity
     * @param changePrice
     */
    _getDiffReason(changeQuantity, changePrice){
        let diffReason = "";
        if(changeQuantity !== 0 && changePrice !== 0 ){
            diffReason = "[调量，调价]";
        }else if(changeQuantity !== 0 && changePrice === 0 ){
            diffReason = YsshssConstant.changeNumExplain;
        }else if(changeQuantity === 0 && changePrice !== 0 ){
            diffReason = YsshssConstant.changePriceExplain;
        }
        return diffReason;
    }

    /**
     * 计算百分比
     * @param diffTotal
     * @param ssTotal 送审
     */
    _calculateDiffTotalRate(diffTotal, ssTotal){
        let diffTotalRate = 100;
        //为0 无价差
        if(diffTotal === 0){
            diffTotalRate = 0;
        }else if(ssTotal === 0){
            diffTotalRate = 100;
        }else{
            diffTotalRate = NumberUtil.divide(diffTotal, ssTotal);
            //百分比 *100且保留两位小数
            diffTotalRate = NumberUtil.numberScale2(NumberUtil.multiply(diffTotalRate, 100));
        }
        return diffTotalRate;
    }


//----------------数据转换-----------------------------------------------------------------

    /**
     * 送审转审定
     * 【增项】不做处理
     * @returns {Promise<void>}
     * @param args
     */
    async ssToSdBatch(args){
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, shDataList} = args;
        if(ObjectUtils.isEmpty(shDataList)){return}
        //送审单位工程数据
        let sdUnitCSXMDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        for (let i = 0; i < shDataList.length; i++) {
            const shData = shDataList[i];
            //比对结果
            const shChange = shData[YsshssConstant.change]
            if(shChange === YsshssConstant.insert){
                continue;
            }
            //送审数据
            const ssData = this.getCSXMData(shData.ysshGlId, ssConstructId, ssSingleId, ssUnitId);
            //审定数据
            let sdData = shData.sequenceNbr ? sdUnitCSXMDatas.find(item => item.sequenceNbr === shData.sequenceNbr) : {};
            //数据转换
            this.saveSdData(sdData, ssData, shChange, sdUnitCSXMDatas);
        }
        //处理【删项】转换数据
        this.dealSdUnitCSXMDatasForDeleteIds(sdUnitCSXMDatas);
    }

    /**
     * 处理【删项】转换数据
     * @param sdUnitCSXMDatas
     */
    dealSdUnitCSXMDatasForDeleteIds(sdUnitCSXMDatas){
        if(!sdUnitCSXMDatas){
            return;
        }
        //[YsshssConstant.change] 不为空代表是【删项】送审到审定数据
        let deleteSdDatas = sdUnitCSXMDatas.filter(item => !ObjectUtils.isEmpty(item.changeFlag));
        //[YsshssConstant.change] 为空代表原有数据
        const ownSdDatas = sdUnitCSXMDatas.filter(item => ObjectUtils.isEmpty(item.changeFlag));
        //遍历数据校验id是否已存在，存在则需更改ID
        for (let i = 0; i < deleteSdDatas.length; i++) {
            const deleteSdData = deleteSdDatas[i];
            if(ownSdDatas.find(s => s.sequenceNbr === deleteSdData.sequenceNbr)){
                //新生成ID,此ID原数据中肯定不存在
                const newId = Snowflake.nextId();
                //查询子集数据并修改parentId
                deleteSdDatas.filter(i => i.parentId === deleteSdData.sequenceNbr)
                    .forEach(item => {
                        item.parentId = newId;
                    });
                //工程量明细
                if(deleteSdData.quantities){
                    deleteSdData.quantities.forEach(item => {
                        item.quotaListId = newId;
                    });
                }
                //更换主数据ID
                deleteSdData.sequenceNbr = newId;
                delete deleteSdData.changeFlag;//
            }
        }
    }


    /**
     * 送审转审定
     * @param sdData
     * @param ssData
     * @param shChange
     * @param sdUnitCSXMDatas
     */
    saveSdData(sdData, ssData, shChange, sdUnitCSXMDatas){
        if(!ssData || !sdData){
            return
        }
        //不复制字段
        const ignoreColumns = ['sequenceNbr', 'parentId', 'quantities', 'quotaListId'];
        //正常、改项数据直接赋值
        if(shChange === YsshssConstant.noChange || shChange === YsshssConstant.update){
            //直接修改内润数据
            sdData = ConvertUtil.deepCopyIgnore(ssData, sdData, ignoreColumns);
            //处理工程量明细
            let quantities = ConvertUtil.deepCopy(ssData.quantities);
            if(quantities){
                //修改工程量明细quotaListId为当前数据ID
                quantities.forEach(item => {
                    item.quotaListId = sdData.sequenceNbr;
                    item.ysshGlId = item.sequenceNbr;//绑定ID
                });
                sdData.quantities = quantities;
                sdData.ysshGlId = ssData.sequenceNbr;//绑定ID
            }
        }else if(shChange === YsshssConstant.delete){
            /*
                【删项】送审有，审定没有的数据
                先深度拷贝，处理完全部数据后再处理id重复问题
             */
            sdData = ConvertUtil.deepCopy(ssData);
            sdData.ysshGlId = ssData.sequenceNbr;
            //标记一下是否是【删项】送审数据
            sdData.changeFlag = 'flag';
            sdUnitCSXMDatas.push(sdData);
        }
    }


    /**
     * 审定转送审
     * 修改内容中的送审数据：根据选中的审核数据，去除对应的审定数据，同步到绑定的送审数据中
     * 注：【减项】数据无此操作
     * @param args
     * @returns {Promise<void>}
     */
    async sdToSsBatch(args){
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, shDataList} = args;
        if(ObjectUtils.isEmpty(shDataList)){return}
        //送审单位工程数据
        let ssUnitCSXMDatas = PricingFileFindUtils.getCSXM(ssConstructId, ssSingleId, ssUnitId);
        for (let i = 0; i < shDataList.length; i++) {
            const shData = shDataList[i];
            //比对结果
            const shChange = shData[YsshssConstant.change]
            if(shChange === YsshssConstant.delete){
                continue;
            }
            //去除对应清单数据，同步对象
            const sdData = this.getCSXMData(shData.sequenceNbr, constructId, singleId, unitId);
            //送审措施清单项目数据
            let ssData = sdData.ysshGlId ? ssUnitCSXMDatas.find(item => item.sequenceNbr === sdData.ysshGlId) : {} ;
            //保存送审数据
            this.saveSsData(ssData, sdData, shChange, ssUnitCSXMDatas);
        }
        //处理【增项】转换数据
        this.dealSsUnitCSXMDatasForInsertIds(ssUnitCSXMDatas);
    }

    /**
     * 处理【增项】数据
     * 这类数据直接添加到了ssUnitCSXMDatas中 且携带 ysshGlId
     * @param ssUnitCSXMDatas
     */
    dealSsUnitCSXMDatasForInsertIds(ssUnitCSXMDatas){
        if(!ssUnitCSXMDatas){
            return;
        }
        //ysshGlId 不为空代表是【增项】审定到送审的数据
        let insertSsDatas = ssUnitCSXMDatas.filter(item => !ObjectUtils.isEmpty(item.ysshGlId));
        //ysshGlId 为空代表原数据
        const ownSSDatas = ssUnitCSXMDatas.filter(item => ObjectUtils.isEmpty(item.ysshGlId));
        //遍历数据校验id是否已存在，存在则需更改ID
        for (let i = 0; i < insertSsDatas.length; i++) {
            const insertSsData = insertSsDatas[i];
            if(ownSSDatas.find(s => s.sequenceNbr === insertSsData.sequenceNbr)){
                //新生成ID,此ID原数据中肯定不存在
                const newId = Snowflake.nextId();
                //查询子集数据并修改parentId
                insertSsDatas.filter(i => i.parentId === insertSsData.sequenceNbr)
                    .forEach(item => {
                        item.parentId = newId;
                    });
                //工程量明细
                if(insertSsData.quantities){
                    insertSsData.quantities.forEach(item => {
                        item.quotaListId = newId;
                        delete item.ysshGlId;//删除绑定ID
                    });
                }
                delete insertSsData.ysshGlId;
                //更换主数据ID
                insertSsData.sequenceNbr = newId;
            }
        }
    }


    /**
     * 保存送审数据
     * @param ssData
     * @param sdData
     * @param shChange 审核结果
     * @param ssUnitCSXMDatas 送审单位工程措施项目数据
     */
    saveSsData(ssData, sdData, shChange, ssUnitCSXMDatas){
        if(!ssData || !sdData){
            return
        }
        //不复制字段：
        const ignoreColumns = ['sequenceNbr', 'parentId', 'quantities', 'quotaListId', 'ysshGlId'];
        //正常、改项数据直接赋值
        if(shChange === YsshssConstant.noChange || shChange === YsshssConstant.update){
            //直接修改内润数据
            ssData = ConvertUtil.deepCopyIgnore(sdData, ssData, ignoreColumns);
            //处理工程量明细
            let quantities = ConvertUtil.deepCopy(sdData.quantities);
            if(quantities){
                //修改工程量明细quotaListId为当前数据ID
                quantities.forEach(item => {
                    item.quotaListId = ssData.sequenceNbr;
                    delete item.ysshGlId;//删除绑定ID
                });
                ssData.quantities = quantities;
            }
        }else if(shChange === YsshssConstant.insert){
            /*
                【增项】审定有，送审没有的数据
                先深度拷贝，处理完全部数据后再处理id重复问题
             */
            ssData = ConvertUtil.deepCopy(sdData);
            ssUnitCSXMDatas.push(ssData);
        }

    }

    /**
     *  获取措施项目清单数据
     * @param sequenceNbr
     * @param constructId
     * @param singleId
     * @param unitId
     */
    getCSXMData(sequenceNbr, constructId, singleId, unitId){
        let csxmData = {};
        const unitCsXMDatas = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        if(!unitCsXMDatas){
            csxmData = unitCsXMDatas.find(i => i.sequenceNbr === sequenceNbr);
        }
        return csxmData;
    }

    /**
     * 【删项】获取父级ID
     * 1、清单数据：获取父级数据类型，查找对应的审核分类数据ID作为parentId;
     * 2、定额数据：获取父级清单数据，根据父级清单数据在审核数据中查看绑定的清单数据，有则已审核清单数据ID作为parentId，无则以原清单数据ID作为parentId;
     * @param ssItemData
     * @param ssCSXMUnitDatas
     * @param resultList 审核数据
     * @returns {undefined}
     */
    async getParentId(ssItemData, ssCSXMUnitDatas, resultList) {
        //默认原父级ID
        let parentId = ssItemData.parentId;
        if(ssItemData.kind === StepItemCostLevelConstant.qd){
            //父级分类数据
            const parentData = ssCSXMUnitDatas.find(item => item.sequenceNbr === ssItemData.parentId);
            if(parentData && parentData.kind === StepItemCostLevelConstant.bt && ObjectUtils.isNotEmpty(resultList)){
                //constructionMeasureType  标题行且措施类型相同
                const shParentData = resultList.find(item => item.kind === StepItemCostLevelConstant.bt
                    && item.constructionMeasureType === parentData.constructionMeasureType);
                if(shParentData){
                    parentId = shParentData.sequenceNbr;
                }
            }
        }else if(ssItemData.kind === StepItemCostLevelConstant.de){
            //父级清单数据
            const parentData = ssCSXMUnitDatas.find(item => item.sequenceNbr === ssItemData.parentId);
            if(parentData){
                //绑定的清单数据
                const shParentData = ObjectUtils.isNotEmpty(resultList) ? resultList.find(item => item.ysshGlId === parentData.sequenceNbr) : {};
                if(shParentData){
                    //有绑定数据则以绑定清单数据ID
                    parentId = shParentData.sequenceNbr;
                }
            }
        }
        return parentId;
    }


    /**
     * 打开节点
     * @param args
     * @return {Promise<boolean>}
     */
    async open(args){
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, pointLine} = args;
        // args.sequenceNbr = null;
        // args.pageNum = 1;
        // args.pageSize = 300000;
        // args.isAllFlag = false;

        if(ObjectUtils.isNotEmpty(unitId)){
            this.service.shenHeYuSuanProject.stepItemCostService.open(constructId, singleId, unitId, pointLine)
        }
        if(ObjectUtils.isNotEmpty(ssUnitId)){
            let ssUnitCsXMDatas = await this.getPageDatas(ssConstructId, ssSingleId, ssUnitId, null, 1, 300000, false);
            //获取行对应的送审数据
            if((pointLine.kind === StepItemCostLevelConstant.qd || pointLine.kind === StepItemCostLevelConstant.de)
                    && pointLine[YsshssConstant.ysshSysj]){
                pointLine.ysshGlId = pointLine[YsshssConstant.ysshSysj].sequenceNbr;
            }
            let ssPointLine = this.getSsItemData(pointLine, ssUnitCsXMDatas?.data);
            if(ObjectUtils.isEmpty(ssPointLine)){
                ssPointLine = pointLine;
            }
            this.service.shenHeYuSuanProject.stepItemCostService.open(ssConstructId, ssSingleId, ssUnitId, ssPointLine);
        }
        return true;
    }

    /**
     * 关闭节点
     * @param args
     * @return {Promise<boolean>}
     */
    async close(args){
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId, pointLine} = args;
        // args.sequenceNbr = null;
        // args.pageNum = 1;
        // args.pageSize = 300000;
        // args.isAllFlag = false;

        if(ObjectUtils.isNotEmpty(unitId)){
            this.service.shenHeYuSuanProject.stepItemCostService.close(constructId, singleId, unitId, pointLine)
        }
        if(ObjectUtils.isNotEmpty(ssUnitId)){
            let ssUnitCsXMDatas = await this.getPageDatas(ssConstructId, ssSingleId, ssUnitId, null, 1, 300000, false);
            //获取行对应的送审数据
            if((pointLine.kind === StepItemCostLevelConstant.qd || pointLine.kind === StepItemCostLevelConstant.de)
                && pointLine[YsshssConstant.ysshSysj]){
                pointLine.ysshGlId = pointLine[YsshssConstant.ysshSysj].sequenceNbr;
            }
            let ssPointLine = this.getSsItemData(pointLine, ssUnitCsXMDatas?.data);
            if(ObjectUtils.isEmpty(ssPointLine)){
                ssPointLine = pointLine;
            }
            this.service.shenHeYuSuanProject.stepItemCostService.close(ssConstructId, ssSingleId, ssUnitId, ssPointLine);
        }
        return true;
    }


    /**
     * 根据审定数据 获取送审数据
     * @param sdItemData
     * @param ssUnitCsXMDatas
     * @return {null|*}
     */
    getSsItemData(sdItemData, ssUnitCsXMDatas){
        let ssPointLine = null;
        if(!sdItemData || !ssUnitCsXMDatas){
            return ssPointLine;
        }
        if(sdItemData.kind === StepItemCostLevelConstant.top){
            ssPointLine = ssUnitCsXMDatas.find(item => item.kind === StepItemCostLevelConstant.top);
        }
        else if(sdItemData.kind === StepItemCostLevelConstant.qd || sdItemData.kind === StepItemCostLevelConstant.de){
            ssPointLine = sdItemData.ysshGlId ? ssUnitCsXMDatas.find(item => item.sequenceNbr === sdItemData.ysshGlId) : null;
        }else{
            //除顶部、清单、定额其它数据
            ssPointLine = this.getSsBtFromItemData(sdItemData, ssUnitCsXMDatas);
        }
        return ssPointLine;
    }

    /**
     * 根据审定数据获取送审数据标题
     * @param sdItemData
     * @param ssUnitCsXMDatas
     */
    getSsBtFromItemData(sdItemData, ssUnitCsXMDatas){
        let ssPointLine = null;
        if(sdItemData && ssUnitCsXMDatas){
            ssPointLine = ssUnitCsXMDatas.find(ssQdData => ssQdData.kind === sdItemData.kind
                && ssQdData.name === sdItemData.name
                && ssQdData.constructionMeasureType === sdItemData.constructionMeasureType
                && ssQdData.type === sdItemData.type);
        }
        return ssPointLine;
    }

    /**
     * 初始化颜色数组
     * @param sdItemData
     * @param ssItemData
     * @param shjgData
     */
    async initColorArray(sdItemData, ssItemData, shjgData) {
        let colorArray = {};
        /*
            删项:粉色的 清单整行都是粉色，定额只有编码是粉红色
            改项:红色的 清单整行都是红色，定额，只有编码和变动的数据是红色
            增项:蓝色的 清单整行都是蓝色，定额只有编码是蓝色
         */
        let change = shjgData[YsshssConstant.change];
        //清单
        if(sdItemData.kind === StepItemCostLevelConstant.qd) {
            let color = "";
            if (change === YsshssConstant.insert) {
                //增项
                color = "blue";
            } else if (change === YsshssConstant.delete) {
                //删项
                color = "pink";
            } else if (change === YsshssConstant.update) {
                //改项
                color = "red";
            }
            //整行数据颜色
            colorArray.allColor = color;
        }
        //定额
        else if(sdItemData.kind === StepItemCostLevelConstant.de){
            colorArray.allColor = "";
            if (change === YsshssConstant.insert) {
                colorArray.fxCode = "blue";
            } else if (change === YsshssConstant.delete) {
                colorArray.fxCode = "pink";
            } else if (change === YsshssConstant.update) {
                //改项 会改动字段
                let keyArray = ["fxCode", "type", "name", "projectAttr", "unit", "quantityExpression", "quantity",
                    "zjfPrice", "zjfTotal", "price", "total", "costMajorName", "measureType"
                ];
                for (let i = 0; i < keyArray.length; i++) {
                    let key = keyArray[i];
                    if(sdItemData[key] !== ssItemData[key]){
                        colorArray[key] = "red";
                    }else{
                        colorArray[key] = "";
                    }
                }
            }
        }
        shjgData.colorArray = colorArray;
    }

    /**
     * 针对特殊情况重置审定数据ID
     * @param sdDatas 审定数据
     * @param ssDatas 送审数据
     * @return {Promise<void>}
     */
    async resetSdIds(sdDatas, ssDatas, searchIds) {
        if(ObjectUtils.isEmpty(sdDatas) || ObjectUtils.isEmpty(ssDatas)
        || searchIds.constructId === searchIds.ssConstructId){
            //如果是相同ID可能传参错误，不做处理
            return;
        }
        //审定数据ID
        // let {constructId, singleId, unitId} = searchIds;
        let sdUnit = PricingFileFindUtils.getUnit(searchIds.constructId, searchIds.singleId, searchIds.unitId);
        for (let i = 0; i < ssDatas.length; i++) {
            let ssData = ssDatas[i];
            if(ssData.kind !== StepItemCostLevelConstant.qd){
                //非清单数据，不处理
                continue;
            }
            //该送审数据未绑定审定数据 且 与审定数据ID相同 则重置审定数据ID并将parentId为此值数据一起修改
            let sdData = sdDatas.find(item => item.ysshGlId === ssData.sequenceNbr);
            let sameIdSdData = sdDatas.find(item => item.sequenceNbr === ssData.sequenceNbr);
            if(ObjectUtils.isEmpty(sdData) && ObjectUtils.isNotEmpty(sameIdSdData)){
                //新审定数ID
                const newSdId = Snowflake.nextId();
                //查询该审定数据子集数据并重置parentId
                let childSdData = sdDatas.filter(item => item.parentId === sameIdSdData.sequenceNbr);
                if(childSdData){
                    //重置子集parentId
                    childSdData.forEach(item => {
                        item.parentId = newSdId;
                    })
                }
                //
                let quantities = sameIdSdData.quantities;
                if(quantities){
                    quantities.forEach(item => {
                        item.quotaListId = newSdId;
                    })
                }
                //修改其它数据
                await this.updateSdOrtherDatas(sdUnit, sameIdSdData.sequenceNbr, newSdId);
                //修改父级Id
                sameIdSdData.oldSequenceNbr = sameIdSdData.sequenceNbr;
                sameIdSdData.sequenceNbr = newSdId;
            }
        }


    }

    /**
     * 修改其它数据
     * 单位工程中：feeBuild:key
     * 单位工程中：zjcsCostMathCache.data:sequenceNbr
     * @param sdUnit
     * @param oldSdId
     * @param newSdId
     */
    updateSdOrtherDatas(sdUnit, oldSdId, newSdId) {
        if(!sdUnit ){
            return;
        }
        if(sdUnit.feeBuild){
            let feeBuilds = sdUnit.feeBuild;
            let oldFeeBultArray = ConvertUtil.deepCopy(feeBuilds[oldSdId]);
            feeBuilds[newSdId] = oldFeeBultArray;
            //删除旧数据
            delete feeBuilds[oldSdId];
            console.log("重置清单ID，feeBuilds重置完毕！");
        }
        if(sdUnit.zjcsCostMathCache && sdUnit.zjcsCostMathCache.data){
            let zjcsCostMaths = sdUnit.zjcsCostMathCache.data;
            let oldZjcsCostMaths = zjcsCostMaths.filter(item => item.sequenceNbr === oldSdId);
            oldZjcsCostMaths.forEach(item => {item.sequenceNbr = newSdId});
            console.log("重置清单ID，zjcsCostMaths重置完毕！");

        }
    }


}

ysshMeasureService.toString = () => "[class ysshMeasureService]"
module.exports = ysshMeasureService
