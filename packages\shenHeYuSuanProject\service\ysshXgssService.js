const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const Log = require('../../../core/log');
const {Service} = require("../../../core");


/**
 * 修改送审
 */
class YsshXgssService extends Service{

    constructor(ctx) {
        super(ctx);
    }



    /**
     * 打开修改送审:
     * 未应用之前，修改送审数据不保存，此处讲与送审数据全部复制
     * @param args
     * @returns {Promise<void>}
     */
    async openSongShen(args) {
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId} = args;
        let newIds = {};
        // newIds.ssConstructId = ssConstructId;
        // newIds.ssSingleId = ssSingleId;
        // newIds.ssUnitId = ssUnitId;

        //获取所有送审数据缓存数据
        const ssProject = PricingFileFindUtils.getProjectObjById(ssConstructId);
        if(!ObjectUtils.isEmpty(ssProject)){
            //深度拷贝
            let newSsProject = ConvertUtil.deepCopy(ssProject);

            //设置新ID
            const newId = Snowflake.nextId();
            //在新数据中存储原送审数据ID
            const songShenId = ssProject.sequenceNbr;

            //存储在备份送审数据中，前端关闭窗口，返给前端
            newSsProject.sequenceNbr = newId;
            ssProject.sdConstructId = constructId;
            ssProject.sdSingleId = singleId;
            ssProject.sdUnitId = unitId;

            //修改项目工程ID

            //备份数据存储在 送审数据中
            ssProject.songShenId = newId;
            //批量修改所有子项中的constructId
            ObjectUtils.updatePropertyValue(newSsProject, 'constructId', newId)
            /*
                将新对象数据写入缓存，修改送审，时
             */
            PricingFileWriteUtils.writeToMemory(newSsProject);
            newIds.ssConstructId = ssConstructId;
            newIds.ssSingleId = ssSingleId;
            newIds.ssUnitId = ssUnitId;
        }
        return newIds;
    }

    /**
     * 修改送审-应用修改
     * @param args
     * @returns {Promise<{}>}
     */
    async saveSongShenDatas(args) {
        const {ssConstructId, ssSingleId, ssUnitId} = args;
        let result = {};

        //修改的送审数据
        const ssProject = PricingFileFindUtils.getProjectObjById(ssConstructId);
        if(!ssProject.songShenId){
            Log.error('未查询到原送审数据关联信息！');
            return result;
        }
        //备份数据
        let bfSsProject = PricingFileFindUtils.getProjectObjById(ssProject.songShenId);
        if(!bfSsProject){
            Log.error('未查询到备份送审数据！');
            return result;
        }
        //存储在备份送审数据中，前端关闭窗口，返给前端
        const {sdConstructId, sdSingleId, sdUnitId} = ssProject;
        result.sdConstructId = sdConstructId;
        result.sdSingleId = sdSingleId;
        result.sdUnitId = sdUnitId;

        //删除关联ID
        delete ssProject.songShenId;
        delete ssProject.sdConstructId;
        delete ssProject.sdSingleId;
        delete ssProject.sdUnitId;

        //删除新送审数据缓存数据
        this.clearYsfMemoryForNewSongShen(bfSsProject)


        //对象重新 自动关联
        let zd ={};
        zd.sequenceNbr = sdConstructId;
        zd.ysshConstructId = ssConstructId;
        this.service.shenHeYuSuanProject.shenHeProjectService.autoBindingRule(zd);


        return result;
    }

    /**
     * 【修改送审】取消
     * @param args
     * @returns {Promise<{}>}
     */
    async removeSongShenDatas(args) {
        let result = {};
        const {ssConstructId, ssSingleId, ssUnitId} = args;
        //获取预算数据
        const ssProject = PricingFileFindUtils.getProjectObjById(ssConstructId);

        //存储在备份送审数据中，前端关闭窗口，返给前端
        const {sdConstructId, sdSingleId, sdUnitId} = ssProject;
        result.sdConstructId = sdConstructId;
        result.sdSingleId = sdSingleId;
        result.sdUnitId = sdUnitId;

        if(!ssProject.songShenId){
            Log.error('未查询到原送审数据关联信息！');
            return ssConstructId;
        }

        //获取备份数据
        const bfSsProject =  PricingFileFindUtils.getProjectObjById(ssProject.songShenId);
        if (ObjectUtils.isEmpty(bfSsProject)){
            Log.error('未查询到原送审数据关联信息对象！');
            return ssConstructId;
        }

        //把备份数据 恢复到 送审数据
        //批量修改所有子项中的constructId
        bfSsProject.sequenceNbr = ssConstructId;
        ObjectUtils.updatePropertyValue(bfSsProject, 'constructId', ssConstructId);

        //删除原 审数据缓存数据
        this.clearYsfMemoryForNewSongShen(ssProject)

        //把备份变为 送审数据
        PricingFileWriteUtils.writeToMemory(bfSsProject);




        return result;
    }


    //删除新送审数据缓存数据
    clearYsfMemoryForNewSongShen(obj){
        global.constructProject = global.constructProject || {};
        //只可清除【修改送审】-新送审数据
        if(obj && obj.sequenceNbr){
            Log.warn("删除项目缓存数据：" + obj.sequenceNbr);
            delete global.constructProject[obj.sequenceNbr];
        }
    }



}


YsshXgssService.toString = () => '[class YsshXgssService]';
module.exports = YsshXgssService;