const {ConvertUtil} = require( "../../../electron/utils/ConvertUtils");
const {Service} = require("../../../core");
const fydm_jiesuan = require("../jsonData/fydm_jiesuan.json");
const CostCodeTypeEnum_2012 = require("../../../electron/enum/CostCodeTypeEnum_2012");
const CostCodeTypeEnum_2022 = require("../../../electron/enum/CostCodeTypeEnum_2022");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {UnitCostCodePrice} = require("../../../electron/model/UnitCostCodePrice");
const OtherProjectCalculationBaseConstant = require("../../../electron/enum/OtherProjectCalculationBaseConstant");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const JieSuanFeeSetEnum = require("../enum/JieSuanFeeSetEnum");

class jieSuanUnitCostCodePriceService extends Service{

    constructor(ctx) {
        super(ctx);
    }


    async initUnitCostCodePrice(args){
        let {constructId,singleId, unitId} = args;

        let unitObj=await PricingFileFindUtils.getUnit(constructId,singleId, unitId)
        //存储预算费用代码值用来计算造价分析的值
        unitObj.unitCostCodePrices_ys = ConvertUtil.deepCopy(unitObj.unitCostCodePrices)
        let array =[]
        for (let i in fydm_jiesuan) {
            let obj = new UnitCostCodePrice();
            ConvertUtil.setDstBySrc(fydm_jiesuan[i], obj)
            obj.price = 0;
            obj.sequenceNbr = Snowflake.nextId();
            array.push(obj);
        }

        unitObj.unitCostCodePrices = array;


        await this.count(args)

    }


    updateUnitCostCodePrice(args){
        for (let i = 0; i < args.unitCostCodePrices.length; i++) {
            args.unitCostCodePrices[i].sequenceNbr = Snowflake.nextId();
        }
        return  PricingFileWriteUtils.updateUnitCostCodePrice(args.unitCostCodePrices,args.constructId,args.singleId, args.unitId);
    }

    getUnitCostCodePrice(args){
        //this.countCostCodePrice(args);
        let unitCostCodePrice = PricingFileFindUtils.getUnitCostCodePrice(args.constructId,args.singleId, args.unitId);
        return  unitCostCodePrice;
    }

    costCodePrice(args){
        let unitCostCodePrice = this.getUnitCostCodePrice(args);
        if(args.type){
            let filter = unitCostCodePrice.filter(item => item.type === args.type );
            return filter;
        }else {
            return unitCostCodePrice;
        }

    }
    costCodeTypeList(args){
        let unitIs2022= PricingFileFindUtils.getConstructDeStandard(args.constructId)==ConstantUtil.DE_STANDARD_22;
        if(unitIs2022){
            let costCodeTypeEnum=Object.values(CostCodeTypeEnum_2022)
            return costCodeTypeEnum
        }else{
            let costCodeTypeEnum=Object.values(CostCodeTypeEnum_2012)
            return costCodeTypeEnum
        }

    }

    /**
     * 计算费用代码和更新费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async countCostCodePrice(args){
        await this.count(args);
        let unit = await PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);


        //更新费用汇总
        await this.service.jieSuanProject.jieSuanUnitCostSummaryService.countUnitCostSummary(unit.unitCostCodePrices,unit.unitCostSummarys,unit,true)
    }


    async count(args) {
        await this.service.unitCostCodePriceService.countCostCodePrice(args)

        let unit = await PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);

        let rgRcjtc = await this.service.jieSuanProject.jieSuanRcjStageService.getFyhzJc(args.constructId, args.singleId, args.unitId,JieSuanRcjDifferenceEnum.RENGONG.code);
        let clNosbRcjtc = await this.service.jieSuanProject.jieSuanRcjStageService.getFyhzJc(args.constructId, args.singleId, args.unitId,JieSuanRcjDifferenceEnum.CAILIAO.code);
        let jxRcjtc = await this.service.jieSuanProject.jieSuanRcjStageService.getFyhzJc(args.constructId, args.singleId, args.unitId,JieSuanRcjDifferenceEnum.JIXIE.code);
        let zgjcl = await this.service.jieSuanProject.jieSuanRcjStageService.getFyhzJc(args.constructId, args.singleId, args.unitId,JieSuanRcjDifferenceEnum.JIXIE.code);

        let constructProjectRcjs = [].concat(rgRcjtc).concat(clNosbRcjtc).concat(jxRcjtc).concat(zgjcl)
        //工程项目
        let constructProject = await PricingFileFindUtils.getProjectObjById(args.constructId);
        //计税对象
        let projectTaxCalculation = constructProject.projectTaxCalculation;
        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;

        let simple = false;

        //判断计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            //简易计税
            simple = true;
        }
        //分部分项定额集合
        let itemBiliDeList = unit.itemBillProjects.filter(item => item.kind === BranchProjectLevelConstant.de);
        //措施项目定额
        let deMeasureProjectTables = PricingFileFindUtils.getUnitDatas(unit.measureProjectTables, BranchProjectLevelConstant.de);
        // 所有定额(整个单位工程)
        let unitDe = [...itemBiliDeList, ...deMeasureProjectTables];
        // 基数定额（不含安装费用、装饰超高、其他总价措施定额 下）
        let jsDe = unitDe.filter(item => item.isCostDe === 0 || item.isCostDe === 1 || item.isCostDe === 4 || item.isCostDe === 6);

        // let constructProjectRcjs = await this.service.rcjProcess.getCountRcjList(args.constructId, args.singleId, args.unitId) === null ? new Array() : this.service.rcjProcess.getCountRcjList(args.constructId, args.singleId, args.unitId);
        // 材料、商砼、砼、浆、商浆、配比、设备、主材
        let allClRcj = constructProjectRcjs.filter(item => (item.kind === 2 || item.kind === 6 || item.kind === 7 || item.kind === 8 || item.kind === 9 || item.kind === 10 || item.kind === 5 || item.kind === 4));

        // 非设备
        let noSbRcj = constructProjectRcjs.filter(item => (item.kind !== 4));

        // 人材机
        let rgClJxRcj = constructProjectRcjs.filter(item => (item.kind === 2 || item.kind === 6 || item.kind === 7 || item.kind === 8 || item.kind === 9 || item.kind === 10 || item.kind === 1 || item.kind === 3));


        //人工人材机
        let rgRcj = []
        //材料人材机
        let clRcj = []
        //机械人材机
        let jxRcj = []
        //主材人材机
        let zcRcj = []
        //设备人材机
        let sbRcj = []

        for (let i = 0; i < constructProjectRcjs.length; i++) {
            let item = constructProjectRcjs[i];
            switch (item.kind) {
                case 1: {
                    rgRcj.push(item);
                    break;
                }
                case 2: {
                    clRcj.push(item);
                    break;
                }
                case 6: {
                    clRcj.push(item);
                    break;
                }
                case 7: {
                    clRcj.push(item);
                    break;
                }
                case 8: {
                    clRcj.push(item);
                    break;
                }
                case 9: {
                    clRcj.push(item);
                    break;
                }
                case 10: {
                    clRcj.push(item);
                    break;
                }
                case 3: {
                    jxRcj.push(item);
                    break;
                }
                case 5: {
                    zcRcj.push(item);
                    break;
                }
                case 4: {
                    sbRcj.push(item);
                    break;
                }
            }
        }


        // 材料调差  非设备
        // let clNosbRcjtc
        // if (!ObjectUtils.isEmpty(noSbRcj)) {
        //     clNosbRcjtc = rgRcj.filter(item => item.isDifference === true);
        // }

        // 材料调差  设备
        // let clRcjtc
        // if (!ObjectUtils.isEmpty(sbRcj)) {
        //     clRcjtc = rgRcj.filter(item => item.isDifference === true);
        // }

        // 机械调差
        // let jxRcjtc
        // if (!ObjectUtils.isEmpty(jxRcj)) {
        //     jxRcjtc = rgRcj.filter(item => item.isDifference === true);
        // }

        //暂估价调差  设备
        // let zgSbRcj
        // if (!ObjectUtils.isEmpty(sbRcj)) {
        //     zgSbRcj = sbRcj.filter(item => item.ifProvisionalEstimate === 1);
        // }

        // 暂估价调差 非设备人材机
        let zgNoSbRcj
        if (!ObjectUtils.isEmpty(noSbRcj)) {
            zgNoSbRcj = noSbRcj.filter(item => item.ifProvisionalEstimate === 1);
        }

        //甲供材料人材机  材料 调差
        let jgClRcjtc
        if (!ObjectUtils.isEmpty(clRcj)) {
            jgClRcjtc = clRcj.filter(item => item.ifDonorMaterial === 1);
        }

        //甲供主要材料人材机  主材 调差
        let jgZcRcjtc
        if (!ObjectUtils.isEmpty(zcRcj)) {
            jgZcRcjtc = zcRcj.filter(item => item.ifDonorMaterial === 1 );
        }

        //甲供材料人材机  设备 调差
        let jgSbRcjtc
        if (!ObjectUtils.isEmpty(sbRcj)) {
            jgSbRcjtc = sbRcj.filter(item => item.ifDonorMaterial === 1 );
        }


        //暂估调差  人材机 且 记取方式为记取规费、税金
        let zgrgClJxRcj2
        if (!ObjectUtils.isEmpty(rgClJxRcj)) {
            zgrgClJxRcj2 = rgClJxRcj.filter(item => item.ifProvisionalEstimate === 1 && item.jieSuanFee === JieSuanFeeSetEnum.METHOD2.code);
        }
        //暂估调差  人材机 且 记取方式为记取规费、安稳费
        let zgrgClJxRcj3
        if (!ObjectUtils.isEmpty(rgClJxRcj)) {
            zgrgClJxRcj3 = rgClJxRcj.filter(item => item.ifProvisionalEstimate === 1 && item.jieSuanFee === JieSuanFeeSetEnum.METHOD3.code);
        }



        //甲供:是  材料、商砼、砼、浆、商浆、配比、设备、主材
        let jgallClRcj
        if (!ObjectUtils.isEmpty(allClRcj)) {
            jgallClRcj = allClRcj.filter(item => item.ifDonorMaterial === 1);
        }


        //价差安全生产、文明施工费
        let jcaqwmsgf = 0;

        // 价差材料费进项税额
        let jcclfjxse = 0;

        // 价差机械费进项税额
        let jcjxfjxse = 0;

        // 价差安全生产、文明施工费合计
        let jcaqwmsgfhj = 0;

        // 价差暂估价进项税额
        let jczgjjxse = 0;


        //费用代码
        let unitCostCodePrices = unit.unitCostCodePrices;

        let awfData = unitCostCodePrices.find(item => item.code === 'AQWMSGF');


        for (let i = 0; i < unitCostCodePrices.length; i++) {
            let unitCostCodePrice = unitCostCodePrices[i];

            switch (unitCostCodePrice.code) {
                case 'JSJC': {
                    //结算价差合计 ∑(单位工程下【所有人材机】下除[类型]="设备"外其余人材机数据的`价差合计`)  ok
                    if (!ObjectUtils.isEmpty(constructProjectRcjs)) {
                        unitCostCodePrice.price = Number(constructProjectRcjs.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add( accumulator ,constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1000;
                    break;
                }
                case 'JS_JGCLF': {
                    //结算甲供材料费 ∑(单位工程下【所有人材机】中[是否甲供]="是"且人材机类型=“材料、商砼、砼、浆、商浆、配比、设备、主材”的[合同市场价]*[甲供数量]*(1-[保管费率%] )  ok
                    if (!ObjectUtils.isEmpty(jgallClRcj)) {
                        unitCostCodePrice.price = jgallClRcj.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, NumberUtil.multiply(constructProjectRcj.marketPrice, constructProjectRcj.donorMaterialNumber, NumberUtil.subtract(1, ObjectUtils.isEmpty(constructProjectRcj.jieSuanAdminRate) ? 0 : constructProjectRcj.jieSuanAdminRate)));
                        }, 0);
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1001;
                    break;
                }
                case 'JS_JCRGF': {
                    //结算人工价差 ∑单位工程【人工调差】下结算价差`价差合计`  ok
                    if (!ObjectUtils.isEmpty(rgRcjtc)) {
                        unitCostCodePrice.price = Number(rgRcjtc.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add( accumulator ,constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1002;
                    break;
                }
                case 'JS_JCCLF': {
                    //结算材料价差 ∑单位工程【材料调差】下结算价差除材料[类型]=“设备”的以外数据行`价差合计`  ok
                    if (!ObjectUtils.isEmpty(clNosbRcjtc)) {
                        unitCostCodePrice.price = Number(clNosbRcjtc.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add( accumulator ,constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1003;
                    break;
                }
                case 'JS_JCJXF': {
                    //结算机械价差 ∑单位工程【机械调差】下结算价差`价差合计`
                    if (!ObjectUtils.isEmpty(jxRcjtc)) {
                        unitCostCodePrice.price = Number(jxRcjtc.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add( accumulator ,constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1004;
                    break;
                }
                case 'JS_JCZGJ': {
                    //结算暂估价差 ∑单位工程【暂估价调差】下除[类型]="设备"外其余人材机数据结算价差`价差合计`  ok
                    if (!ObjectUtils.isEmpty(zgNoSbRcj)) {
                        unitCostCodePrice.price = zgNoSbRcj.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0);
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1005;
                    break;
                }
                case 'JS_JCSBF': {
                    //结算设备费价差 ∑单位工程下调差材料中[类型]为“设备”的结算价差`价差合计`

                    unitCostCodePrice.price = 0;//【设备没有调差功能】

                    // unitCostCodePrice.price = 1006;
                    break;
                }
                case 'JS_JCJGCLF': {
                    //结算甲供材料费价差 ∑单位工程下调差材料中类型为类型=“材料、商砼、砼、浆、商浆、配比””且为甲供的单位价差*甲供数量   ok
                    if (!ObjectUtils.isEmpty(jgClRcjtc)) {
                        unitCostCodePrice.price = Number(jgClRcjtc.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, NumberUtil.multiply(constructProjectRcj.jieSuanPriceDifferenc, constructProjectRcj.donorMaterialNumber));
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1007;
                    break;
                }
                case 'JS_JCJGZCF': {
                    //结算甲供主材费价差 ∑单位工程下调差材料中类型为“主材”且为甲供的单位价差*甲供数量   ok
                    if (!ObjectUtils.isEmpty(jgZcRcjtc)) {
                        unitCostCodePrice.price = Number(jgZcRcjtc.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, NumberUtil.multiply(constructProjectRcj.jieSuanPriceDifferenc, constructProjectRcj.donorMaterialNumber));
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 1009;
                    break;
                }
                case 'JS_JCJGSBF': {
                    //结算甲供设备费价差 ∑单位工程下调差材料中类型为“设备”且为甲供的单位价差*甲供数量   ok
                    if (!ObjectUtils.isEmpty(jgSbRcjtc)) {
                        unitCostCodePrice.price = Number(jgSbRcjtc.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, NumberUtil.multiply(constructProjectRcj.jieSuanPriceDifferenc, constructProjectRcj.donorMaterialNumber));
                        }, 0).toFixed(2));
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 2000;
                    break;
                }
                case 'JCHJ_JGFHSJ': {
                    //价差合计(计规费和税金) ∑(单位工程下【人工、材料、机械、暂估价调差】中`取费`="记取规费、税金"的人材机数据的`价差合计`)  ok
                    if (!ObjectUtils.isEmpty(zgrgClJxRcj2)) {
                        unitCostCodePrice.price = zgrgClJxRcj2.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0);
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 2001;
                    break;
                }
                case 'JCGFHJ': {
                    //价差规费合计(计规费和税金) ∑(单位工程下【人工、材料、机械、暂估价调差】中`取费`="记取规费、税金"的人材机数据的`价差规费`之和)  ok
                    if (!ObjectUtils.isEmpty(zgrgClJxRcj2)) {
                        unitCostCodePrice.price = zgrgClJxRcj2.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0);
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 2002;
                    break;
                }
                case 'JCAQWMSGFHJ': {
                    //价差安全生产、文明施工费合计（计安、文施工费和税金) ∑(单位工程下【人工、材料、机械、暂估价调差】中`取费`="记取规费、安文费"的人材机数据的`价差安文费`之和)    ok
                    if (!ObjectUtils.isEmpty(zgrgClJxRcj3)) {
                        jcaqwmsgf = Number(zgrgClJxRcj3.reduce((accumulator, constructProjectRcj) => {
                            return NumberUtil.add(accumulator, constructProjectRcj.jieSuanPriceDifferencSum);
                        }, 0).toFixed(2));
                        unitCostCodePrice.price = jcaqwmsgf;
                    } else {
                        unitCostCodePrice.price = 0;
                    }
                    // unitCostCodePrice.price = 2003;
                    break;
                }


                case 'JCJXSE': {
                    //价差进项税额  价差材料费进项税额+价差机械费进项税额+价差安全生产、文明施工进项税额+价差暂估价进项税额  ok
                    if (!simple) {
                        unitCostCodePrice.price = NumberUtil.numberScale2(NumberUtil.addParams(jcclfjxse, jcjxfjxse, jcaqwmsgfhj, jczgjjxse))
                        // unitCostCodePrice.price = 2004;
                    }
                    break;
                }
                case 'JCCLFJXSE': {
                    //价差材料费进项税额  ∑单位工程下调差材料类型=材料、商砼、砼、浆、商浆、配比、主材的数据行[结算价差进项税额]  ok
                    //判断计税
                    if (!simple) {
                        let clJsDeRcj = []
                        if (!ObjectUtils.isEmpty(allClRcj)) {
                            clJsDeRcj = allClRcj.filter(item => item.ifDonorMaterial !== 1);
                            jcclfjxse = Number(clJsDeRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.settlementPriceDifferencInputTax);
                            }, 0).toFixed(2));
                            unitCostCodePrice.price = jcclfjxse;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        // unitCostCodePrice.price = 2005;
                    }
                    break;
                }
                case 'JCJXFJXSE': {
                    //价差机械费进项税额  ∑单位工程下调差材料类型=机械的数据行[结算价差进项税额]  ok
                    //判断计税
                    if (!simple) {
                        let jxJsDeRcj = []
                        if (!ObjectUtils.isEmpty(jxRcj)) {
                            jxJsDeRcj = jxRcj.filter(item => item.ifDonorMaterial !== 1);
                            jcjxfjxse = Number(jxJsDeRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.settlementPriceDifferencInputTax);
                            }, 0).toFixed(2));
                            unitCostCodePrice.price = jcjxfjxse;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        // unitCostCodePrice.price = 2006;
                    }
                    break;
                }
                case 'JCZGFJXSE': {
                    //价差暂估价进项税额  ∑单位工程下调差材料[是否暂估]为“是”，且类型≠“设备”的数据行[结算价差进项税额]  ok
                    //判断计税
                    if (!simple) {
                        if (!ObjectUtils.isEmpty(zgNoSbRcj)) {
                            jczgjjxse = Number(zgNoSbRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.settlementPriceDifferencInputTax);
                            }, 0).toFixed(2));
                            unitCostCodePrice.price = jczgjjxse;
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        // unitCostCodePrice.price = 2007;
                    }
                    break;
                }


                case 'JCSBFJXSE': {
                    //价差设备费进项税额  ∑单位工程下调差材料中类型=“设备”的[结算价差进项税额]  ok
                    //判断计税
                    if (!simple) {
                        if (!ObjectUtils.isEmpty(sbRcj)) {
                            sbRcj = sbRcj.filter(item => item.ifDonorMaterial !== 1);
                            unitCostCodePrice.price = Number(sbRcj.reduce((accumulator, constructProjectRcj) => {
                                return NumberUtil.add(accumulator, constructProjectRcj.settlementPriceDifferencInputTax);
                            }, 0).toFixed(2));
                        } else {
                            unitCostCodePrice.price = 0;
                        }
                        // unitCostCodePrice.price = 2008;
                    }
                    break;
                }
                case 'JCAQWMSGF': {
                    //价差安全生产、文明施工费进项税额  价差安全生产、文明施工费合计*安文费除税系数3%   ok
                    //判断计税
                    if (!simple) {
                        if (jcaqwmsgf === 0) {
                            unitCostCodePrice.price = 0;
                        } else {
                            jcaqwmsgfhj = Number(NumberUtil.multiply(jcaqwmsgf, 0.03).toFixed(2));
                            unitCostCodePrice.price = jcaqwmsgfhj;
                        }
                        // unitCostCodePrice.price = 2009;
                    }
                    break;
                }


            }
        }

        await this.service.inputTaxDetailsService.countInputTaxDetails(unit);
    }

    /**
     * 计算装饰地上地下的人工金额
     */
    calculateZsData(constructId,singleId, unitId){
        let unit = PricingFileFindUtils.getUnit(constructId,singleId, unitId);


        //分部分项定额
        let deByfbfx = PricingFileFindUtils.getDeByfbfx(constructId,singleId, unitId);
        //单价措施定额
        let deByDjcs = PricingFileFindUtils.getDeByDjcs(constructId,singleId, unitId);

        let deList = [];
        deList.push(...deByfbfx);
        deList.push(...deByDjcs);
        if (ObjectUtils.isEmpty(deList)){
            return {"up":0,"down":0};
        }

        let ds = [];
        let dx = [];
        for (const de of deList) {
            if (de.upOrDown === "up"){
                ds.push(de);
            }
            if (de.upOrDown === "down"){
                dx.push(de);
            }
        }

        //获取到当前项目的所有人材机数据
        let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);

        if (ObjectUtils.isEmpty(deList)) {
            return {"up":0,"down":0};
        }
        let dsIdList = ds.map(k => k.sequenceNbr);
        let dxIdList = dx.map(k => k.sequenceNbr);
        //求和
        let dsReduce = rcjList.filter(k => k.kind === 1 && k.unit === '工日' && dsIdList.includes(k.deId))
            .map(k => k.totalNumber).reduce((accumulator, currentValue) => accumulator + currentValue, 0);

        let dxReduce = rcjList.filter(k => k.kind === 1 && k.unit === '工日' && dxIdList.includes(k.deId))
            .map(k => k.totalNumber).reduce((accumulator, currentValue) => accumulator + currentValue, 0);


        let dsNumber = NumberUtil.numberScale(dsReduce,6);
        let dxNumber = NumberUtil.numberScale(dxReduce,6);


        let itemBillProjects = unit.itemBillProjects;
        let measureProjectTables = unit.measureProjectTables;

        let allDe =[];
        allDe.push(itemBillProjects);
        allDe.push(measureProjectTables);
        //获取垂运的费用定额
        //地上
        let dsCostDe = allDe.filter(k =>k.quantityExpression === "DSZSGR");
        dsCostDe.forEach(k =>{
            k.quantityExpressionNbr = dsNumber;
        });

        //地下
        let dxCostDe = allDe.filter(k =>k.quantityExpression === "DXZSGR");
        dxCostDe.forEach(k =>{
            k.quantityExpressionNbr = dxNumber;
        });
        return {"up":NumberUtil.numberScale(dsReduce,6),"down":NumberUtil.numberScale(dxReduce,6)};

    }


}
jieSuanUnitCostCodePriceService.toString = () => '[class jieSuanUnitCostCodePriceService]';
module.exports = jieSuanUnitCostCodePriceService;
