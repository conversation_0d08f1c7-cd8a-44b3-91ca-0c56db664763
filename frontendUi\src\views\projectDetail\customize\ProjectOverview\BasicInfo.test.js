/**
 * 测试 BasicInfo.vue 中 timeSelect 函数的 oldRemark 传递
 * 验证撤销回退功能是否正确工作
 */

// 模拟依赖
const mockXeUtils = {
  clone: (data, deep) => JSON.parse(JSON.stringify(data))
};

const mockMessage = {
  success: jest.fn(),
  warning: jest.fn()
};

const mockProjectStore = {
  currentTreeInfo: { levelType: 1 },
  currentTreeGroupInfo: { constructId: 'test-construct-id' },
  type: 'ys'
};

// 模拟 saveAndUpdateOperate 函数
const mockSaveAndUpdateOperate = jest.fn();

// 模拟 redo 对象
const mockRedo = {
  addnoMatchedRedoList: jest.fn()
};

// 测试数据
const testTableData = [
  {
    sequenceNbr: 1,
    name: '开工日期',
    remark: '2024-01-01',
    groupCode: 1
  }
];

/**
 * 测试 timeSelect 函数是否正确传递 oldRemark 参数
 */
function testTimeSelectOldRemarkPassing() {
  // 模拟 timeSelect 函数的实现
  let oldRemark = '';
  
  const timeSelect = (row, { $event }) => {
    console.log('timeSelect', $event);
    oldRemark = row.remark; // 保存旧值
    row['remark'] = $event.value; // 设置新值
    console.log('saveOrUpdateBasicInfo', $event.value, oldRemark);
    
    // 调用 saveOrUpdateBasicInfo 并传递正确的参数
    saveOrUpdateBasicInfo(
      { data: mockXeUtils.clone(testTableData, true) }, 
      true, 
      $event.value, 
      oldRemark, 
      '备注'
    );
  };

  const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, oldValue, title) => {
    // 调用 saveAndUpdateOperate 并传递所有参数
    let res = await mockSaveAndUpdateOperate(param.data, value, oldValue, title);
    console.log('saveOrUpdateBasicInfo', res, param.data, value, oldValue, title);
    return res;
  };

  // 测试场景：修改日期
  const testRow = testTableData[0];
  const testEvent = { value: '2024-12-31' };
  
  console.log('测试开始：修改日期从', testRow.remark, '到', testEvent.value);
  
  // 执行 timeSelect 函数
  timeSelect(testRow, { $event: testEvent });
  
  // 验证结果
  console.log('测试结果：');
  console.log('- 新值:', testRow.remark);
  console.log('- 旧值:', oldRemark);
  console.log('- saveAndUpdateOperate 调用次数:', mockSaveAndUpdateOperate.mock.calls.length);
  
  if (mockSaveAndUpdateOperate.mock.calls.length > 0) {
    const lastCall = mockSaveAndUpdateOperate.mock.calls[mockSaveAndUpdateOperate.mock.calls.length - 1];
    console.log('- saveAndUpdateOperate 最后一次调用参数:', lastCall);
    
    // 验证参数传递是否正确
    const [data, newValue, oldValue, title] = lastCall;
    console.log('参数验证：');
    console.log('  - newValue:', newValue, '(期望: 2024-12-31)');
    console.log('  - oldValue:', oldValue, '(期望: 2024-01-01)');
    console.log('  - title:', title, '(期望: 备注)');
    
    if (newValue === '2024-12-31' && oldValue === '2024-01-01' && title === '备注') {
      console.log('✅ 测试通过：timeSelect 正确传递了 oldRemark 参数');
      return true;
    } else {
      console.log('❌ 测试失败：参数传递不正确');
      return false;
    }
  } else {
    console.log('❌ 测试失败：saveAndUpdateOperate 未被调用');
    return false;
  }
}

/**
 * 测试 saveAndUpdateOperate 函数是否正确处理 oldValue 参数
 */
function testSaveAndUpdateOperateOldValueHandling() {
  const saveAndUpdateOperate = async (data, newValue, oldValue, title) => {
    // 模拟调用 redo.addnoMatchedRedoList
    mockRedo.addnoMatchedRedoList({
      sequenceNbr: data.sequenceNbr,
      columnTitle: title,
      newValue,
      oldValue,
    });
    
    console.log('saveAndUpdateOperate 调用 redo.addnoMatchedRedoList，参数：', {
      sequenceNbr: data.sequenceNbr,
      columnTitle: title,
      newValue,
      oldValue,
    });
    
    return { status: 200 };
  };

  // 测试数据
  const testData = testTableData[0];
  const newValue = '2024-12-31';
  const oldValue = '2024-01-01';
  const title = '备注';
  
  console.log('测试 saveAndUpdateOperate 函数...');
  
  // 执行函数
  saveAndUpdateOperate(testData, newValue, oldValue, title);
  
  // 验证 redo.addnoMatchedRedoList 是否被正确调用
  console.log('redo.addnoMatchedRedoList 调用次数:', mockRedo.addnoMatchedRedoList.mock.calls.length);
  
  if (mockRedo.addnoMatchedRedoList.mock.calls.length > 0) {
    const lastCall = mockRedo.addnoMatchedRedoList.mock.calls[mockRedo.addnoMatchedRedoList.mock.calls.length - 1];
    console.log('redo.addnoMatchedRedoList 最后一次调用参数:', lastCall[0]);
    
    const { sequenceNbr, columnTitle, newValue: passedNewValue, oldValue: passedOldValue } = lastCall[0];
    
    if (passedNewValue === newValue && passedOldValue === oldValue && columnTitle === title) {
      console.log('✅ 测试通过：saveAndUpdateOperate 正确处理了 oldValue 参数');
      return true;
    } else {
      console.log('❌ 测试失败：参数处理不正确');
      return false;
    }
  } else {
    console.log('❌ 测试失败：redo.addnoMatchedRedoList 未被调用');
    return false;
  }
}

// 运行测试
console.log('=== BasicInfo.vue timeSelect oldRemark 修复测试 ===\n');

console.log('1. 测试 timeSelect 函数 oldRemark 参数传递...');
const test1Result = testTimeSelectOldRemarkPassing();

console.log('\n2. 测试 saveAndUpdateOperate 函数 oldValue 参数处理...');
const test2Result = testSaveAndUpdateOperateOldValueHandling();

console.log('\n=== 测试总结 ===');
console.log('timeSelect 函数测试:', test1Result ? '通过' : '失败');
console.log('saveAndUpdateOperate 函数测试:', test2Result ? '通过' : '失败');
console.log('整体修复状态:', (test1Result && test2Result) ? '✅ 修复成功' : '❌ 需要进一步修复');

// 导出测试函数（如果在模块环境中使用）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testTimeSelectOldRemarkPassing,
    testSaveAndUpdateOperateOldValueHandling
  };
}
