
const DePropertyTypeConstant = require("../../../electron/enum/DePropertyTypeConstant");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {ResponseData} = require("../../../common/ResponseData");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");

const {Service} = require("../../../core");


/**
 * 人材机 process
 */
class JieSuanRcjProcess extends Service {

    constructor(ctx) {
        super(ctx);
    }

    //1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比
    getUnitRcjType(arg) {
        let {constructId, singleId, unitId, levelType} = arg;
        let treeListVO = {};
        let itemList = new Array();
        if (levelType === 1) {
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            let rcj = {"0": "合同内所有人材机", type: 1};
            itemList.push(rcj);
            //查找合同内的单位是否有一个选择了分期  ifParticipateInAdjustment
            let tempUnitList = unitList.filter(k => k.originalFlag && ObjectUtil.isNotEmpty(k.rcjStageSet));


            let list = unitList.filter(k => !k.originalFlag && k.isDifference);
            if (!ObjectUtil.isNotEmpty(tempUnitList)) {
                let unit = unitList.filter(k => k.originalFlag)[0];
                //调差信息
                let rcjDifference = unit.rcjDifference;
                let rengong = {"1": "人工调差"}
                rengong.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.RENGONG.code);
                itemList.push(rengong);
                let cailiao = {"2": "材料调差"}
                cailiao.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.CAILIAO.code);
                itemList.push(cailiao);
                let zangujia = {"8": "暂估价材料调差"}
                zangujia.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code);
                itemList.push(zangujia);

                let jixie = {"3": "机械调差"}
                jixie.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.JIXIE.code);
                itemList.push(jixie);
            }
            itemList.push({"21": "合同外所有人材机", type: 2});
            if (ObjectUtil.isNotEmpty(list)) {
                itemList.push({"20": "价差"});
            }

        }


        if (levelType === 3) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            if (unit.originalFlag) {
                itemList.push({"0": "所有人材机"});
                //调差信息
                let rcjDifference = unit.rcjDifference;
                if (!ObjectUtils.isEmpty(rcjDifference)) {
                    let rengong = {"1": "人工调差"}
                    rengong.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.RENGONG.code);
                    itemList.push(rengong);
                    let cailiao = {"2": "材料调差"}
                    cailiao.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.CAILIAO.code);
                    itemList.push(cailiao);

                    let zangujia = {"8": "暂估价材料调差"}
                    zangujia.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code);
                    itemList.push(zangujia);

                    let jixie = {"3": "机械调差"}
                    jixie.info = rcjDifference.find(k => k.kind == JieSuanRcjDifferenceEnum.JIXIE.code);
                    itemList.push(jixie);
                }
            } else {
                itemList.push({"0": "所有人材机"});
                itemList.push({"1": "人工"});
                itemList.push({"2": "材料"});
                itemList.push({"3": "机械"});
                itemList.push({"4": "设备"});
                itemList.push({"5": "主材"});
                itemList.push({"6": "预拌混凝土"});
                if (!unit.originalFlag && unit.isDifference) {
                    itemList.push({"20": "价差"});
                }
                // itemList.push({"7": "主要材料表"});
                // itemList.push({"8": "暂估价材料表"});
                // itemList.push({"9": "发包人供应材料和设备"});
                // itemList.push({"10": "承包人供应材料和设备"});

            }


        }
        treeListVO.itemList = itemList;
        return treeListVO;
    }


    /**
     * 单位下人材机汇总查询
     */
    async unitRcjQuery(args) {

        let {constructId, singleId, unitId, kind, num} = args;
        //获取当前单位
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //获取费用定额的ID
        let costDeIdList = this._getCostDeIdList(unit);

        //材料父级
        let copyRcjs = ObjectUtil.cloneDeep(unit.constructProjectRcjs);
        if (ObjectUtil.isEmpty(copyRcjs)) {
            return copyRcjs;
        }

        //材料子级
        let childDetails = ObjectUtil.cloneDeep(unit.rcjDetailList);
        childDetails.forEach(k => {
            //费用定额人材机以及二次解析的父级材料
            if (k.ifProvisionalEstimate ==1) {
                //置灰标识
                k.isGray = true;
            }
        });
        copyRcjs.forEach(k => {
            //费用定额人材机以及二次解析的父级材料
            //if (costDeIdList.includes(k.deId) || (k.markSum === 1 && (k.levelMark === 1 || k.levelMark === 2)) || k.ifProvisionalEstimate ==1) {
            if (costDeIdList.includes(k.deId) || (k.markSum === 1 && (k.levelMark == 1 || k.levelMark == 2)) ) {
                //置灰标识
                k.isGray = true;
            }
            ;
            //是否二次解析（如果二次解析需要将子级材料也填充进来）
            if (k.markSum === 1 && ObjectUtil.isNotEmpty(childDetails)) {


                let childRcjs = childDetails.filter(i => i.rcjId === k.sequenceNbr);
                copyRcjs.push(...childRcjs);
            }
        });

        //根据类型筛选
        if (kind !== 0) {
            //   【材料调差】：所有类型为“材料”、“主材”、“设备”、“商砼”、“浆”、“商浆”、“配比”且是否调差为"是"、是否暂估为"否"且是否甲供为"否"的所有人材机数据行
            if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {
                //人材机类型
                let rcjType = [2, 4, 5, 6, 8, 9, 10];
                copyRcjs = copyRcjs.filter(i => rcjType.includes(i.kind));
            } else if (JieSuanRcjDifferenceEnum.ZANGUJIA.code == kind) {
                // 【暂估价调差】所有是否暂估为"是"的人材机数据行 （包含暂估甲供材）；
                copyRcjs = copyRcjs.filter(i => i.ifProvisionalEstimate == 1 || i.ifDonorMaterial ==1);
            } else if (20 == kind) {
                //   【价差】
                copyRcjs = copyRcjs.filter(i => i.isDifference);
            } else {
                copyRcjs = copyRcjs.filter(i => i.kind == kind);
            }

            if (unit.originalFlag) {
                if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {
                    //人材机类型
                    copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1 && i.ifDonorMaterial != 1);
                }
                //   【人工调差】：所有类型 为“人工”且是否调差为"是"、是否暂估为"否"、是否甲供为"否"的所有人材机数据行
                if (JieSuanRcjDifferenceEnum.RENGONG.code == kind) {
                    //copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1 && i.ifDonorMaterial != 1);
                    copyRcjs = copyRcjs.filter(i=>!costDeIdList.includes(i.deId));
                }
                //    【机械调差】所有类型为“机械”且是否调差为"是"、是否暂估为"否"的所有人材机数据行；
                if (JieSuanRcjDifferenceEnum.JIXIE.code == kind) {
                    copyRcjs = copyRcjs.filter(i => i.isDifference && i.ifProvisionalEstimate != 1);
                }
            }
        }

        //根据 材料编码、类型、名称、规格型号、单位、合同预算价去重
        //分组
        let rcjGroup = this._getRcjGroup(copyRcjs);

        let newRcjList = new Array();
        //人材机数据封装
        this._getRcjListDataHandler(newRcjList, rcjGroup, constructId, singleId, unitId, num);

        /**
         * 人材机排序规则：
         * 1. 整体【非置灰数据】排在【置灰数据】前面
         * 2.【 非置灰数据】内排序顺序：人工、材料、主材、机械
         * 3.【 置灰数据】内排序顺序：人工、材料、主材、机械、二次解析父级材料
         */
        let sortRcjList = new Array();
        //非置灰
        let noGray = newRcjList.filter(i => !i.isGray).sort((a, b) => {
            if (a.kind === b.kind) {
                return a.materialCode.localeCompare(b.materialCode)
            }
            return a.kind - b.kind;
        });

        //置灰
        let ercijiexi = newRcjList.filter(i => i.isGray).filter(i => i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2));//二次解析
        let feiercijiexi = newRcjList.filter(i => i.isGray).filter(i => !(i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2))).sort((a, b) => {
            return a.kind - b.kind;
        });//非二次解析
        sortRcjList.push(...noGray);
        sortRcjList.push(...feiercijiexi);
        sortRcjList.push(...ercijiexi);


        if(!ObjectUtils.isEmpty(sortRcjList)){
            //暂估置灰
            for (let sortRcjListElement of sortRcjList) {
                if (sortRcjListElement.ifProvisionalEstimate ==1 || sortRcjListElement.ifDonorMaterial  ==1){
                    sortRcjListElement.isDifference = true;
                }

                if (sortRcjListElement.outputToken != 1){
                    sortRcjListElement.outputToken =2;
                }
            }
        }

        //过滤 临时删除
        sortRcjList = sortRcjList.filter(i=>!(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag));

        let parm ={};
        parm.constructId =constructId;
        parm.singleId    =singleId ;
        parm.unitId      =unitId;
        parm.rcjList      =sortRcjList;
        parm.clType = kind;
        parm.fqNum = num;

        //统一处理人材机 调差
        await this.service.jieSuanProject.jieSuanRcjStageService.jieSuanRcjListTongYiTiaoCha(parm);

        return sortRcjList;
    }

    /**
     * 人材机根据几要素分组
     * @private
     */
    _getRcjGroup(rcjs) {
        //拼接相同材料
        for (let arrayElement of rcjs) {
            arrayElement.tempcol =this._indexJoint(3,arrayElement);
        }
        //分组
        return rcjs.reduce((accumulator, currentValue) => {
            // 将分组作为对象的 key，相同分组的项放入同一个数组
            (accumulator[currentValue.tempcol] = accumulator[currentValue.tempcol] || []).push(currentValue);
            return accumulator;
        }, {});
    }


    /**
     * 获取费用定额的ID
     * @private
     */
    _getCostDeIdList(unit) {
        //获取当前单位的费用定额ID
        let costDeIdList = [];
        let costDeList = [DePropertyTypeConstant.AWF_DE, DePropertyTypeConstant.ZJCS_DE, DePropertyTypeConstant.CG_DE, DePropertyTypeConstant.AZ_DE];
        if (ObjectUtil.isNotEmpty(unit.itemBillProjects)) {
            let itemBillProjectsIds = unit.itemBillProjects.filter(k => costDeList.includes(k.isCostDe)).map(k => k.sequenceNbr);
            costDeIdList.push(...itemBillProjectsIds);
        }
        if (ObjectUtil.isNotEmpty(unit.measureProjectTables)) {
            let measureProjectTablesIds = unit.measureProjectTables.filter(k => costDeList.includes(k.isCostDe)).map(k => k.sequenceNbr);
            costDeIdList.push(...measureProjectTablesIds);
        }
        return costDeIdList;
    }


    /**
     * 工程项目级别的人材机列表数据
     */
    async projectRcjList(args) {
        let {constructId, type, kind} = args;


        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;
        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }

        //获取所有的单位
        let unitList = PricingFileFindUtils.getUnitList(constructId);

        //单位集合
        let tempUnitList = [];
        //材料集合
        let tempRcjArray = new Array();
        if (type == 1) {
            //合同内单位
            tempUnitList = unitList.filter(k => k.originalFlag);
            this._rcjMerge(tempRcjArray, tempUnitList);
        }
        if (type == 2) {
            //合同外单位
            tempUnitList = unitList.filter(k => !k.originalFlag);
            this._rcjMerge(tempRcjArray, tempUnitList);
        }

        //去重 材料编码（去除#及后字符）、类型、名称、规格型号、单位、合同预算价、合同/确认单价、是否甲供、保管费率%、是否暂估、是否调差、二次解析情况
        tempRcjArray = Array.from(tempRcjArray.reduce((map, k) => {
            //材料编码（去除#及后字符）
            let tempMaterialCode = k.materialCode.includes('#') ? k.materialCode.split('#')[0] : k.materialCode;
            let tempKey = tempMaterialCode +
                (ObjectUtils.isNotEmpty(k.kind) ? k.kind : "")+
                (ObjectUtils.isNotEmpty(k.materialName) ? k.materialName : "")+
                (ObjectUtils.isNotEmpty(k.specification) ? k.specification : "")+
                (ObjectUtils.isNotEmpty(k.unit) ? k.unit : "")+
                (ObjectUtils.isNotEmpty(k.dePrice) ? k.dePrice : "")+
                (ObjectUtils.isNotEmpty(k.marketPrice) ? k.marketPrice : "")+
                (ObjectUtils.isNotEmpty(k.ifDonorMaterial) ? k.ifDonorMaterial : "")+
                (ObjectUtils.isNotEmpty(k.jieSuanAdminRate) ? k.jieSuanAdminRate : "")+
                (ObjectUtils.isNotEmpty(k.isDifference) ? k.isDifference : "")+
                (ObjectUtils.isNotEmpty(k.markSum) ? k.markSum : "")+
                (ObjectUtils.isNotEmpty(k.ifProvisionalEstimate) ? k.ifProvisionalEstimate : "");
            if (type == 1) {
                //合同内
                tempKey = tempKey.concat(ObjectUtil.isNotEmpty(k.ifProvisionalEstimate) ? k.ifProvisionalEstimate : "");
            }
            k.tempcol = tempKey;
            if (!map.has(tempKey)) map.set(tempKey, k);
            return map;
        }, new Map()).values());


        //根据类型筛选
        if (type == 1 && kind !== 0) {
            //   1. 【人工调差】
            if (JieSuanRcjDifferenceEnum.RENGONG.code == kind) {
                //tempRcjArray = tempRcjArray.filter(i => i.kind == kind && i.isDifference);

                let deIdList =[];
                if (!ObjectUtils.isEmpty(tempUnitList)){
                    for (let tempUnitListElement of tempUnitList) {
                          let getCostDeIdList = this._getCostDeIdList(tempUnitListElement);
                          deIdList = deIdList.concat(getCostDeIdList);
                    }
                }

                tempRcjArray = tempRcjArray.filter(i => i.kind == kind && !deIdList.includes(i.deId));
            }
            //   2. 【材料调差】：
            if (JieSuanRcjDifferenceEnum.CAILIAO.code == kind) {

                //人材机类型
                let rcjType = [2, 4, 6, 8, 9, 10];
                tempRcjArray = tempRcjArray.filter(i => rcjType.includes(i.kind )&& i.isDifference);
            }
            //    3. 【机械调差】
            if (JieSuanRcjDifferenceEnum.JIXIE.code == kind) {
                tempRcjArray = tempRcjArray.filter(i => i.kind == kind && i.isDifference);
            }
            //    4. 【暂估价调差】
            if (JieSuanRcjDifferenceEnum.ZANGUJIA.code == kind) {
                tempRcjArray = tempRcjArray.filter(i=> i.ifProvisionalEstimate == 1);
            }
            //   【价差】
            if (20 == kind) {
                tempRcjArray = tempRcjArray.filter(i => i.isDifference);
            }
        }
        this.highlightHandle(tempRcjArray)

        /**
         * 人材机排序规则：
         * 1. 整体【非置灰数据】排在【置灰数据】前面
         * 2.【 非置灰数据】内排序顺序：人工、材料、主材、机械
         * 3.【 置灰数据】内排序顺序：人工、材料、主材、机械、二次解析父级材料
         */
        let sortRcjList = new Array();
        //非置灰
        let noGray = tempRcjArray.filter(i => !i.isGray).sort((a, b) => {
            if (a.kind === b.kind) {
                return a.materialCode.localeCompare(b.materialCode)
            }
            return a.kind - b.kind;
        });
        //置灰


        let ercijiexi = tempRcjArray.filter(i => i.isGray).filter(i => i.markSum == 1 && (i.levelMark == 1 || i.levelMark == 2));//二次解析
        let feiercijiexi = tempRcjArray.filter(i => i.isGray).filter(i => !(i.markSum == 1 && (i.levelMark == 1 || i.levelMark == 2))).sort((a, b) => {
            return a.kind - b.kind;
        });//非二次解析

        sortRcjList.push(...noGray);
        sortRcjList.push(...feiercijiexi);
        sortRcjList.push(...ercijiexi);


        //工程项目 数据处理
        for (let sortRcjListElement of sortRcjList) {
            if (sortRcjListElement.ifProvisionalEstimate  ==1){
                //置灰标识
                sortRcjListElement.isGray = true;
            }

            if (sortRcjListElement.outputToken != 1){
                sortRcjListElement.outputToken =2;
            }
            //计税判断
            if (!simple) {
                //一般
                let decimal = NumberUtil.multiplyToString(sortRcjListElement.taxRemoval, 0.01);
                sortRcjListElement.jxTotal = NumberUtil.multiplyToString(sortRcjListElement.total, decimal, 2);
            } else {
                //简易
                sortRcjListElement.jxTotal = 0;
            }
            //结算进项税额
            sortRcjListElement.jieSunJxTotal = sortRcjListElement.jxTotal
        }


        if (kind != 21){
            //合同内数据
            for (let sortRcjListElement of sortRcjList) {
                //处理 人材机
                if (!ObjectUtils.isEmpty(sortRcjListElement.jieSuanRcjDifferenceTypeList)){
                    let jieSuanRcjDifferenceTypeList = sortRcjListElement.jieSuanRcjDifferenceTypeList[0];
                    //基期价
                    sortRcjListElement.jieSuanBasePrice = jieSuanRcjDifferenceTypeList.jieSuanBasePrice;
                    //基期价来源
                    sortRcjListElement.jieSuanBasePriceSource = jieSuanRcjDifferenceTypeList.jieSuanBasePriceSource;

                    if (!ObjectUtils.isEmpty(jieSuanRcjDifferenceTypeList)){
                        let jieSuanDifferencePriceListElement = jieSuanRcjDifferenceTypeList.jieSuanDifferencePriceList[0];
                        //结算单价
                        sortRcjListElement.jieSuanPrice = jieSuanDifferencePriceListElement.jieSuanPrice;
                        //结算单价来源
                        sortRcjListElement.jieSuanPriceSource = jieSuanDifferencePriceListElement.jieSuanPriceSource;

                    }
                }

                //结算价差进项税额
                sortRcjListElement.settlementPriceDifferencInputTax = NumberUtil.numberScale(NumberUtil.multiply(sortRcjListElement.priceDifferencSum
                    , NumberUtil.divide100(sortRcjListElement.jieSuanTaxRemoval)), 2);

                //基期价浮动率(%)
                sortRcjListElement.basePriceFloatRate = null;
                if (Number(sortRcjListElement.marketPrice) !== 0) {
                    let divide = NumberUtil.divide(NumberUtil.subtract(sortRcjListElement.jieSuanBasePrice, sortRcjListElement.marketPrice), sortRcjListElement.marketPrice);
                    if (divide != 0) {
                        sortRcjListElement.basePriceFloatRate = NumberUtil.numberScale2(NumberUtil.multiply(divide,100));
                    }
                }

                //单价涨/跌幅(%)  单价涨/跌幅=（结算单价-基期价）/基期价*100%
                sortRcjListElement.jieSuanPriceLimit = NumberUtil.numberScale2(NumberUtil.multiply(
                    NumberUtil.divide(NumberUtil.subtract(sortRcjListElement.jieSuanStagePrice, sortRcjListElement.jieSuanBasePrice), sortRcjListElement.jieSuanBasePrice),100));

                //单价价差
                sortRcjListElement.jieSuanPriceDifferenc =NumberUtil.numberScale2(NumberUtil.subtract());

                sortRcjListElement.jieSuanDifferenceQuantity = sortRcjListElement.jieSuanTotalNumber;
                //结算价差合计  价差合计=单位价差*调差工程量
                sortRcjListElement.jieSuanPriceDifferencSum = NumberUtil.multiply(sortRcjListElement.jieSuanPriceDifferenc, sortRcjListElement.jieSuanDifferenceQuantity);
            }
        }


        let parm ={};
        parm.constructId =constructId;

        parm.rcjList      =sortRcjList;

        //统一处理人材机 调差
        await this.service.jieSuanProject.jieSuanRcjStageService.jieSuanRcjListTongYiTiaoCha(parm);

        return sortRcjList;
    }

    /**
     * 子父集材料条件合并
     * @param tempRcjArray
     * @param unitList
     */
    _rcjMerge(tempRcjArray, unitList) {
        //所有单位的材料集合
        unitList.forEach(k => {
            //获取费用定额的ID
            let costDeIdList = this._getCostDeIdList(k);

            //材料父级
            let copyRcjs = k.constructProjectRcjs;
            if (ObjectUtil.isNotEmpty(copyRcjs)) {
                //过滤临时删除
                copyRcjs = copyRcjs.filter(i=>!(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag));
                for (let i = 0; i < copyRcjs.length; i++) {
                    let copyRcj = copyRcjs[i];
                    //二次解析的父级材料
                    if (copyRcj.markSum == 1 && (copyRcj.levelMark == 1 || copyRcj.levelMark == 2)) {
                        //材料子级
                        let childRcjs = k.rcjDetailList.filter(i => i.rcjId === copyRcj.sequenceNbr && !(!ObjectUtils.isEmpty(i.tempDeleteFlag) && i.tempDeleteFlag));
                        //置灰标识
                        copyRcj.isGray = true;


                        tempRcjArray.push(...childRcjs);
                        //continue;
                    }
                    //置灰
                    /*if (copyRcj.markSum != 1) {
                        copyRcj.isGray = true;
                    }*/
                    tempRcjArray.push(copyRcj);
                }

                for (let tempRcjArrayElement of tempRcjArray) {
                    if (costDeIdList.includes(tempRcjArrayElement.deId) ){
                        tempRcjArrayElement.isGray = true;
                    }
                }
            }
        });

    }

    /**
     * 结算 获取 主要材料设备和工程设备一览表数据
     */
    getJieSuanRcjBBzyclgc(args){
        let {constructId, singleId, unitId, methodType} = args;
        args.kind =2;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtil.isEmpty(projectObjById)){
            return null;
        }

        let clType = 2
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let rcjDifference = unit.rcjDifference;

        for (const obj of rcjDifference) {
            if (obj.kind == clType) {
                if (obj.rcjDifferenceType !=methodType){
                    return null;
                }
            }
        }

        return this.unitRcjQuery(args);

    }


    /**+
     * 高亮处理
     * @param tempRcjArray
     */
    highlightHandle(tempRcjArray) {
        for (const tempRcj of tempRcjArray  ){
            if (ObjectUtil.isNotEmpty(tempRcj.marketPrice)&& ObjectUtil.isNotEmpty(tempRcj.jieSuanPrice) && parseFloat(tempRcj.jieSuanPrice) !== tempRcj.marketPrice) {
                tempRcj.highlight = true;
            } else {
                tempRcj.highlight = false;
            }
        }

    }

    /**
     * 人材机列表数据处理
     * @private
     */
    _getRcjListDataHandler(array, rcjGroup, constructId, singleId, unitId, num) {

        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let unithtn =true;
        if (unit.originalFlag !=true){
            unithtn = false;
        }

        //计税对象
        let projectTaxCalculation = projectObjById.projectTaxCalculation;

        //计税方式
        let taxCalculationMethod = projectTaxCalculation.taxCalculationMethod;
        let simple = false;
        //简易计税
        if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            simple = true;
        }
        //查询各期材料的调差工程量
        let rcjStageList = this.service.jieSuanProject.jieSuanRcjStageService.getRcjStageList({
            "constructId": constructId,
            "singleId": singleId,
            "unitId": unitId,
            "kind": null
        });
        let unitCostSummary = PricingFileFindUtils.getUnitCostSummary(constructId, singleId, unitId);

        let unitRcjDifference = unit.rcjDifference;

        let map = new Map();

        if (!ObjectUtil.isEmpty(num)){
            map = this.jieSuanFenQiRcjTotalNumber(constructId, singleId, unitId);
        }

        for (let group in rcjGroup) {
            if (rcjGroup.hasOwnProperty(group)) {
                //取第一条数据
                let groupedElement = rcjGroup[group][0];
                let type = groupedElement.kind;
                //人材机类型
                if ([2, 4, 5, 6, 8, 9, 10].includes(type)) {
                    //人材机类型
                    type = JieSuanRcjDifferenceEnum.CAILIAO.code;
                }
                if (groupedElement.ifProvisionalEstimate == 1) {
                    //人材机类型
                    type = JieSuanRcjDifferenceEnum.ZANGUJIA.code;
                }


                let number = 0;
                let jsnumber = 0;
                rcjGroup[group].forEach(item => {
                    //结算数量
                    number = NumberUtil.add(number, item.totalNumber)
                });
                rcjGroup[group].forEach(item => {
                    //合同数量
                    jsnumber = NumberUtil.add(jsnumber, item.jieSuanTotalNumber);
                });
                //前端展示的结算数量
                groupedElement.totalNumber = NumberUtil.numberScale(number, 4);


                //前端展示的合同数量
                groupedElement.jieSuanTotalNumber = NumberUtil.numberScale(jsnumber, 4);
                /*if (unithtn == false){
                    groupedElement.totalNumber = groupedElement.jieSuanTotalNumber;

                }*/


                groupedElement.rcjDetailsDTOs = null;



                //let sum = rcjGroup[group].reduce((accumulator, currentValue) => Number(accumulator) + Number(currentValue.jieSuanTotalNumber), 0);
                //调差工程量
                //杨帆说 调差工程量等于 合同数量
                //沈磊说 调差工程量等于  结算数量
                //groupedElement.jieSuanDifferenceQuantity = NumberUtil.numberScale(sum, 6);
                //groupedElement.jieSuanDifferenceQuantity = groupedElement.jieSuanTotalNumber;
                //结算价差合计  价差合计=单位价差*调差工程量
                //groupedElement.jieSuanPriceDifferencSum = NumberUtil.multiply(groupedElement.jieSuanPriceDifferenc, groupedElement.jieSuanDifferenceQuantity);

                //合同内的数据
                if (ObjectUtil.isNotEmpty(unitRcjDifference)) {
                    //获取当前人材机的调整法
                    let item = unitRcjDifference.find(k => k.kind == type);

                    //获取人材机的调整法集合
                    if (!ObjectUtil.isEmpty(groupedElement.jieSuanRcjDifferenceTypeList) && !ObjectUtils.isEmpty(item)) {
                        let rcjDifference = groupedElement.jieSuanRcjDifferenceTypeList.find(k => k.rcjDifferenceType == item.rcjDifferenceType);

                        //基期价
                        groupedElement.jieSuanBasePrice = rcjDifference.jieSuanBasePrice;
                        //基期价来源
                        groupedElement.jieSuanBasePriceSource = rcjDifference.jieSuanBasePriceSource;
                        //结算单价
                        groupedElement.jieSuanPrice = rcjDifference.jieSuanDifferencePriceList[0].jieSuanPrice;
                        //结算单价来源
                        groupedElement.jieSuanPriceSource = rcjDifference.jieSuanDifferencePriceList[0].jieSuanPriceSource;
                        //第N期单位价差
                        groupedElement.jieSuanUnitPriceDifferenc = groupedElement.jieSuanPriceDifferenc;
                        //第N期价差合计
                        groupedElement.jieSuanStagePriceDifferencSum = groupedElement.jieSuanPriceDifferencSum;
                        //第N期涨跌幅
                        groupedElement.jieSuanPriceLimit = groupedElement.jieSuanPriceLimit;

                        //各期价格处理
                        if (ObjectUtil.isNotEmpty(num)) {
                            if (ObjectUtil.isNotEmpty(rcjStageList)) {
                                //结算合价
                                groupedElement.total = NumberUtil.numberScale2(NumberUtil.multiply(groupedElement.totalNumber, groupedElement.jieSuanPrice));

                                //结算第n期调差工程量
                                let rcj = rcjStageList.find(k => k.materialCode == groupedElement.materialCode);
                                //groupedElement.jieSuanStageDifferenceQuantity = rcj[JieSuanConstantUtil.COLUMN + num];
                                if (!ObjectUtil.isEmpty(map)){
                                    let v = map.get(groupedElement.materialCode+num);
                                    v = !ObjectUtils.isEmpty(v)?v:0;
                                    groupedElement.totalNumber = v;
                                    groupedElement.jieSuanDifferenceQuantity = groupedElement.totalNumber;
                                    groupedElement.jieSuanStageDifferenceQuantity = groupedElement.jieSuanDifferenceQuantity;

                                }
                                let rcjStageSet = unit.rcjStageSet;
                                let sum = 0;
                                for (let i = 0; i < rcjStageSet.periods; i++) {
                                    let index = i + 1;
                                    sum = sum + rcj[JieSuanConstantUtil.COLUMN + index];
                                }
                                //调差工程量
                                groupedElement.jieSuanStageDifferenceQuantity = groupedElement.jieSuanDifferenceQuantity;
                                //变值权重B  B=a材料合同合价/工程造价下合同金额
                                let price = unitCostSummary.find(k => k.type == '工程造价').jieSuanPrice;
                                groupedElement.jieSuanValuetWeightB = NumberUtil.numberScale(NumberUtil.divide(groupedElement.jieSuanTotal, price),4);

                                //变值权重B分期数
                                let frequencyList = item.frequencyList;
                                for (let i = 0; i < frequencyList.length; i++) {
                                    let index = i + 1;
                                    let temp = rcj[JieSuanConstantUtil.COLUMN + index];
                                    let scope = frequencyList[i].scope;

                                    if (ObjectUtil.isNotEmpty(scope)) {
                                        let start = scope[0];
                                        let end = scope[1];
                                        temp = 0;
                                        this.findNumbersBetween(Number(start), Number(end)).forEach(k => {
                                            temp = temp + rcj[JieSuanConstantUtil.COLUMN + k]
                                        });
                                    }
                                    temp = NumberUtil.multiply(NumberUtil.divide(temp, rcj.jieSuanTotalNumber), groupedElement.jieSuanValuetWeightB)
                                    groupedElement.jieSuanStageValuetWeightB.push(temp);
                                }
                            }
                            //结算单价
                            //groupedElement.jieSuanPrice = null;
                            //第i期单价
                            groupedElement.jieSuanStagePrice = rcjDifference.jieSuanDifferencePriceList[num - 1].jieSuanPrice;
                            //第i期单价来源
                            groupedElement.jieSuanStagePriceSource = rcjDifference.jieSuanDifferencePriceList[num - 1].jieSuanPriceSource;

                            //载价后的高亮显示
                            groupedElement.highlight = null;
                            if (ObjectUtil.isNotEmpty(groupedElement.highlightList)) {
                                groupedElement.highlight = groupedElement.highlightList[num - 1];
                            }

                            //第n期单价涨/跌幅(%)  单价涨/跌幅=（结算单价-基期价）/基期价*100%
                            //groupedElement.jieSuanPriceLimit = NumberUtil.divide(NumberUtil.subtract(groupedElement.jieSuanStagePrice, groupedElement.jieSuanBasePrice), groupedElement.jieSuanBasePrice);

                            //结算第n期除税系数
                            let jieSuanStagetaxRemovalListElement = groupedElement.jieSuanStagetaxRemovalList[num - 1];
                            if (ObjectUtils.isEmpty(jieSuanStagetaxRemovalListElement)){
                                jieSuanStagetaxRemovalListElement = groupedElement.taxRemoval;
                            }
                            groupedElement.jieSuanStagetaxRemoval = jieSuanStagetaxRemovalListElement;

                            groupedElement.taxRemoval = groupedElement.jieSuanStagetaxRemoval;

                            //结算第n期单位价差
                            //groupedElement.jieSuanUnitPriceDifferenc = groupedElement.jieSuanUnitPriceDifferencList[num - 1];

                            //结算第n期价差合计    第n期价差合计 = 第n期单位价差*第n期调差工程量
                            //groupedElement.jieSuanStagePriceDifferencSum = NumberUtil.multiply(groupedElement.jieSuanUnitPriceDifferenc, groupedElement.jieSuanStageDifferenceQuantity);
                            //结算第n期价差进项税额 第n期价差进项税额=第n期价差合计*第n期除税系数%
                            groupedElement.jieSuanStagePriceDifferencInputTax = NumberUtil.multiply(groupedElement.jieSuanStagePriceDifferencSum, groupedElement.jieSuanStagetaxRemoval);


                        }else {
                            //结算合价
                            groupedElement.total = NumberUtil.numberScale2(NumberUtil.multiply(groupedElement.totalNumber, groupedElement.jieSuanPrice));
                        }
                    }else {
                        //基期价
                        groupedElement.jieSuanBasePrice = groupedElement.marketPrice;
                        //基期价来源
                        groupedElement.jieSuanBasePriceSource = groupedElement.marketSourcePrice;
                        //结算单价
                        groupedElement.jieSuanPrice = groupedElement.marketPrice;
                        //结算单价来源
                        groupedElement.jieSuanPriceSource = groupedElement.marketSourcePrice;

                        //结算合价
                        groupedElement.total = NumberUtil.numberScale2(NumberUtil.multiply(groupedElement.totalNumber, groupedElement.jieSuanPrice));

                    }
                }
                //计算高亮
                /*if (ObjectUtil.isNotEmpty(groupedElement.marketPrice)) {
                    if (ObjectUtil.isNotEmpty(groupedElement.jieSuanBasePrice) && parseFloat(groupedElement.jieSuanBasePrice) !== groupedElement.marketPrice || (parseFloat(groupedElement.jieSuanStagePrice) !== groupedElement.marketPrice && ObjectUtils.isNotEmpty(groupedElement.jieSuanStagePrice))
                        || ObjectUtil.isNotEmpty(groupedElement.jieSuanPrice) && parseFloat(groupedElement.jieSuanPrice) !== groupedElement.marketPrice) {
                        groupedElement.highlight = true;
                    } else {
                        groupedElement.highlight = false;
                    }
                } else {
                    groupedElement.highlight = false;
                }*/

                //结算价差进项税额
                groupedElement.settlementPriceDifferencInputTax = NumberUtil.numberScale(NumberUtil.multiply(groupedElement.jieSuanPriceDifferencSum
                    , NumberUtil.divide100(groupedElement.jieSuanTaxRemoval)), 2);

                //基期价浮动率(%)
                groupedElement.basePriceFloatRate = null;
                if (Number(groupedElement.marketPrice) !== 0) {
                    let divide = NumberUtil.divide(NumberUtil.subtract(groupedElement.jieSuanBasePrice, groupedElement.marketPrice), groupedElement.marketPrice);
                    if (divide != 0) {
                        groupedElement.basePriceFloatRate = NumberUtil.numberScale2(NumberUtil.multiply(divide,100));
                    }
                }

                //变值权重B  B=a材料合同合价/工程造价下合同金额
                let price = unitCostSummary.find(k => k.type == '工程造价').jieSuanPrice;
                groupedElement.jieSuanValuetWeightB = NumberUtil.numberScale(NumberUtil.divide(groupedElement.jieSuanTotal, price),4);

                //单价涨/跌幅(%)  单价涨/跌幅=（结算单价-基期价）/基期价*100%
                //groupedElement.jieSuanPriceLimit = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(groupedElement.jieSuanStagePrice, groupedElement.jieSuanBasePrice), groupedElement.jieSuanBasePrice),100));

                //单价价差
                //groupedElement.jieSuanPriceDifferenc =NumberUtil.numberScale2(NumberUtil.subtract());



                //合同合价
                groupedElement.jieSuanTotal = NumberUtil.numberScale2(NumberUtil.multiply(groupedElement.jieSuanTotalNumber, groupedElement.marketPrice));


                //计税判断
                if (!simple) {
                    //一般
                    let decimal = NumberUtil.multiplyToString(groupedElement.taxRemoval, 0.01);
                    groupedElement.jxTotal = NumberUtil.multiplyToString(groupedElement.total, decimal, 2);
                } else {
                    //简易
                    groupedElement.jxTotal = 0;
                }
                //结算进项税额
                groupedElement.jieSunJxTotal = groupedElement.jxTotal
                array.push(groupedElement);
            }
        }
    }

    /**
     * 结算 计算单位工程 分期人材机合计数量
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<null|Map<any, any>>}
     */
     jieSuanFenQiRcjTotalNumber(constructId, singleId, unitId){
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

        if (ObjectUtils.isEmpty(unit)){
            return null;
        }

        //如果不分期 则不需要计算
        if (ObjectUtils.isEmpty(unit.rcjStageSet) && unit.rcjStageSet.isStage ==false){
            return null;
        }

        //分部分项
        let itemBillProjects = unit.itemBillProjects.getAllNodes();
        //措施项目
        let measureProjectTables = unit.measureProjectTables.getAllNodes();

        //一级人材机
        let constructProjectRcjs = unit.constructProjectRcjs;
        //二级人材机
        let rcjDetailList = unit.rcjDetailList;

        let map = new Map();

        if (!ObjectUtils.isEmpty(itemBillProjects)){
            //循环 清单
            for (let itemBillProject of itemBillProjects) {
                //清单
                if (itemBillProject.kind =="03"){

                    //人材机占比比例记录
                    let blMap = new Map;
                    if (!ObjectUtil.isEmpty(itemBillProject.stageQuantitiesList)){
                        //计算清单分期占比

                        let stageQuantitiesList = itemBillProject.stageQuantitiesList;
                        //按比例分期
                        if (itemBillProject.stageType ==1){
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageQuantity,itemBillProject.quantity));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }else {
                            //按工程量分期
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageQuantity,itemBillProject.quantity));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }


                        let filter = itemBillProjects.filter(i=>i.parentId ==itemBillProject.sequenceNbr);

                        let array = new Array();
                        //清单id
                        array.push(itemBillProject.sequenceNbr);
                        for (let filterElement of filter) {
                            //定额id
                            array.push(filterElement.sequenceNbr);
                        }

                        let constructProjectRcjs1 = constructProjectRcjs.filter(i=>array.includes(i.deId));
                        let rcjDetailList1 = rcjDetailList.filter(i=>array.includes(i.deId));

                        //一级人材机
                        for (let constructProjectRcj of constructProjectRcjs1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }

                        //二级
                        for (let constructProjectRcj of rcjDetailList1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }
                    }
                }
            }
        }

        //措施项目
        if (!ObjectUtils.isEmpty(measureProjectTables)){
            //循环 清单
            for (let itemBillProject of measureProjectTables) {
                //清单
                if (itemBillProject.kind =="03"){

                    //人材机占比比例记录
                    let blMap = new Map;
                    if (!ObjectUtil.isEmpty(itemBillProject.stageQuantitiesList)){
                        //计算清单分期占比

                        let stageQuantitiesList = itemBillProject.stageQuantitiesList;
                        //按比例分期
                        if (itemBillProject.stageType ==1){
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageQuantity,itemBillProject.quantity));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }else {
                            //按工程量分期
                            for (let stageQuan of stageQuantitiesList) {
                                //比例
                                let bl  = NumberUtil.numberScale6(NumberUtil.divide(stageQuan.stageRatio,100));
                                blMap.set(stageQuan.stage,bl);
                            }
                        }


                        let filter = itemBillProjects.filter(i=>i.parentId ==itemBillProject.sequenceNbr);

                        let array = new Array();
                        //清单id
                        array.push(itemBillProject.sequenceNbr);
                        for (let filterElement of filter) {
                            //定额id
                            array.push(filterElement.sequenceNbr);
                        }

                        let constructProjectRcjs1 = constructProjectRcjs.filter(i=>array.includes(i.deId));
                        let rcjDetailList1 = rcjDetailList.filter(i=>array.includes(i.deId));

                        //一级人材机
                        for (let constructProjectRcj of constructProjectRcjs1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }

                        //二级
                        for (let constructProjectRcj of rcjDetailList1) {
                            let materialCode = constructProjectRcj.materialCode;
                            let totalNumber = constructProjectRcj.totalNumber;
                            for (let stageQuan of stageQuantitiesList) {
                                //获取比例
                                let v = blMap.get(stageQuan.stage);

                                //计算工程量
                                let number = NumberUtil.numberScale6(NumberUtil.multiply(totalNumber,v));

                                let rcjZongTotalNumber = map.get(materialCode+stageQuan.stage);

                                if (ObjectUtil.isEmpty(rcjZongTotalNumber)){
                                    map.set(materialCode+stageQuan.stage,number);
                                }else {
                                    map.set(materialCode+stageQuan.stage,NumberUtil.add(rcjZongTotalNumber,number));
                                }
                            }
                        }
                    }
                }
            }
        }

        return map;
    }




    /**
     * 人材机汇总选择
     * @param args
     */
    async unitRcjCollectSelect(args) {
        let {constructId, singleId, unitId, levelType, kind, name, type} = args;

        let param = {};
        param.constructId = constructId;
        param.singleId = singleId;
        param.unitId = unitId;
        param.kind = 0;
        param.type = type;
        //条件处理
        if (kind.includes(2)) {
            kind.push(...[4, 6, 8, 9, 10])
        }

        let unitRcjQuery = [];
        if (levelType == 3) {
            //获取单位级别人材机数据
            unitRcjQuery = await this.unitRcjQuery(param);
        }
        if (levelType == 1) {
            //获取项目级别人材机数据
            unitRcjQuery = await this.projectRcjList(param);
        }
        //根据类型筛选
        unitRcjQuery = unitRcjQuery.filter(k => kind.includes(k.kind) && k.ifProvisionalEstimate != 1 && k.ifDonorMaterial != 1 && !k.isGray);
        //根据名称筛选
        if (ObjectUtil.isNotEmpty(name)) {
            unitRcjQuery = unitRcjQuery.filter(k => k.materialName.includes(name) || k.materialCode.includes(name))
        }
        return ResponseData.success(unitRcjQuery);

    }


    /**
     * 自动过滤调差材料
     */
    async filterDifferenceRcj(args) {
        let {constructId, singleId, unitId, type, value} = args;

        //获取当前单位
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        if (ObjectUtils.isEmpty(unit)){
            return ;
        }

        //获取费用定额的ID
        let costDeIdList = this._getCostDeIdList(unit);


        let param = {};
        param.constructId = constructId;
        param.singleId = singleId;
        param.unitId = unitId;


        let isDifferenceTrue =[];

        let noZcSheBeiFalse = [];

        if (type == 1) {
            param.kind = 0;
            let unitRcjQuery = await this.unitRcjQuery(param);
            if (ObjectUtils.isEmpty(unitRcjQuery)){
                return ;
            }
            //过滤掉 暂估 甲供 费用定额材料
            unitRcjQuery = unitRcjQuery.filter(i=>!(i.ifProvisionalEstimate==1 || i.ifDonorMaterial == 1 || costDeIdList.includes(i.deId)) );

            //合同计价文件中主要材料、设备 设置调差
            isDifferenceTrue = unitRcjQuery.filter(k => (k.kind == 4 || k.kind == 5));

            //非主材设备 设置不调差
            noZcSheBeiFalse = unitRcjQuery.filter(i => [2, 6,7,8, 9, 10].includes(i.kind));


        }
        if (type == 2) {
            param.kind = 0;
            let unitRcjQuery =await this.unitRcjQuery(param);
            if (ObjectUtils.isEmpty(unitRcjQuery)){
                return ;
            }

            //过滤掉 暂估 甲供 费用定额材料
            unitRcjQuery = unitRcjQuery.filter(i=>!(i.ifProvisionalEstimate==1 || i.ifDonorMaterial == 1 || costDeIdList.includes(i.deId)) && [2, 6,7,8, 9, 10].includes(i.kind));
            //根据材料在单位工程内的合同市场价合计的数值进行排序

            if (value>unitRcjQuery.length){
                isDifferenceTrue = unitRcjQuery;
            }else {
                unitRcjQuery.sort((a, b) => b.total - a.total);

                //
                isDifferenceTrue = unitRcjQuery.slice(0, Number(value));

                let idList = [];
                isDifferenceTrue.forEach(i=>idList.push(i.sequenceNbr));

                noZcSheBeiFalse = unitRcjQuery.filter(i=>!idList.includes(i.sequenceNbr));

            }

        }
        if (type == 3) {
            param.kind = 0;
            let unitRcjQuery = await this.unitRcjQuery(param);

            if (ObjectUtils.isEmpty(unitRcjQuery)){
                return ;
            }

            //过滤掉 暂估 甲供 费用定额材料
            unitRcjQuery = unitRcjQuery.filter(i=>!(i.ifProvisionalEstimate==1 || i.ifDonorMaterial == 1 || costDeIdList.includes(i.deId))  && [2, 6,7,8, 9, 10].includes(i.kind));
            //根据材料在单位工程内的合同市场价合计的数值占比进行排序
            unitRcjQuery.sort((a, b) => b.total - a.total);
            let sum = unitRcjQuery.reduce((accumulator, currentValue) => accumulator + Number(currentValue.total), 0);
            let valueSum = NumberUtil.multiply(sum, NumberUtil.divide100(Number(value)));

            let tempArray = new Array();
            let temp = 0;
            for (let i = 0; i < unitRcjQuery.length; i++) {
                temp = temp + Number(unitRcjQuery[i].total);
                tempArray.push(unitRcjQuery[i]);
                if (temp >= valueSum) {
                    tempArray.push(unitRcjQuery[i]);
                    break;
                }
            }
            //unitRcjQuery = tempArray;
            isDifferenceTrue = tempArray;

            let idList = [];
            isDifferenceTrue.forEach(i=>idList.push(i.sequenceNbr));

            noZcSheBeiFalse = unitRcjQuery.filter(i=>!idList.includes(i.sequenceNbr));

        }

        let rcjcs ={
            type:2,
            constructId:constructId,
            singleId:singleId,
            unitId:unitId
        };

        if (!ObjectUtils.isEmpty(isDifferenceTrue)){
            for (let differenceTrueElement of isDifferenceTrue) {
                rcjcs.sequenceNbr = differenceTrueElement.sequenceNbr;
                let constructProjectRcj ={
                    isDifference:true
                }
                rcjcs.constructProjectRcj = constructProjectRcj;
                this.service.jieSuanProject.jieSuanRcjStageService.changeRcjNewJieSuan(rcjcs);
            }
        }


        if (!ObjectUtils.isEmpty(noZcSheBeiFalse)){
            for (let differenceTrueElement of noZcSheBeiFalse) {
                rcjcs.sequenceNbr = differenceTrueElement.sequenceNbr;
                let constructProjectRcj ={
                    isDifference:false
                }
                rcjcs.constructProjectRcj = constructProjectRcj;
                this.service.jieSuanProject.jieSuanRcjStageService.changeRcjNewJieSuan(rcjcs);
            }
        }

        //六要素集合
        /*let tempcolList = unitRcjQuery.map(k => k.tempcol);
        for (const item of tempcolList) {
            let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
            if (ObjectUtil.isNotEmpty(rcjList)) {
                rcjList.forEach(k => {
                    if (this._indexJoint(3,k) == item) {
                        k.isDifference = true;
                    }
                })
            }
            let rcjDetailList = PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId);
            if (ObjectUtil.isNotEmpty(rcjDetailList)) {
                rcjDetailList.forEach(k => {
                    if (this._indexJoint(3,k)  == item) {
                        k.isDifference = true;
                    }
                })
            }
        }*/


        return ResponseData.success(true);
    }


    /**
     * 元素拼接
     */
    _indexJoint(levelType,k){
        let tempcol = "";
        if (levelType == 3) {
            tempcol = (ObjectUtils.isNotEmpty(k.materialCode) ? k.materialCode : "")+
                (ObjectUtils.isNotEmpty(k.kind) ? k.kind : "")+
                (ObjectUtils.isNotEmpty(k.materialName) ? k.materialName : "")+
                (ObjectUtils.isNotEmpty(k.specification) ? k.specification : "")+
                (ObjectUtils.isNotEmpty(k.unit) ? k.unit : "")+
                (ObjectUtils.isNotEmpty(k.dePrice) ? k.dePrice : "");
        }
        if (levelType == 1) {
            tempcol = (ObjectUtils.isNotEmpty(k.materialCode) ? k.materialCode : "")+
                (ObjectUtils.isNotEmpty(k.kind) ? k.kind : "")+
                (ObjectUtils.isNotEmpty(k.materialName) ? k.materialName : "")+
                (ObjectUtils.isNotEmpty(k.specification) ? k.specification : "")+
                (ObjectUtils.isNotEmpty(k.unit) ? k.unit : "")+
                (ObjectUtils.isNotEmpty(k.dePrice) ? k.dePrice : "")+
                (ObjectUtils.isNotEmpty(k.marketPrice) ? k.marketPrice : "")+
                (ObjectUtils.isNotEmpty(k.ifDonorMaterial) ? k.ifDonorMaterial : "")+
                (ObjectUtils.isNotEmpty(k.jieSuanAdminRate) ? k.jieSuanAdminRate : "")+
                (ObjectUtils.isNotEmpty(k.isDifference) ? k.isDifference : "")+
                (ObjectUtils.isNotEmpty(k.markSum) ? k.markSum : "")+
                (ObjectUtils.isNotEmpty(k.ifProvisionalEstimate) ? k.ifProvisionalEstimate : "");
        }
        return tempcol;

    }


    /**
     * 获取两个数字之间的所有数字
     * @param num1
     * @param num2
     * @return {[]}
     */
    findNumbersBetween(num1, num2) {
        let start = Math.min(num1, num2);
        let end = Math.max(num1, num2);
        let numbers = [];
        for (let i = start; i <= end; i++) {
            numbers.push(i);
        }
        return numbers;
    }


    /**
     * 人材机汇总选择后的确认
     */
    unitRcjCollectSelectNotarize(args) {
        let {constructId, singleId, unitId, levelType, selectList} = args;
        //六要素集合
        for (const item of selectList) {
            if (levelType == 1) {
                let unitList = PricingFileFindUtils.getUnitList(constructId);
                unitList.forEach(k => {
                    this._onlyValueRcjUpdate(k.constructProjectRcjs, k.rcjDetailList, levelType, item);
                })
            }
            if (levelType == 3) {
                let rcjList = PricingFileFindUtils.getRcjList(constructId, singleId, unitId);
                let rcjDetailList = PricingFileFindUtils.getRcjDetailList(constructId, singleId, unitId);
                this._onlyValueRcjUpdate(rcjList, rcjDetailList, levelType, item);
            }
        }
        return ResponseData.success(true);
    }


    /**
     * 唯一要素人材机修改
     */
    _onlyValueRcjUpdate(rcjList, rcjDetailList, levelType, item) {
        if (ObjectUtil.isNotEmpty(rcjList)) {
            rcjList.forEach(k => {
                if (this._indexJoint(levelType,k) == item.tempcol) {
                    k.isDifference = item.type;
                }
            })
        }
        if (ObjectUtil.isNotEmpty(rcjDetailList)) {
            rcjDetailList.forEach(k => {
                if (this._indexJoint(levelType,k) == item.tempcol) {
                    k.isDifference = item.type;
                }
            })
        }
    }
}

JieSuanRcjProcess.toString = () => '[class JieSuanRcjProcess]';
module.exports = JieSuanRcjProcess;