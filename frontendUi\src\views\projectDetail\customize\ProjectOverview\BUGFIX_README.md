# BasicInfo.vue timeSelect 撤销回退功能修复

## 问题描述

在 `BasicInfo.vue` 组件中，`timeSelect` 函数在处理日期选择时无法正确获取 `oldRemark` 值，导致撤销回退功能无法正常工作。

## 问题原因

1. **参数传递不完整**：`timeSelect` 函数调用 `saveOrUpdateBasicInfo` 时，没有传递 `oldValue` 参数
2. **函数签名不匹配**：`saveOrUpdateBasicInfo` 和 `saveAndUpdateOperate` 函数的参数签名不支持 `oldValue` 参数
3. **撤销记录缺失**：`redo.addnoMatchedRedoList` 缺少 `oldValue` 参数，无法记录修改前的值

## 修复内容

### 1. 修改 `timeSelect` 函数

**文件**: `frontendUi/src/views/projectDetail/customize/ProjectOverview/BasicInfo.vue`

**修改前**:
```javascript
const timeSelect = (row, { $event }) => {
  console.log('timeSelect', $event);
  oldRemark.value = row.remark;
  row['remark'] = $event.value;
  console.log('saveOrUpdateBasicInfo', $event.value, oldRemark.value);
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};
```

**修改后**:
```javascript
const timeSelect = (row, { $event }) => {
  console.log('timeSelect', $event);
  oldRemark.value = row.remark;
  row['remark'] = $event.value;
  console.log('saveOrUpdateBasicInfo', $event.value, oldRemark.value);
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true, $event.value, oldRemark.value, '备注');
};
```

### 2. 修改 `saveOrUpdateBasicInfo` 函数签名

**修改前**:
```javascript
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, title) => {
  let res = await saveAndUpdateOperate(param.data, value, title);
  // ...
};
```

**修改后**:
```javascript
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, oldValue, title) => {
  let res = await saveAndUpdateOperate(param.data, value, oldValue, title);
  // ...
};
```

### 3. 修改 `saveAndUpdateOperate` 函数

**文件**: `frontendUi/src/views/projectDetail/customize/ProjectOverview/comBasiciInfo.js`

**修改前**:
```javascript
const saveAndUpdateOperate = async (data,newValue,title) => {
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
  });
  // ...
};
```

**修改后**:
```javascript
const saveAndUpdateOperate = async (data,newValue,oldValue,title) => {
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
    oldValue,
  });
  // ...
};
```

### 4. 修复其他相关函数调用

同时修复了以下函数中对 `saveOrUpdateBasicInfo` 的调用：

- `averageBlur` 函数
- `inputFinish` 函数  
- `insertHandle` 函数

确保所有调用都传递了正确的参数。

## 测试验证

创建了测试文件 `BasicInfo.test.js` 来验证修复的正确性：

1. **测试 timeSelect 函数**：验证是否正确传递 `oldRemark` 参数
2. **测试 saveAndUpdateOperate 函数**：验证是否正确处理 `oldValue` 参数

## 影响范围

- ✅ 日期选择器的撤销回退功能现在可以正常工作
- ✅ 其他输入控件的撤销回退功能保持正常
- ✅ 不影响现有的保存和更新逻辑

## 相关文件

1. `frontendUi/src/views/projectDetail/customize/ProjectOverview/BasicInfo.vue`
2. `frontendUi/src/views/projectDetail/customize/ProjectOverview/comBasiciInfo.js`
3. `frontendUi/src/hooks/redo.js` (使用现有的 `addnoMatchedRedoList` 方法)

## 验证步骤

1. 打开项目概况页面
2. 选择一个日期字段（如"开工日期"、"竣工日期"等）
3. 修改日期值
4. 使用撤销功能（Ctrl+Z 或撤销按钮）
5. 验证日期是否正确回退到修改前的值

## 注意事项

- 确保在测试时 `oldRemark` 变量能够正确捕获修改前的值
- 撤销功能依赖于 `redo.js` 中的撤销回退机制
- 修改后的函数签名需要与所有调用点保持一致
