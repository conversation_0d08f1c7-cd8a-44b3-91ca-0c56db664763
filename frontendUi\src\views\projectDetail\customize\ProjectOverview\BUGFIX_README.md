# BasicInfo.vue timeSelect 撤销回退功能修复

## 问题描述

在 `BasicInfo.vue` 组件中，`timeSelect` 函数在处理日期选择时存在两个主要问题：

1. **无法正确获取 `oldRemark` 值**：导致撤销回退功能无法正常工作
2. **模板没有被替换为具体的值**：撤销回退显示的是模板字符串而不是实际值
3. **只有 `isChange: true` 时才触发 redo**：撤销功能只在特定条件下才会记录

## 问题原因

1. **参数传递不完整**：`timeSelect` 函数调用 `saveOrUpdateBasicInfo` 时，没有传递 `oldValue` 参数
2. **函数签名不匹配**：`saveOrUpdateBasicInfo` 和 `saveAndUpdateOperate` 函数的参数签名不支持 `oldValue` 参数
3. **撤销记录缺失**：`redo.addnoMatchedRedoList` 缺少 `oldValue` 参数，无法记录修改前的值
4. **模板信息缺失**：`addnoMatchedRedoList` 没有包含模板和 channel 信息
5. **isChange 硬编码**：`isChange` 被硬编码为 `true`，没有根据实际变化情况设置

## 修复内容

### 1. 修改 `timeSelect` 函数

**文件**: `frontendUi/src/views/projectDetail/customize/ProjectOverview/BasicInfo.vue`

**修改前**:
```javascript
const timeSelect = (row, { $event }) => {
  console.log('timeSelect', $event);
  oldRemark.value = row.remark;
  row['remark'] = $event.value;
  console.log('saveOrUpdateBasicInfo', $event.value, oldRemark.value);
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);
};
```

**修改后**:
```javascript
const timeSelect = (row, { $event }) => {
  console.log('timeSelect', $event);
  oldRemark.value = row.remark;
  row['remark'] = $event.value;
  console.log('saveOrUpdateBasicInfo', $event.value, oldRemark.value);
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true, $event.value, oldRemark.value, '备注');
};
```

### 2. 修改 `saveOrUpdateBasicInfo` 函数签名

**修改前**:
```javascript
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, title) => {
  let res = await saveAndUpdateOperate(param.data, value, title);
  // ...
};
```

**修改后**:
```javascript
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, oldValue, title) => {
  let res = await saveAndUpdateOperate(param.data, value, oldValue, title);
  // ...
};
```

### 3. 修改 `saveAndUpdateOperate` 函数

**文件**: `frontendUi/src/views/projectDetail/customize/ProjectOverview/comBasiciInfo.js`

**修改前**:
```javascript
const saveAndUpdateOperate = async (data,newValue,title) => {
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
  });
  // ...
};
```

**修改后**:
```javascript
const saveAndUpdateOperate = async (data,newValue,oldValue,title) => {
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
    oldValue,
  });
  // ...
};
```

### 4. 添加模板和 channel 信息

**修改前**:
```javascript
redo.addnoMatchedRedoList({
  sequenceNbr: data.sequenceNbr,
  columnTitle: title,
  newValue,
  oldValue,
});
```

**修改后**:
```javascript
redo.addnoMatchedRedoList({
  sequenceNbr: data.sequenceNbr,
  columnTitle: title,
  newValue,
  oldValue,
  name: `工程概况 ${title} 由 【{oldValue}】 修改为 【{newValue}】`, // 添加模板
  channel: 'controller.projectOverviewController.saveBasicEngineeringInfoOrEngineeringFeature', // 添加 channel
});
```

### 5. 修复 isChange 条件

**修改前**:
```javascript
let apiData = {
  ...dafaultParams,
  projectOverviewList: toRaw(data),
  isChange: true, // 硬编码
};
```

**修改后**:
```javascript
// 检查是否有实际变化
const hasChange = newValue !== oldValue &&
                 !(newValue === '' && (oldValue === null || oldValue === undefined)) &&
                 !(oldValue === '' && (newValue === null || newValue === undefined));

let apiData = {
  ...dafaultParams,
  projectOverviewList: toRaw(data),
  isChange: hasChange, // 根据实际变化设置
};
```

### 6. 更新 redo.js 和 proRedo.js

在 `frontendUi/src/hooks/redo.js` 和 `frontendUi/src/store/proRedo.js` 中添加对模板和 channel 信息的支持。

### 7. 添加项目概况配置

在 `frontendUi/src/hooks/redoData.js` 中添加项目概况相关的配置：

```javascript
{
  channel: ['controller.projectOverviewController.saveBasicEngineeringInfoOrEngineeringFeature'],
  columnTitle: [
    {
      key: '名称',
      value: '名称',
    },
    {
      key: '备注',
      value: '备注',
    },
  ],
  oldValue: [], // 动态值，不需要映射
  newValue: [], // 动态值，不需要映射
},
```

### 8. 修复其他相关函数调用

同时修复了以下函数中对 `saveOrUpdateBasicInfo` 的调用：

- `averageBlur` 函数
- `inputFinish` 函数
- `insertHandle` 函数

确保所有调用都传递了正确的参数。

## 测试验证

创建了测试文件 `BasicInfo.test.js` 来验证修复的正确性：

1. **测试 timeSelect 函数**：验证是否正确传递 `oldRemark` 参数
2. **测试 saveAndUpdateOperate 函数**：验证是否正确处理 `oldValue` 参数

## 影响范围

- ✅ 日期选择器的撤销回退功能现在可以正常工作
- ✅ 撤销回退显示正确的模板替换值（如："工程概况 备注 由 【2024-01-01】 修改为 【2024-12-31】"）
- ✅ 只有在实际发生变化时才记录撤销操作，避免无效记录
- ✅ 其他输入控件的撤销回退功能保持正常
- ✅ 不影响现有的保存和更新逻辑

## 相关文件

1. `frontendUi/src/views/projectDetail/customize/ProjectOverview/BasicInfo.vue`
2. `frontendUi/src/views/projectDetail/customize/ProjectOverview/comBasiciInfo.js`
3. `frontendUi/src/hooks/redo.js` (使用现有的 `addnoMatchedRedoList` 方法)

## 验证步骤

1. 打开项目概况页面
2. 选择一个日期字段（如"开工日期"、"竣工日期"等）
3. 修改日期值
4. 使用撤销功能（Ctrl+Z 或撤销按钮）
5. 验证日期是否正确回退到修改前的值

## 注意事项

- 确保在测试时 `oldRemark` 变量能够正确捕获修改前的值
- 撤销功能依赖于 `redo.js` 中的撤销回退机制
- 修改后的函数签名需要与所有调用点保持一致
