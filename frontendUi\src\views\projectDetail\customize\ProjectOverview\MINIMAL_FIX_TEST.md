# 最小化修复测试

## 修复内容

### 1. 修复 timeSelect 函数
**文件**: `BasicInfo.vue`
**修改**: 在 `timeSelect` 函数中传递 `oldRemark.value` 参数

```javascript
// 修改前
saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true);

// 修改后  
saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true, $event.value, oldRemark.value, '备注');
```

### 2. 修复 saveOrUpdateBasicInfo 函数签名
**文件**: `BasicInfo.vue`
**修改**: 添加 `oldValue` 参数

```javascript
// 修改前
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, title) => {

// 修改后
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, oldValue, title) => {
```

### 3. 修复 saveAndUpdateOperate 函数
**文件**: `comBasiciInfo.js`
**修改**: 添加 `oldValue` 参数并传递给后端

```javascript
// 修改前
const saveAndUpdateOperate = async (data,newValue,title) => {
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
  });

// 修改后
const saveAndUpdateOperate = async (data,newValue,oldValue,title) => {
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
    oldValue,
  });
  let apiData = {
    ...dafaultParams,
    projectOverviewList: toRaw(data),
    isChange: true,
    // 添加后端注解需要的参数
    columnTitle: title,
    oldValue: oldValue,
    newValue: newValue,
  };
```

## 预期效果

1. **timeSelect 可以获取 oldRemark**: ✅ 现在 `oldRemark.value` 会被正确传递
2. **模板替换**: ✅ 后端注解现在可以获取到 `columnTitle`, `oldValue`, `newValue` 参数
3. **redo 触发**: ✅ 保持原有的 `isChange: true` 逻辑

## 测试步骤

1. 打开项目概况页面
2. 选择一个日期字段（如"开工日期"）
3. 修改日期值（如从 2024-01-01 改为 2024-12-31）
4. 查看撤销列表，应该显示：`"工程概况 备注 由 【2024-01-01】 修改为 【2024-12-31】"`
5. 点击撤销，验证日期是否正确回退

## 修改文件列表

1. `frontendUi/src/views/projectDetail/customize/ProjectOverview/BasicInfo.vue`
   - 第 272 行：修改 `timeSelect` 函数调用
   - 第 334-344 行：修改 `saveOrUpdateBasicInfo` 函数签名

2. `frontendUi/src/views/projectDetail/customize/ProjectOverview/comBasiciInfo.js`
   - 第 61-76 行：修改 `saveAndUpdateOperate` 函数

## 注意事项

- 这是最小化修复，只修改了必要的参数传递
- 保持了原有的 `isChange: true` 逻辑
- 没有修改其他复杂的逻辑，降低了风险
