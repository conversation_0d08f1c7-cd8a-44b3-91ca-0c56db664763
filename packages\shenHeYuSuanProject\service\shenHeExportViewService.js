const {Service} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const ExportSheetNameEnum = require("../enum/ExportSheetNameEnum");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ShenHeExcelUtil} = require("../utils/ShenHeExcelUtil.js");
const {ShenHeWriteExcelBySheetUtil} = require("../utils/ShenHeWriteExcelBySheetUtil.js");
const path = require('path');
const ExcelJS = require('exceljs');
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const ConstructionMeasureTypeConstant = require("../../../electron/enum/ConstructionMeasureTypeConstant");
const ProjectLevelConstant = require("../../../electron/enum/ProjectLevelConstant");
const {ConstructProjectRcj} = require("../../../electron/model/ConstructProjectRcj");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const UtilsPs = require('../../../core/ps');
const AdmZip = require('adm-zip');
const fs = require('fs');
const {
    app: electronApp, dialog, shell, BrowserView, Notification, powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const projectLevelConstant = require("../../../electron/enum/ProjectLevelConstant");
const {ExcelUtil} = require("../../../electron/utils/ExcelUtil");
const {map} = require("rxjs");
const Decimal = require("decimal.js");
const YsshssConstant = require("../enum/YsshssConstant");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const _ = require("lodash");

class ShenHeExportViewService extends Service {
    constructor(ctx) {
        super(ctx);
    }

    //展示报表查看的表名列表
    async showExportHeadLine(itemLevel, args) {
        let result = []

        //定义一个存放数据值和cell的对象
        function HeadLineList(desc, baoBiaoList) {
            this.desc = desc;//存放大标题
            this.baoBiaoList = baoBiaoList;
        }

        //存放大栏目下的表的表名
        let gongChengLiangList = ExportSheetNameEnum.审核报表.filter(function (element) {
            if (element.projectLevel == itemLevel) return element;
        });
        return gongChengLiangList;
    }


    async showSheetStyle(itemLevel, sheetName, args) {
        let unitIs2022= PricingFileFindUtils.getConstructDeStandard(args.constructId)==ConstantUtil.DE_STANDARD_22;
        let shenhe = "";
        if (!unitIs2022) {
            shenhe = this.getProjectRootPath() + "\\excelTemplate\\shenhe\\12";
        }else {  //目前审核暂没有22的

        }
        if (itemLevel == "project") {
            shenhe = shenhe + "\\工程项目层级.xlsx";
        } else if (itemLevel == "single") {
            shenhe = shenhe + "\\单项工程层级.xlsx";
        } else if (itemLevel == "unit") {
            shenhe = shenhe + "\\单位工程层级.xlsx";
        }
        let loadPath = shenhe;

        let workbook = await ShenHeExcelUtil.readToWorkBook(loadPath);
        args["workbook"] = workbook;
        try {
            await this.switchWorkSheet(itemLevel, workbook.getWorksheet(sheetName), args);
        } catch (e) {
            console.log("报表填充数据异常");
        }
        let result;
        try {
            result = await ShenHeExcelUtil.findCellStyleList(workbook.getWorksheet(sheetName));
        } catch (e) {
            console.log("报表填充数据异常");
        }
        return result;
    }

    async queryLanMuData(constructId) {
        let project = [];
        let single = [];
        let unit = [];

        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum.审核报表, "project", project);
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum.审核报表, "single", single);
        this.traverseGetHeadLineAndLeaf(ExportSheetNameEnum.审核报表, "unit", unit);

        let result = {};
        let construct = PricingFileFindUtils.getProjectObjById(constructId);
        if (construct.biddingType == 2) {
            //则为单位工程
            result["headLine"] = construct.constructName;
            // let unitSequenceNbr = construct.unitProject.sequenceNbr;
            // let unitYsshGlId = construct.unitProject.ysshGlId;
            // for (let p = 0; p < unit.length; p++) {
            //     let unitElement = unit[p];
            //     unitElement["id"] = unitSequenceNbr;
            //     unitElement["sdUnitId"] = unitSequenceNbr;
            //     unitElement["ssUnitId"] = unitYsshGlId;
            //     unitElement['levelType'] = 3;
            // }
            result["childrenList"] = unit;
            result["id"] = construct.sequenceNbr;
            result["ssConstructId"] = construct.ysshGlId;
            result['biddingType'] = 2;

            // let args = {};
            // args['levelType'] = 3;
            // args['constructId'] = construct.sequenceNbr;
            // args["sdConstructId"] = construct.sequenceNbr;
            // args["ssConstructId"] = construct.ysshGlId;
            // args['unitId'] = construct.unitProject.sequenceNbr;
            // args['sdUnitId'] = construct.unitProject.sequenceNbr;
            // let taxCalculation = await this.service.shenHeYuSuanProject.baseFeeFileService.taxCalculation(args);
            // //简易计税
            // if (taxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
            //     result["childrenList"].splice(24, 4);
            //     if (lanMuName == "招标项目报表" || lanMuName == "招标项目报表" || lanMuName == "工程量清单报表") {
            //         result["childrenList"].splice(24, 4);
            //     } else {
            //         //如果是其他栏目 报表  舍掉最后两个元素
            //         result["childrenList"].pop();
            //         result["childrenList"].pop();
            //     }
            // }

            //对result进行递归遍历  增加唯一序号
            let object = {};
            object['id'] = 1;
            await this.addOrderNum(result, object);
            return result;
        }
        let newProject = await this.deepCopy(project);
        result["headLine"] = construct.constructName;
        result["childrenList"] = newProject;
        result["id"] = construct.sequenceNbr;
        result["sdConstructId"] = construct.sequenceNbr;
        result["ssConstructId"] = construct.ysshConstructId;
        result["levelType"] = construct.levelType;


        for (let i = 0; i < construct.singleProjects.length; i++) {
            await this.traverseSingleForExport(construct.singleProjects[i],single,unit,newProject);
        }
        //对result进行递归遍历  增加唯一序号
        let object = {};
        object['id'] = 1;
        await this.addOrderNum(result, object);

        // await this.exportExcelZip(lanMuName,result);

        return result;
    }

    //增加子单项及其单位的结构
    async traverseSingleForExport(singleProject,single,unit,result) {

        let singleObject = {};
        singleObject["headLine"] = singleProject.projectName;
        let singleChildren = await this.deepCopy(single);
        singleObject["childrenList"] = singleChildren;
        singleObject["id"] = singleProject.sequenceNbr;
        singleObject["sdSingleId"] = singleProject.sequenceNbr;
        singleObject["ssSingleId"] = singleProject.ysshSingleId;
        singleObject["levelType"] = singleProject.levelType;
        result.push(singleObject);

        if (!ObjectUtils.isEmpty(singleProject.unitProjects)) {
            for (let i = 0; i < singleProject.unitProjects.length; i++) {
                let unitObject = {};
                unitObject["headLine"] = singleProject.unitProjects[i].upName;
                unitObject["childrenList"] = await this.deepCopy(unit);
                unitObject["id"] = singleProject.unitProjects[i].sequenceNbr;
                unitObject["sdUnitId"] = singleProject.unitProjects[i].sequenceNbr;
                unitObject["ssUnitId"] = singleProject.unitProjects[i].ysshUnitId;
                unitObject["sdSingleId"] = singleProject.unitProjects[i].parentId;
                unitObject["ssSingleId"] = singleProject.unitProjects[i].ysshSingleId;
                unitObject["levelType"] = singleProject.unitProjects[i].levelType;
                singleChildren.push(unitObject);
            }
        }

        if (!ObjectUtils.isEmpty(singleProject.subSingleProjects)) {
            for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                await this.traverseSingleForExport(singleProject.subSingleProjects[i],single,unit,singleChildren);
            }
        }
    }

    async addOrderNum(result, object) {
        if (result['id'] == null) {
            result['id'] = object.id++;
        }
        if (result.childrenList != null) {
            for (let i = 0; i < result.childrenList.length; i++) {
                await this.addOrderNum(result.childrenList[i], object);
            }
        }
    }

    async deepCopy(obj) {
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        let clone = Array.isArray(obj) ? [] : {};

        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                clone[key] = await this.deepCopy(obj[key]);
            }
        }

        return clone;
    }

    async exportExcel(params) {
        // let {constructName} = params.headLine;
        // let defaultStoragePath = PricingFileFindUtils.getDefaultStoragePath(constructName);
        const dialogOptions = {
            title: '保存文件', defaultPath: params.headLine, filters: [{name: 'zip', extensions: ['zip']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            console.log(filePath);
            await this.exportExcelZip(params, filePath);
            return true;
            // this.service.shenHeYuSuanProject.systemService.openWindowForProject(constructName,sequenceNbr);
        } else {
            return false;
        }
    }


    async exportExcelZip(params, filePath) {
        let constructIs2022= PricingFileFindUtils.getConstructDeStandard(params.id)==ConstantUtil.DE_STANDARD_22;
        let project = await this.initWorkBook("project",constructIs2022);
        let single = await this.initWorkBook("single",constructIs2022);
        let unit = await this.initWorkBook("unit",constructIs2022);

        let fileDir = this.getProjectRootPath() + "\\excelTemplate\\exportShenHe\\" + params.headLine;
        let args = {};
        args['constructId'] = params.id;
        args['ssConstructId'] = params.ssConstructId;
        await this.parseParams(params, project, single, unit, fileDir, args,constructIs2022);
        //对 对应的目录进行压缩 生成zip文件
        // 创建一个新的 Zip 实例
        const zip = new AdmZip();

        // 递归遍历目录及其子目录中的文件和子目录
        function traverseDirectory(dirPath, relativePath = '') {
            const files = fs.readdirSync(dirPath);

            files.forEach(file => {
                const filePath = path.join(dirPath, file);
                const stats = fs.statSync(filePath);

                if (stats.isDirectory()) {
                    const fileRelativeNext = path.join(relativePath, file);
                    zip.addFile(fileRelativeNext + '/', Buffer.alloc(0), '', 493); // 添加空文件夹
                    traverseDirectory(filePath, fileRelativeNext);
                } else {
                    zip.addLocalFile(filePath, relativePath);
                }
            });
        }

        traverseDirectory(fileDir, params.headLine);
        // 将 zip 文件保存到指定路径
        zip.writeZip(filePath);

        function deleteDirectory(dirPath) {
            if (fs.existsSync(dirPath)) {
                fs.readdirSync(dirPath).forEach(file => {
                    const filePath = path.join(dirPath, file);

                    if (fs.lstatSync(filePath).isDirectory()) {
                        deleteDirectory(filePath); // 递归删除子目录
                    } else {
                        fs.unlinkSync(filePath); // 删除文件
                    }
                });

                fs.rmdirSync(dirPath); // 删除空目录
                console.log('目录删除成功');
            } else {
                console.log('目录不存在');
            }
        }

        deleteDirectory(fileDir);
    }

    async parseParams(params, project, single, unit, fileDir, args,constructIs2022) {
        if (args == null) {
            args = {};
        }
        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == "project") {
                args["constructId"] = params.id;
                args["sdConstructId"] = params.id;
                args["ssConstructId"] = params.ssConstructId;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(project, param.projectLevel, param.headLine, args);
                } else {
                    project.removeWorksheet(param.headLine);
                }
                if (project.worksheets.length == 1 && project.worksheets[0].name == "格式替换sheet") {
                    project.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == "single") {
                args["singleId"] = params.id;
                args["sdSingleId"] = params.id;
                args["ssSingleId"] = params.ssSingleId;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(single, param.projectLevel, param.headLine, args);
                } else {
                    single.removeWorksheet(param.headLine);
                }
                if (single.worksheets.length == 1 && single.worksheets[0].name == "格式替换sheet") {
                    single.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == "unit") {
                //分情况  如果预算项目为单位工程 args要重新赋值 因为返回前端的是constructId
                if (params.biddingType == 2) {  //如果预算工程为单位工程
                    args["constructId"] = params.id;
                    // args["singleId"] = params.id;
                    args["unitId"] = params.id;

                    args["sdConstructId"] = params.id;
                    args["ssConstructId"] = params.ssConstructId;
                    // args["sdSingleId"] = params.id;
                    // args["ssSingleId"] = params.ssConstructId;
                    args["sdUnitId"] = params.id;
                    args["ssUnitId"] = params.ssConstructId;
                } else {

                    args["unitId"] = params.id;
                    args["sdUnitId"] = params.id;
                    args["ssUnitId"] = params.ssUnitId;
                }
                if (param.selected) {
                    await this.getWorkSheetWithData(unit, param.projectLevel, param.headLine, args);
                } else {
                    unit.removeWorksheet(param.headLine);
                }
                if (unit.worksheets.length == 1 && unit.worksheets[0].name == "格式替换sheet") {
                    unit.removeWorksheet("格式替换sheet");
                }
            }
            //1、去对应的栏目下拿到对应的sheet
            //2、对每一层级的sheet进行组装   生成文件 并压缩
            //3、返回文件流  删掉原文件
        }
        //针对不同的workbook 生成该一层级的excel文件
        let filename = fileDir + "\\" + params.headLine + ".xlsx";

        // 创建目录
        function createDirectory(directoryPath) {
            if (!fs.existsSync(directoryPath)) {
                fs.mkdirSync(directoryPath, {recursive: true});
                console.log('目录已创建');
            } else {
                console.log('目录已存在');
            }
        }

        if (params.childrenList != null && params.childrenList[0].projectLevel == "project") {
            project.removeWorksheet("格式替换sheet");
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                createDirectory(fileDir);
                await project.xlsx.writeFile(filename);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == "single") {
            single.removeWorksheet("格式替换sheet");
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                createDirectory(fileDir);
                await single.xlsx.writeFile(filename);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == "unit") {
            unit.removeWorksheet("格式替换sheet");
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                createDirectory(fileDir);
                await unit.xlsx.writeFile(filename);
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList != null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.initWorkBook("single",constructIs2022);
                unit = await this.initWorkBook("unit",constructIs2022);
                directory = fileDir + "\\" + filter[i].headLine;
                await this.parseParams(filter[i], project, single, unit, directory, args,constructIs2022);
            }
        }
    }

    async initWorkBook(projectLevel,constructIs2022) {
        let loadDir = "";
        if (!constructIs2022) {
            loadDir = this.getProjectRootPath() + "\\excelTemplate\\shenhe\\12";
        }else {

        }

        let loadPath = "";

        if (projectLevel == "single") {
            loadPath = loadDir + "\\单项工程层级.xlsx";
        } else if (projectLevel == "unit") {
            loadPath = loadDir + "\\单位工程层级.xlsx";
        } else if (projectLevel == "project") {
            loadPath = loadDir + "\\工程项目层级.xlsx";
        }

        //加载workbook
        let workbook = await ShenHeExcelUtil.readToWorkBook(loadPath);
        return workbook;
    }

    async getWorkSheetWithData(workbook, projectType, sheetName, args) {
        let worksheet = workbook.getWorksheet(sheetName);
        args["workbook"] = workbook;
        try {
            await this.switchWorkSheet(projectType, worksheet, args);
        } catch (e) {
            console.log("报表填充数据异常" + sheetName);
        }
        return worksheet;
    }

    async dealWithForSinglePageWhenExport(workSheet, workbook, headArgs) {
        let headStartNum = 0;
        let headEndNum = 0;
        if (headArgs != null) {
            headStartNum = headArgs['headStartNum'];
            headEndNum = headArgs['headEndNum'];
            if (headArgs['titlePage'] == null) {
                headArgs['titlePage'] = false;//默认为 数据页
            }
        } else {
            headArgs = {};
            headStartNum = 1;
            headEndNum = 4;
            headArgs['headStartNum'] = headStartNum;
            headArgs['headEndNum'] = headEndNum;
            headArgs['titlePage'] = false;//默认为 数据页
        }

        //1、复制表头
        //2、进行 行高自适应的处理 确定行高后  进行分页
        //10号字体
        // 在该行下方插入一个分页符
        //A4 行高 721.5   宽度
        // let marginLeft = ;//左边距
        //得到每一个cell的宽度比例 并计入map
        await ShenHeExcelUtil.getRatioWidthSheet(workSheet);


        let mergeMap = new Map(Object.entries(workSheet._merges));
        let fontSize = 13;
        //行高自适应  如果所需行高大于1行  则置为两行
        for (let i = headEndNum + 1; i <= workSheet._rows.length; i++) {
            let minHeight = 0;
            let fitRight = true;//这里预设为false 就会保留初始模板的空白行高度 为true针对空白行统统高度为0

            for (let j = 0; j < workSheet.getRow(i)._cells.length; j++) {
                let cell = workSheet.getRow(i)._cells[j];
                let celltextValue = cell.model.value;
                if (!celltextValue) {
                    continue;
                }
                fitRight = true;
                if (typeof celltextValue === 'number') {
                    celltextValue = String(celltextValue);
                }
                let contents;
                try {
                    contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
                } catch (e) {
                    console.log("");
                }
                let mergeName = ShenHeExcelUtil.getMergeName(workSheet._merges, cell);
                let mergeLength = 0;//得到该cell的宽度大小
                if (mergeName != null) {
                    let value = mergeMap.get(mergeName).model;
                    for (let m = value.left; m <= value.right; m++) {
                        mergeLength += workSheet.getRow(i)._cells[m - 1]._column.width;
                    }
                } else {
                    mergeLength = cell._column.width;
                }
                // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
                //这若为0  会造成递归死循环
                let rowWordNum = Math.trunc(mergeLength / ((fontSize / 72) * 10)) //每一列能够存放的字数
                let rowSpace = 2;//行间距
                let rowNumTotal = 0;
                for (let j = 0; j < contents.length; j++) {
                    let rowText = contents[j];
                    if (!rowText && rowText.length == 0) {
                        continue;
                    }
                    // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                    let rowNum = Math.ceil(rowText.length / rowWordNum);
                    //优化处理  如果单行字数超过五  考虑到单元格的两侧边界距离  实际每行能存放的字数进行减二
                    if (rowNum >= 2 && rowNum * rowWordNum == rowText.length) {
                        rowNum++;
                    }
                    rowNumTotal += rowNum;
                }
                if (rowNumTotal > 2) {
                    rowNumTotal = 2;  //最大置为两行
                }
                let newMinHeight = ((fontSize) + rowSpace) * rowNumTotal + 8;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距

                if (minHeight < newMinHeight) {
                    minHeight = newMinHeight; //得到该行的最大行高
                }
            }
            if (fitRight) {
                workSheet.getRow(i).height = minHeight;
            }
        }
        //分页处理
        // await workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
        if (!headArgs['titlePage']) {  //如果不是扉页
            let totalPage = await ShenHeExcelUtil.pageSplit(workSheet, 1, headArgs, 0);
            /*****************************************/
            //对页码显示进行处理
            let cellList = ShenHeExcelUtil.findContainValueCell(workSheet, "第 1 页  共 1 页");
            if (cellList.length == 0) {
                cellList = ShenHeExcelUtil.findContainValueCell(workSheet, "第 1 页 共 1 页");//横版是如此格式
            }
            const grouped = cellList.reduce((result, obj) => {
                const key = obj.cell._row._number;
                if (!result[key]) {
                    result[key] = [];
                }
                result[key].push(obj.cell);
                return result;
            }, {});
            let mergeMap = new Map(Object.entries(grouped));
            let count = 0;
            for (let [key, value] of mergeMap) {
                count++;
                let str = "第 " + (count) + " 页 共 " + totalPage + " 页";
                for (let i = 0; i < value.length; i++) {
                    let elementCell = value[i];
                    elementCell.value = str;
                }
            }
            /*****************对空白行的处理********************************/
            //要求空白行只能是在末尾页  而不是在页中  否则逻辑出错
            // let blankRowList = await this.getBlankRow(workSheet);
            await ShenHeExcelUtil.dealWithBlankRow(workSheet, headArgs);
        }
    }

    async switchWorkSheet(projectType, worksheet, args) {
        let {constructId, unitId, singleId, workbook} = args;
        // let unit = {};
        // if (constructId != null && singleId != null && unitId != null) {
        //     unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // }
        if (projectType == "project") {
            let constructProjectJBXX = await this.getconstructProjectJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //工程项目层级
                case "【封面1】工程审核书封面":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet1(constructProjectJBXX, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 13;
                    headArgsQd1['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    break;
                case "【封面2】工程审核书签署页":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet2(constructProjectJBXX, worksheet);
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 9;
                    headArgsQd2['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    break;
                case "【封面3】审核签署表":
                    let constructProjectTotal = await this.getconstructProjectSheet3(args, constructProjectJBXX);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet3(constructProjectTotal, worksheet);
                    let headArgsQd3 = {};
                    headArgsQd3['headStartNum'] = 1;
                    headArgsQd3['headEndNum'] = 11;
                    headArgsQd3['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    break;
                case "【封面4】工程审核认证单":
                    let constructProjectTotal4 = await this.getconstructProjectSheet4(args, constructProjectJBXX);
                    let constructProjectSheet4List = await this.getconstructProjectSheet4List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet4(constructProjectTotal4, constructProjectSheet4List, worksheet);
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 4;
                    headArgsQd4['titlePage'] = false;
                    headArgsQd4['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    // let heJiCell4 = ExcelUtil.findValueCell(worksheet, "合计");
                    // let row4 = worksheet.getRow(heJiCell4.cell._row._number);
                    // await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(1, row4.number + 1, worksheet, workSheetGeshi, 1, 4, 8);
                    // await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(2, row4.number + 2, worksheet, workSheetGeshi, 1, 4, 8);
                    // await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(3, row4.number + 3, worksheet, workSheetGeshi, 1, 4, 8);

                    let totalSS = constructProjectTotal4.filter(object => object.name == "totalS")[0];
                    await this.sheetMerge(worksheet, headArgsQd4['headEndNum'], totalSS.remark);
                    break;
                case "【封面5】工程造价审查书":
                    let constructProjectTotal5 = await this.getconstructProjectSheet3(args, constructProjectJBXX);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet5(constructProjectTotal5, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 9;
                    headArgsQd5['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    break;
                case "【项1】工程审核汇总对比表":
                    let constructProjectTotal6 = await this.getconstructProjectSheet4(args, constructProjectJBXX);
                    let constructProjectSheet6List = await this.getconstructProjectSheet6List(args, constructProjectTotal6);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet6(constructProjectTotal6, constructProjectSheet6List, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 4;
                    headArgsQd6['titlePage'] = false;
                    headArgsQd6['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    let heJiCell6 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row6 = worksheet.getRow(heJiCell6.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(5, row6.number + 1, worksheet, workSheetGeshi, 3, 6, 9);
                    break;
                case "【人材机1】人材机汇总对比表":
                    let constructProjectSheet7List = await this.getconstructProjectSheet7List(args);
                    constructProjectJBXX["projectRcjHeji"] = args["projectRcjHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet7(constructProjectJBXX, constructProjectSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell7 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(7, row7.number + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    break;
            }
        }
        if (projectType == "single") {
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //单项工程层级
                case "【单项1】单项工程审核对比表":
                    let constructSingle1 = await this.getconstructProjectSingleJBXX(args);
                    let constructSingleSheet1List = await this.getconstructSingleSheet1List(args);
                    constructSingle1["singleHeji"] = args["singleHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToSingleSheet1(constructSingle1, constructSingleSheet1List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell2 = ExcelUtil.findValueCell(worksheet, "合计（不含设备及其税金）");
                    let row2 = worksheet.getRow(heJiCell2.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(1, row2.number + 1, worksheet, workSheetGeshi, 2, 4, 5);
                    break;
            }
        }
        if (projectType == "unit") {
            let constructUnitJBXX = await this.getconstructProjectSingleUnitJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //单位工程层级
                case "【费1】单位工程审核对比表":
                    let constructUnitSheet1List = await this.getconstructUnitSheet1List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet1(constructUnitJBXX, constructUnitSheet1List, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 4;
                    headArgsQd1['titlePage'] = false;
                    headArgsQd1['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    // let heJiCell1 = ExcelUtil.findValueCell(worksheet, "合计");
                    // let row1 = worksheet.getRow(heJiCell1.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(1, worksheet._rows.length + 1, worksheet, workSheetGeshi, 3, 5, 8);
                    break;
                case "【分部1】分部分项清单对比表":
                    let constructUnitSheet2List = await this.getconstructUnitSheet2List(args);
                    constructUnitJBXX["unitFbfxHeji"] = args["unitFbfxHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet2(constructUnitJBXX, constructUnitSheet2List, worksheet);
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 4;
                    headArgsQd2['titlePage'] = false;
                    headArgsQd2['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    let heJiCell2 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row2 = worksheet.getRow(heJiCell2.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(3, row2.number + 1, worksheet, workSheetGeshi, 3, 7, 12);
                    break;
                case "【分部6】分部分项清单对比表(含关联项)":
                    let constructUnitSheet3List = await this.getconstructUnitSheet3List(args);
                    constructUnitJBXX["unitFbfxHeji"] = args["unitFbfxHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet3(constructUnitJBXX, constructUnitSheet3List, worksheet);
                    let headArgsQd3 = {};
                    headArgsQd3['headStartNum'] = 1;
                    headArgsQd3['headEndNum'] = 4;
                    headArgsQd3['titlePage'] = false;
                    headArgsQd3['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    let heJiCell3 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row3 = worksheet.getRow(heJiCell3.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(5, row3.number + 1, worksheet, workSheetGeshi, 4, 8, 14);
                    await this.sheetMerge(worksheet, headArgsQd3['headEndNum'], "");
                    break;
                case "【措施1】措施项目审核对比表":
                    let constructUnitSheet4List = await this.getconstructUnitSheet4List(args);
                    constructUnitJBXX["unitCuoshiHeji"] = args["unitCuoshiHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet4(constructUnitJBXX, constructUnitSheet4List, worksheet);
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 4;
                    headArgsQd4['titlePage'] = false;
                    headArgsQd4['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    let heJiCell4 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row4 = worksheet.getRow(heJiCell4.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(7, row4.number + 1, worksheet, workSheetGeshi, 4, 7, 11);
                    break;
                case "【其他1】其他项目审核对比表":
                    let constructUnitSheet5List = await this.getconstructUnitSheet5List(args);
                    constructUnitJBXX["unitOtherHeji"] = args["unitOtherHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet5(constructUnitJBXX, constructUnitSheet5List, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 3;
                    headArgsQd5['titlePage'] = false;
                    headArgsQd5['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    let heJiCell5 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row5 = worksheet.getRow(heJiCell5.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(9, row5.number + 1, worksheet, workSheetGeshi, 2, 4, 6);
                    break;
                case "【计日工1】计日工审核对比表":
                    let constructUnitSheet6List = await this.getconstructUnitSheet6List(args);
                    constructUnitJBXX["unitOtherJrgHeji"] = args["unitOtherJrgHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet6(constructUnitJBXX, constructUnitSheet6List, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 4;
                    headArgsQd6['titlePage'] = false;
                    headArgsQd6['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    let heJiCell6 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row6 = worksheet.getRow(heJiCell6.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(11, row6.number + 1, worksheet, workSheetGeshi, 4, 8, 12);
                    break;
                case "【人材机2】人材机审核对比表":
                    let constructUnitSheet7List = await this.getconstructUnitSheet7List(args);
                    constructUnitJBXX["unitRcjHeji1"] = args["unitRcjHeji1"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet7(constructUnitJBXX, constructUnitSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell7 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(13, row7.number + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    break;
                case "【人材机3】人材机价差汇总对比表":
                    let constructUnitSheet8List = await this.getconstructUnitSheet8List(args);
                    constructUnitJBXX["unitRcjHeji2"] = args["unitRcjHeji2"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet8(constructUnitJBXX, constructUnitSheet8List, worksheet);
                    let headArgsQd8 = {};
                    headArgsQd8['headStartNum'] = 1;
                    headArgsQd8['headEndNum'] = 4;
                    headArgsQd8['titlePage'] = false;
                    headArgsQd8['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8);
                    let heJiCell8 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row8 = worksheet.getRow(heJiCell8.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(15, row8.number + 1, worksheet, workSheetGeshi, 5, 10, 16);
                    break;
                case "【增值税1】材料、机械、设备增值税对比表":
                    let constructUnitSheet9List = await this.getconstructUnitSheet9List(args);
                    constructUnitJBXX['unitcljxsbHeji'] = args["unitcljxsbHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet9(constructUnitJBXX, constructUnitSheet9List, worksheet);
                    let headArgsQd9 = {};
                    headArgsQd9['headStartNum'] = 1;
                    headArgsQd9['headEndNum'] = 4;
                    headArgsQd9['titlePage'] = false;
                    headArgsQd9['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9);
                    let heJiCell9 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row9 = worksheet.getRow(heJiCell9.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(17, row9.number + 1, worksheet, workSheetGeshi, 5, 10, 14);
                    await this.sheetMerge(worksheet, headArgsQd9['headEndNum'], "");
                    break;
                case "【规费1】规费明细对比表":
                    let constructUnitSheet10List = await this.getconstructUnitSheet10List(args);
                    constructUnitJBXX["unitGuifeiHeji"] = args["unitGuifeiHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet10(constructUnitJBXX, constructUnitSheet10List, worksheet);
                    let headArgsQd10 = {};
                    headArgsQd10['headStartNum'] = 1;
                    headArgsQd10['headEndNum'] = 4;
                    headArgsQd10['titlePage'] = false;
                    headArgsQd10['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd10);
                    let heJiCell10 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row10 = worksheet.getRow(heJiCell10.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(19, row10.number + 1, worksheet, workSheetGeshi, 4, 8, 11);
                    break;
                case "【安全文施1】安全文明施工费明细对比表":
                    let constructUnitSheet11List = await this.getconstructUnitSheet11List(args);
                    constructUnitJBXX["unitAnwenfeiHeji"] = args["unitAnwenfeiHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet11(constructUnitJBXX, constructUnitSheet11List, worksheet);
                    let headArgsQd11 = {};
                    headArgsQd11['headStartNum'] = 1;
                    headArgsQd11['headEndNum'] = 4;
                    headArgsQd11['titlePage'] = false;
                    headArgsQd11['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11);
                    let heJiCell11 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row11 = worksheet.getRow(heJiCell11.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(21, row11.number + 1, worksheet, workSheetGeshi, 4, 8, 13);
                    break;
                case "【工程量1】审定工程量计算书":
                    let constructUnitSheet12List = await this.getconstructUnitSheet12List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet12(constructUnitJBXX, constructUnitSheet12List, worksheet);
                    let headArgsQd12 = {};
                    headArgsQd12['headStartNum'] = 1;
                    headArgsQd12['headEndNum'] = 3;
                    headArgsQd12['titlePage'] = false;
                    headArgsQd12['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd12);
                    // let heJiCell12 = ExcelUtil.findValueCell(worksheet, "合计");
                    // let row12 = worksheet.getRow(heJiCell12.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(23, worksheet._rows.length + 1, worksheet, workSheetGeshi, 2, 5, 7);
                    break;
                case "【增值税4】增值税进项税额对比表":
                    let constructUnitSheet13List = await this.getconstructUnitSheet13List(args);
                    constructUnitJBXX["unitZzsjxseHeji"] = args["unitZzsjxseHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet13(constructUnitJBXX, constructUnitSheet13List, worksheet);
                    let headArgsQd13 = {};
                    headArgsQd13['headStartNum'] = 1;
                    headArgsQd13['headEndNum'] = 3;
                    headArgsQd13['titlePage'] = false;
                    headArgsQd13['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13);
                    let heJiCell13 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row13 = worksheet.getRow(heJiCell13.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(25, row13.number + 1, worksheet, workSheetGeshi, 2, 3, 5);
                    break;
                default:
            }
        }
    }


    async sheetMerge(worksheet, headEndNum, totalSS) {
        let dataNum = headEndNum + 1;
        if (worksheet.name.includes("【封面4】工程审核认证单")) {
            let rowBreaks = worksheet.rowBreaks;
            //合并第一列描述
            if (rowBreaks != null && rowBreaks.length > 0) {
                //有分页
                let start = 0;
                for (let j = 0; j < rowBreaks.length; j++) {
                    start = start + dataNum;
                    let rowBreak = rowBreaks[j];
                    let id = rowBreak.id;
                    //合并第五行到id
                    worksheet.unMergeCells(start - 2, 1, id, 1);
                    worksheet.mergeCells(start - 2, 1, id, 1);
                    let rowLast = worksheet.getRow(start);
                    rowLast.getCell(1).value = totalSS;
                    rowLast.getCell(1).style.alignment.horizontal = "center";
                    rowLast.getCell(1).style.alignment.vertical = "middle";
                    start = rowBreak.id;
                }

                let start2 = start + dataNum;
                let end2 = start + dataNum;
                let flag = false;
                for (let j = worksheet._rows.length - 1; j > 0; j--) {
                    let row = worksheet._rows[j];
                    for (let j = 0; j < row._cells.length; j++) {
                        let cell = row._cells[j];
                        if (cell.col == 2) {
                            if (cell.value != "负责人：" && cell.value != "年  月  日" && cell.value != "施工单位") {
                                end2 = row.number;
                                flag = true;
                            }
                            break;
                        }
                    }
                    if (flag) {
                        break;
                    }
                }
                worksheet.unMergeCells(start2 - 2, 1, end2, 1);
                worksheet.mergeCells(start2 - 2, 1, end2, 1);
                let rowLast = worksheet.getRow(start2);
                rowLast.getCell(1).value = totalSS;
                rowLast.getCell(1).style.alignment.horizontal = "center";
                rowLast.getCell(1).style.alignment.vertical = "middle";
            } else {
                //没有分页
                let startNoPage = 0;
                startNoPage = startNoPage + dataNum;
                let endNoPage = startNoPage + dataNum;

                let heJiCell4 = ExcelUtil.findValueCell(worksheet, "合计");
                let row4 = worksheet.getRow(heJiCell4.cell._row._number);
                endNoPage = row4.number;

                worksheet.unMergeCells(startNoPage - 2, 1, endNoPage, 1);
                worksheet.mergeCells(startNoPage - 2, 1, endNoPage, 1);
                let rowLast = worksheet.getRow(startNoPage);
                rowLast.getCell(1).value = totalSS;
                rowLast.getCell(1).style.alignment.horizontal = "center";
                rowLast.getCell(1).style.alignment.vertical = "middle";
            }

            //合并最后三行的数据
            let heJiCell1 = ExcelUtil.findValueCell(worksheet, "施工单位");
            if (heJiCell1 != undefined) {
                let row1 = worksheet.getRow(heJiCell1.cell._row._number);
                worksheet.unMergeCells(row1.number, heJiCell1.cell._column._number, row1.number, heJiCell1.cell._column._number + 2);
                worksheet.mergeCells(row1.number, heJiCell1.cell._column._number, row1.number, heJiCell1.cell._column._number + 2);
            }

            let heJiCell2 = ExcelUtil.findValueCell(worksheet, "审计单位");
            if (heJiCell2 != undefined) {
                let row2 = worksheet.getRow(heJiCell2.cell._row._number);
                worksheet.unMergeCells(row2.number, heJiCell2.cell._column._number, row2.number, heJiCell2.cell._column._number + 3);
                worksheet.mergeCells(row2.number, heJiCell2.cell._column._number, row2.number, heJiCell2.cell._column._number + 3);
            }


            let containValueCell1 = ExcelUtil.findContainValueCell(worksheet, "年  月  日");
            if (containValueCell1 != undefined && containValueCell1.length > 1) {
                for (let i = 1; i < containValueCell1.length; i++) {
                    let cell1Element = containValueCell1[i];
                    let row1 = worksheet.getRow(cell1Element.cell._row._number);
                    if (i === 1) {
                        worksheet.unMergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 2);
                        worksheet.mergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 2);
                    } else if (i === 2) {
                        worksheet.unMergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 3);
                        worksheet.mergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 3);
                    }
                }
            }

            let containValueCell2 = ExcelUtil.findContainValueCell(worksheet, "负责人：");
            if (containValueCell2 != undefined && containValueCell2.length > 1) {
                for (let i = 1; i < containValueCell2.length; i++) {
                    let cell1Element = containValueCell2[i];
                    let row1 = worksheet.getRow(cell1Element.cell._row._number);
                    if (i === 1) {
                        worksheet.unMergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 2);
                        worksheet.mergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 2);
                    } else if (i === 2) {
                        worksheet.unMergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 3);
                        worksheet.mergeCells(row1.number, cell1Element.cell._column._number, row1.number, cell1Element.cell._column._number + 3);
                    }
                }
            }
        } else if (worksheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
            let rowBreaks = worksheet.rowBreaks;
            //合并送审1、2、3、4、5、6、13、14列
            if (rowBreaks != null && rowBreaks.length > 0) {
                //有分页
                let start = 0;
                for (let j = 0; j < rowBreaks.length; j++) {
                    start = start + dataNum;
                    let rowBreak = rowBreaks[j];
                    let id = rowBreak.id;

                    let hebinghangStart = start;
                    let hebinghangEnd = start;
                    for (let j = start; j <= id-1; j++) {
                        let cell1 = worksheet.getRow(j).getCell(1);
                        let cellNextRow = worksheet.getRow(j+1).getCell(1);
                        if (cell1.value == cellNextRow.value) {
                            hebinghangEnd = hebinghangEnd+1;
                        }else {
                            if (hebinghangEnd - hebinghangStart > 0 && worksheet.getRow(hebinghangEnd).getCell(14).value == "一对多") {

                                worksheet.unMergeCells(hebinghangStart, 1, hebinghangEnd, 1);
                                worksheet.mergeCells(hebinghangStart, 1, hebinghangEnd, 1);
                                worksheet.unMergeCells(hebinghangStart, 2, hebinghangEnd, 2);
                                worksheet.mergeCells(hebinghangStart, 2, hebinghangEnd, 2);
                                worksheet.unMergeCells(hebinghangStart, 3, hebinghangEnd, 3);
                                worksheet.mergeCells(hebinghangStart, 3, hebinghangEnd, 3);
                                worksheet.unMergeCells(hebinghangStart, 4, hebinghangEnd, 4);
                                worksheet.mergeCells(hebinghangStart, 4, hebinghangEnd, 4);
                                worksheet.unMergeCells(hebinghangStart, 5, hebinghangEnd, 5);
                                worksheet.mergeCells(hebinghangStart, 5, hebinghangEnd, 5);
                                worksheet.unMergeCells(hebinghangStart, 6, hebinghangEnd, 6);
                                worksheet.mergeCells(hebinghangStart, 6, hebinghangEnd, 6);

                                worksheet.unMergeCells(hebinghangStart, 13, hebinghangEnd, 13);
                                worksheet.mergeCells(hebinghangStart, 13, hebinghangEnd, 13);
                                worksheet.unMergeCells(hebinghangStart, 14, hebinghangEnd, 14);
                                worksheet.mergeCells(hebinghangStart, 14, hebinghangEnd, 14);
                            }

                            hebinghangStart = j+1;
                            hebinghangEnd = j+1;
                        }
                    }
                    start = rowBreak.id;
                }
            } else {
                //没有分页
                let start = dataNum;
                let hebinghangStart = start;
                let hebinghangEnd = start;

                for (let j = start; j <= worksheet._rows.length-1; j++) {
                    let cell1 = worksheet.getRow(j).getCell(1);
                    let cellNextRow = worksheet.getRow(j+1).getCell(1);
                    if (cell1.value == cellNextRow.value) {
                        hebinghangEnd = hebinghangEnd+1;
                    }else {     //和下一行内容不相等的话 对上面的行内容进行合并
                        if (hebinghangEnd - hebinghangStart > 0 && worksheet.getRow(hebinghangEnd).getCell(14).value == "一对多") {

                            worksheet.unMergeCells(hebinghangStart, 1, hebinghangEnd, 1);
                            worksheet.mergeCells(hebinghangStart, 1, hebinghangEnd, 1);
                            worksheet.unMergeCells(hebinghangStart, 2, hebinghangEnd, 2);
                            worksheet.mergeCells(hebinghangStart, 2, hebinghangEnd, 2);
                            worksheet.unMergeCells(hebinghangStart, 3, hebinghangEnd, 3);
                            worksheet.mergeCells(hebinghangStart, 3, hebinghangEnd, 3);
                            worksheet.unMergeCells(hebinghangStart, 4, hebinghangEnd, 4);
                            worksheet.mergeCells(hebinghangStart, 4, hebinghangEnd, 4);
                            worksheet.unMergeCells(hebinghangStart, 5, hebinghangEnd, 5);
                            worksheet.mergeCells(hebinghangStart, 5, hebinghangEnd, 5);
                            worksheet.unMergeCells(hebinghangStart, 6, hebinghangEnd, 6);
                            worksheet.mergeCells(hebinghangStart, 6, hebinghangEnd, 6);

                            worksheet.unMergeCells(hebinghangStart, 13, hebinghangEnd, 13);
                            worksheet.mergeCells(hebinghangStart, 13, hebinghangEnd, 13);
                            worksheet.unMergeCells(hebinghangStart, 14, hebinghangEnd, 14);
                            worksheet.mergeCells(hebinghangStart, 14, hebinghangEnd, 14);
                        }

                        hebinghangStart = j+1;
                        hebinghangEnd = j+1;
                    }

                }
            }
        } else if (worksheet.name.includes("【增值税1】材料、机械、设备增值税对比表")) {
            for (let i = 0; i < worksheet._rows.length; i++) {
                let row3 = worksheet.getRow(i);
                let cell1 = row3.getCell(2);
                if (cell1.value === "材料" || cell1.value === "机械" || cell1.value === "设备") {
                    let value = cell1.value;
                    let style = row3.getCell(3).style;
                    worksheet.unMergeCells(row3.number, 1, row3.number, 14);
                    worksheet.mergeCells(row3.number, 1, row3.number, 14);
                    cell1.value = value;
                    cell1.style = style;
                    row3.getCell(1).style = style;
                }
            }
        }
    }

    traverseHeadLineList(headLineList, strCondition) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];
            if (element.headLine == strCondition) {
                return element;
            }
            if (element.hasOwnProperty("children")) {
                let result = this.traverseHeadLineList(element.children, strCondition);
                if (result != null) return result;
            }
        }
        return null;
    }

    //拿到list中 没有children的元素
    traverseGetHeadLineAndLeaf(headLineList, levelType, list) {
        for (let i = 0; i < headLineList.length; i++) {
            let element = headLineList[i];

            if (element.projectLevel == levelType && element.children == null) {
                element['selected'] = false;
                list.push(element);
            }
            if (element.projectLevel == levelType && element.children != null) {
                this.traverseGetHeadLineAndLeaf(element.children, levelType, list);
            }
        }
        return list;
    }

    async getconstructProjectJBXX(param) {
        //获取工程项目对象
        let projectObjById = await PricingFileFindUtils.getProjectObjById(param.constructId);
        //获取基本信息
        param['levelType'] = projectLevelConstant.construct;
        let shenHeJBXX1 = await this.service.shenHeYuSuanProject.ysshZdxglService.getYsshProjectOverview(param);
        let shenHeJBXX = shenHeJBXX1.filter(function (element) {
            if (element.groupCode == 1) return element;
        });

        let array = new Array();
        let constructName = {};
        constructName.name = "项目名称";
        constructName.remark = projectObjById.constructName;
        array.push(constructName);

        let chongmingMap = new Map();
        for (let i = 0; i < shenHeJBXX.length; i++) {
            let heJBXX = shenHeJBXX[i];
            let project = {};
            if (chongmingMap.has(heJBXX.name)) {
                continue;
            }
            switch (heJBXX.name) {
                case "编制人":
                    project.name = heJBXX.name;
                    project.remark = heJBXX.remark;
                    array.push(project);
                    chongmingMap.set(heJBXX.name, heJBXX.name);
                    break;
                case "审核人":
                    project.name = heJBXX.name;
                    project.remark = heJBXX.remark;
                    array.push(project);
                    chongmingMap.set(heJBXX.name, heJBXX.name);
                    break;
                case "审定人":
                    project.name = heJBXX.name;
                    project.remark = heJBXX.remark;
                    array.push(project);
                    chongmingMap.set(heJBXX.name, heJBXX.name);
                    break;
                case "审定时间":
                    project.name = heJBXX.name;
                    project.remark = heJBXX.remark;
                    array.push(project);
                    chongmingMap.set(heJBXX.name, heJBXX.name);
                    break;
                case "审核单位":
                    project.name = heJBXX.name;
                    project.remark = heJBXX.remark;
                    array.push(project);
                    chongmingMap.set(heJBXX.name, heJBXX.name);
                    break;
                case "审核单位法定代表人或授权人":
                    project.name = "审核单位法定代表人或其授权人";
                    project.remark = heJBXX.remark;
                    array.push(project);
                    chongmingMap.set(heJBXX.name, heJBXX.name);
                    break;
            }
        }

        return array;
    }


    async getconstructProjectSingleJBXX(param) {
        //获取工程项目对象
        let singleProject = PricingFileFindUtils.getSingleProject(param.constructId, param.singleId);
        if (ObjectUtils.isUndefined(singleProject)) {
            //说明只有送审没有审定，需要用送审的id去查询
            singleProject = PricingFileFindUtils.getSingleProject(param.ssConstructId, param.ssSingleId);
        }

        let projectName = singleProject.projectName;

        param['levelType'] = projectLevelConstant.construct;
        // const shenHeJBXX = await this.service.shenHeYuSuanProject.projectOverviewShenHeService.getProjectOverviewListSh(param);

        let array = new Array();
        let constructName = {};
        constructName.name = "工程名称";
        constructName.remark = projectName;
        array.push(constructName);

        return array;

    }


    async getconstructProjectSingleUnitJBXX(param) {
        //获取工程项目对象
        let singleProject = PricingFileFindUtils.getSingleProject(param.constructId, param.singleId);
        let unitProject = PricingFileFindUtils.getUnit(param.constructId, param.singleId, param.unitId);
        if (ObjectUtils.isUndefined(singleProject) && ObjectUtils.isUndefined(unitProject)) {
            singleProject = PricingFileFindUtils.getSingleProject(param.ssConstructId, param.ssSingleId);
            unitProject = PricingFileFindUtils.getUnit(param.ssConstructId, param.ssSingleId, param.ssUnitId);
        }

        param['levelType'] = projectLevelConstant.construct;
        // const shenHeJBXX = await this.service.shenHeYuSuanProject.projectOverviewShenHeService.getProjectOverviewListSh(param);

        let array = new Array();
        let constructName = {};
        constructName.name = "工程名称";
        if (singleProject != null) {
            constructName.remark = singleProject.projectName + "-" + unitProject.upName;
        } else {
            constructName.remark = unitProject.upName;
        }
        array.push(constructName);

        return array;

    }


    async getconstructProjectSheet3(param, constructProjectJBXX3) {
        let arg = {};
        arg.levelType = 1;
        arg.constructId = param.constructId;
        arg.constructIdSD = param.sdConstructId;
        arg.ssConstructId = param.ssConstructId;
        let projectTotal = await this.service.shenHeYuSuanProject.ysshCostAnalysisService.getCostAnalysisData(arg);

        let result = projectTotal.result.costAnalysisConstructVOList;
        let totalSS = 0;
        let totalSD = 0;
        let totalChange = 0;
        if (result != null) {
            totalSS = result.reduce((sum, item) => sum + Number(item.ysshSysj == undefined || item.ysshSysj.gczj == undefined ? 0 : item.ysshSysj.gczj), 0);
            totalSD = result.reduce((sum, item) => sum + Number(item.gczj == undefined ? 0 : item.gczj), 0);
            totalChange = result.reduce((sum, item) => sum + Number(item.ysshSysj.changeTotal == undefined ? 0 : item.ysshSysj.changeTotal), 0);
        }

        let array = constructProjectJBXX3;
        let bszj = {};
        bszj.name = "送审造价";
        bszj.remark = NumberUtil.roundHalfUp(totalSS);
        array.push(bszj);
        let tzje = {};
        tzje.name = "调整金额（+、—）";
        tzje.remark = NumberUtil.roundHalfUp(totalChange);
        array.push(tzje);
        let dx = {};
        dx.name = "审定造价大写";
        dx.remark = NumberUtil.roundHalfUp(totalSD);
        array.push(dx);
        let xx = {};
        xx.name = "审定造价";
        xx.remark = NumberUtil.roundHalfUp(totalSD);
        array.push(xx);
        return array;
    }


    async getconstructProjectSheet4(param, constructProjectJBXX3) {
        return this.getconstructProjectSheet3(param, constructProjectJBXX3);
    }


    async getconstructProjectSheet4List(param) {
        let arg = {};
        arg.levelType = 1;
        arg.constructId = param.constructId;
        arg.constructIdSD = param.sdConstructId;
        arg.ssConstructId = param.ssConstructId;
        let projectTotal = await this.service.shenHeYuSuanProject.ysshCostAnalysisService.getCostAnalysisData(arg);
        let returnResult = projectTotal.result.costAnalysisConstructVOList;
        const result = [];
        result.push(...returnResult);
        await this.getAllElement(returnResult.childrenList,result);
        // if (projectTotal.costAnalysisConstructVOList != null && projectTotal.costAnalysisConstructVOList.length > 0) {
        //     for (let i = 0; i < projectTotal.costAnalysisConstructVOList.length; i++) {
        //         let listElement = projectTotal.costAnalysisConstructVOList[i];
        //         result.push(listElement);
        //         if (listElement.childrenList != null && listElement.childrenList.length > 0) {
        //             listElement.childrenList.forEach(o => {
        //                 result.push(o);
        //             })
        //         }
        //     }
        // }
        return result;
    }

    async getAllElement(data,result) {
        if (ObjectUtils.isEmpty(data)) {
            return ;
        }
        for (let i = 0; i < data.length; i++) {
            let listElement = data[i];
            result.push(listElement);
            if (listElement.childrenList != null && listElement.childrenList.length > 0) {
                await this.getAllElement(listElement.childrenList,result);
            }
        }
        return result;
    }


    async getconstructProjectSheet6List(param, constructProjectTotal6) {
        let arg = {};
        arg.levelType = 1;
        arg.constructId = param.constructId;
        arg.constructIdSD = param.sdConstructId;
        arg.ssConstructId = param.ssConstructId;
        let projectTotal = await this.service.shenHeYuSuanProject.ysshCostAnalysisService.getCostAnalysisData(arg);
        let returnResult = projectTotal.result.costAnalysisConstructVOList;
        const result = [];
        await this.getAllElement(returnResult,result);

        let ssgczj = returnResult.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.gczj == undefined ? 0 : item.ysshSysj.gczj), 0).toNumber();
        let sssbfsj = returnResult.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.sbfsj == undefined ? 0 : item.ysshSysj.sbfsj), 0).toNumber();
        let gczj = returnResult.reduce((sum, item) => Decimal.add(sum, item.gczj == undefined ? 0 : item.gczj), 0).toNumber();
        let sbfsj = returnResult.reduce((sum, item) => Decimal.add(sum, item.sbfsj == undefined ? 0 : item.sbfsj), 0).toNumber();

        let sszj = {};
        sszj.name = "送审造价(含设备费及其税金)";
        sszj.remark = NumberUtil.add(ssgczj, sssbfsj);
        constructProjectTotal6.push(sszj);
        let sdzj = {};
        sdzj.name = "审定造价(含设备费及其税金)";
        sdzj.remark = NumberUtil.add(gczj, sbfsj);
        constructProjectTotal6.push(sdzj);

        return result;
    }


    async getconstructProjectSheet7List(param) {
        let heji = {};
        heji.sstotal = 0;         //
        heji.sdtotal = 0;
        heji.changePrice = 0;
        heji.changeTotal = 0;

        let Sheet4List = [];
        //人工
        let arg1 = {};
        arg1.type = 1;
        arg1.kind = 1;
        arg1.constructId = param.constructId;
        arg1.ssConstructId = param.ssConstructId;
        let rcjComparison1 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg1);
        if (rcjComparison1 != null && rcjComparison1.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison1, "一、", "人工", "小计");
            let num1 = 0;
            rcjComparison1.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num1++;
                    o.dispNo = num1;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "一、", "人工");
        }

        //材料
        let arg2 = {};
        arg2.type = 1;
        arg2.kind = 2;
        arg2.constructId = param.constructId;
        arg2.ssConstructId = param.ssConstructId;
        let rcjComparison2 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg2);
        if (rcjComparison2 != null && rcjComparison2.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison2, "二、", "材料", "小计");
            let num2 = 0;
            rcjComparison2.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num2++;
                    o.dispNo = num2;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "二、", "材料");
        }

        //机械
        let arg3 = {};
        arg3.type = 1;
        arg3.kind = 3;
        arg3.constructId = param.constructId;
        arg3.ssConstructId = param.ssConstructId;
        let rcjComparison3 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg3);
        if (rcjComparison3 != null && rcjComparison3.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison3, "三、", "机械", "小计");
            let num3 = 0;
            rcjComparison3.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num3++;
                    o.dispNo = num3;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "三、", "机械");
        }

        //设备
        let arg4 = {};
        arg4.type = 1;
        arg4.kind = 4;
        arg4.constructId = param.constructId;
        arg4.ssConstructId = param.ssConstructId;
        let rcjComparison4 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg4);
        if (rcjComparison4 != null && rcjComparison4.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison4, "四、", "设备", "小计");
            let num4 = 0;
            rcjComparison4.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num4++;
                    o.dispNo = num4;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "四、", "设备");
        }

        //主材
        let arg5 = {};
        arg5.type = 1;
        arg5.kind = 5;
        arg5.constructId = param.constructId;
        arg5.ssConstructId = param.ssConstructId;
        let rcjComparison5 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg5);
        if (rcjComparison5 != null && rcjComparison5.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison5, "五、", "主材", "小计");
            let num5 = 0;
            rcjComparison5.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num5++;
                    o.dispNo = num5;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "五、", "主材");
        }

        //预拌混凝土
        // let arg6 = {};
        // arg6.type = 1;
        // arg5.kind = 5;
        // arg6.sdConstructId = param.sdConstructId;
        // arg6.ssConstructId = param.ssConstructId;
        // let rcjComparison6 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg6);
        // if (rcjComparison6 != null && rcjComparison6.length > 0) {
        //     heji = await this.insertTopAndBottom(heji, rcjComparison6, "六、", "预拌混凝土");
        //     let num6 = 0;
        //     rcjComparison6.forEach(o => {
        //         if (o.dispNo == undefined || o.dispNo == null) {
        //             num6++;
        //             o.dispNo = num6;
        //         }
        //         Sheet4List.push(o);
        //     })
        // }

        param['projectRcjHeji'] = heji;

        return Sheet4List;
    }

    async insertTopAndBottom(heji, rcjComparison1, num, type, xiaoji) {
        let sstotal = 0;
        let sdtotal = 0;
        let changePrice = 0;
        let changeTotal = 0;

        if (rcjComparison1 != null && rcjComparison1.length > 0) {
            // sstotal = rcjComparison1.reduce((sum, item) => sum + parseFloat(item.ysshSysj.total), 0).toFixed(2);
            // sdtotal = rcjComparison1.reduce((sum, item) => sum + parseFloat(item.total), 0).toFixed(2);
            // changePrice = rcjComparison1.reduce((sum, item) => sum + parseFloat(item.ysshSysj.changePrice), 0).toFixed(2);
            // changeTotal = rcjComparison1.reduce((sum, item) => sum + parseFloat(item.ysshSysj.changeTotal), 0).toFixed(2);
            sstotal = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.total == undefined ? 0 : item.ysshSysj.total), 0).toNumber();
            sdtotal = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
            changePrice = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changePrice == undefined ? 0 : item.ysshSysj.changePrice), 0).toNumber();
            changeTotal = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined ? 0 : item.ysshSysj.changeTotal), 0).toNumber();
        }

        let ysshSysj1 = {};
        ysshSysj1.totalNumber = "";
        ysshSysj1.marketPrice = "";
        ysshSysj1.total = "";
        ysshSysj1.changePrice = "";
        ysshSysj1.changeTotal = "";
        ysshSysj1.changeExplain = "";

        let insertObj1 = {};
        insertObj1.dispNo = num;
        insertObj1.materialCode = "";
        insertObj1.materialName = type;
        insertObj1.specification = "";
        insertObj1.unit = "";
        insertObj1.dePrice = "";
        insertObj1.totalNumber = "";
        insertObj1.marketPrice = "";
        insertObj1.total = "";
        insertObj1.ysshSysj = ysshSysj1;
        rcjComparison1.unshift(insertObj1);

        let ysshSysj2 = {};
        ysshSysj2.totalNumber = "";
        ysshSysj2.marketPrice = "";
        ysshSysj2.total = sstotal;
        ysshSysj2.changePrice = changePrice;
        ysshSysj2.changeTotal = changeTotal;
        ysshSysj2.changeExplain = "";

        let insertObj2 = {};
        insertObj2.dispNo = "";
        insertObj2.materialCode = "";
        insertObj2.materialName = xiaoji;
        insertObj2.specification = "";
        insertObj2.unit = "";
        insertObj2.dePrice = "";
        insertObj2.totalNumber = "";
        insertObj2.marketPrice = "";
        insertObj2.total = sdtotal;
        insertObj2.ysshSysj = ysshSysj2;
        rcjComparison1.push(insertObj2);

        heji.sstotal = NumberUtil.add(heji.sstotal, sstotal);
        heji.sdtotal = NumberUtil.add(heji.sdtotal, sdtotal);
        heji.changePrice = NumberUtil.add(heji.changePrice, changePrice);
        heji.changeTotal = NumberUtil.add(heji.changeTotal, changeTotal);

        return heji;
    }


    async insertTopAndBottom9(heji, rcjComparison1, num, type, xiaoji) {
        let ssjxTotal = 0;
        let ssxxTotal = 0;
        let sdjxTota = 0;
        let sdxxTotal = 0;
        let jxszjje = 0;

        if (rcjComparison1 != null && rcjComparison1.length > 0) {
            // if (rcjComparison1[rcjComparison1.length - 1].materialName === "小计") {
            //     rcjComparison1.pop();
            // }
            ssjxTotal = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.jxTotal == undefined ? 0 : item.ysshSysj.jxTotal), 0).toNumber();
            ssxxTotal = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.xxTotal == undefined ? 0 : item.ysshSysj.xxTotal), 0).toNumber();
            sdjxTota = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.jxTotal == undefined ? 0 : item.jxTotal), 0).toNumber();
            sdxxTotal = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.xxTotal == undefined ? 0 : item.xxTotal), 0).toNumber();
            jxszjje = rcjComparison1.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.jxszjje == undefined ? 0 : item.ysshSysj.jxszjje), 0).toNumber();
        }

        let ysshSysj1 = {};
        ysshSysj1.totalNumber = "";
        ysshSysj1.taxRemoval = "";
        ysshSysj1.marketPrice = "";
        ysshSysj1.jxTotal = "";
        ysshSysj1.xxTotal = "";
        ysshSysj1.jxszjje = "";

        let insertObj1 = {};
        insertObj1.materialCode = "";
        insertObj1.materialName = type;
        insertObj1.unit = "";
        insertObj1.totalNumber = "";
        insertObj1.taxRemoval = "";
        insertObj1.marketPrice = "";
        insertObj1.jxTotal = "";
        insertObj1.xxTotal = "";
        insertObj1.ysshSysj = ysshSysj1;
        rcjComparison1.unshift(insertObj1);

        let ysshSysj2 = {};
        ysshSysj2.totalNumber = "";
        ysshSysj2.taxRemoval = "";
        ysshSysj2.marketPrice = "";
        ysshSysj2.jxTotal = ssjxTotal;
        ysshSysj2.xxTotal = ssxxTotal;
        ysshSysj2.jxszjje = jxszjje;

        let insertObj2 = {};
        insertObj2.materialCode = "";
        insertObj2.materialName = xiaoji;
        insertObj2.unit = "";
        insertObj2.totalNumber = "";
        insertObj2.taxRemoval = "";
        insertObj2.marketPrice = "";
        insertObj2.jxTotal = sdjxTota;
        insertObj2.xxTotal = sdxxTotal;
        insertObj2.ysshSysj = ysshSysj2;
        rcjComparison1.push(insertObj2);

        heji.ssjxTotal = NumberUtil.add(heji.ssjxTotal, ssjxTotal);
        heji.ssxxTotal = NumberUtil.add(heji.ssxxTotal, ssxxTotal);
        heji.sdjxTota = NumberUtil.add(heji.sdjxTota, sdjxTota);
        heji.sdxxTotal = NumberUtil.add(heji.sdxxTotal, sdxxTotal);
        heji.jxszjje = NumberUtil.add(heji.jxszjje, jxszjje);

        return heji;
    }

    async insertRcjTop(Sheet4List, num, type) {
        let ysshSysj1 = {};
        ysshSysj1.totalNumber = "";
        ysshSysj1.marketPrice = "";
        ysshSysj1.total = "";
        ysshSysj1.changePrice = "";
        ysshSysj1.changeTotal = "";
        ysshSysj1.changeExplain = "";

        let insertObj1 = {};
        insertObj1.dispNo = num;
        insertObj1.materialCode = "";
        insertObj1.materialName = type;
        insertObj1.specification = "";
        insertObj1.unit = "";
        insertObj1.dePrice = "";
        insertObj1.totalNumber = "";
        insertObj1.marketPrice = "";
        insertObj1.total = "";
        insertObj1.ysshSysj = ysshSysj1;
        Sheet4List.push(insertObj1);

        return Sheet4List;
    }

    async insertRcjTop9(Sheet4List, num, type) {
        let ysshSysj1 = {};
        ysshSysj1.totalNumber = "";
        ysshSysj1.marketPrice = "";
        ysshSysj1.total = "";
        ysshSysj1.changePrice = "";
        ysshSysj1.changeTotal = "";
        ysshSysj1.changeExplain = "";

        let insertObj1 = {};
        insertObj1.dispNo = num;
        insertObj1.materialCode = "";
        insertObj1.materialName = type;
        insertObj1.specification = "";
        insertObj1.unit = "";
        insertObj1.dePrice = "";
        insertObj1.totalNumber = "";
        insertObj1.marketPrice = "";
        insertObj1.total = "";
        insertObj1.ysshSysj = ysshSysj1;
        Sheet4List.push(insertObj1);

        return Sheet4List;
    }

    async getconstructSingleSheet1List(param) {
        let singleHeji = {};
        singleHeji.sstotal = 0;
        singleHeji.sdtotal = 0;
        singleHeji.changeTotal = 0;


        let arg = {};
        arg.levelType = 2;
        arg.constructId = param.constructId;
        arg.constructIdSD = param.sdConstructId;
        arg.ssConstructId = param.ssConstructId;
        arg.singleId = param.singleId;
        arg.ssSingleId = param.ssSingleId;

        let projectTotal = await this.service.shenHeYuSuanProject.ysshCostAnalysisService.getCostAnalysisData(arg);
        let result = projectTotal.result.costAnalysisSingleVOList;
        //todo
        let resultList = [];
        resultList.push(result);
        resultList = await this.getAllElement(result.childrenList,resultList);

        singleHeji.sstotal = Decimal.add(0, result.ysshSysj == undefined || result.ysshSysj.gczj == undefined ? 0 : result.ysshSysj.gczj).toNumber();
        singleHeji.sdtotal = Decimal.add(0, result.gczj == undefined ? 0 : result.gczj).toNumber();
        singleHeji.changeTotal = Decimal.add(0, result.changeTotal == undefined ? 0 : result.changeTotal).toNumber();

        param["singleHeji"] = singleHeji;
        return resultList;
    }


    async getconstructUnitSheet1List(param) {
        let arg1 = {};
        arg1.constructId = param.constructId;
        arg1.ssConstructId = param.ssConstructId;
        arg1.singleId = param.singleId;
        arg1.ssSingleId = param.ssSingleId;
        arg1.unitId = param.unitId;
        arg1.ssUnitId = param.ssUnitId;
        let responseData = await this.service.shenHeYuSuanProject.ysshCostSummaryService.getCostSummaryComparisonList(arg1);
        return responseData.result.filter(item =>ObjectUtils.isNotEmpty(item.sequenceNbr));

    }


    async getconstructUnitSheet2List(param) {
        let arg2 = {};
        arg2.pageNum = 1;
        arg2.pageSize = 300000;
        arg2.isAllFlag = true;
        arg2.constructId = param.constructId;
        arg2.ssConstructId = param.ssConstructId;
        arg2.singleId = param.singleId;
        arg2.ssSingleId = param.ssSingleId;
        arg2.unitId = param.unitId;
        arg2.ssUnitId = param.ssUnitId;
        let responseData = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxListComparison(arg2);

        let unitFbfxHeji = {};
        unitFbfxHeji.sstotal = 0;
        unitFbfxHeji.sdtotal = 0;
        unitFbfxHeji.changeTotal = 0;
        if (responseData != null && responseData.length > 0) {
            let jisuanList = responseData.filter(function (element) {
                if (!String(element.dispNo).includes(".") && !ObjectUtils.isNull(element.dispNo)) return element;
            });
            if (!ObjectUtils.isNull(jisuanList) && jisuanList.length > 0) {
                unitFbfxHeji.sstotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.total == undefined ? 0 : item.ysshSysj.total), 0).toNumber();
                unitFbfxHeji.sdtotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
                unitFbfxHeji.changeTotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined ? 0 : item.ysshSysj.changeTotal), 0).toNumber();
            }
        }
        param["unitFbfxHeji"] = unitFbfxHeji;

        return responseData;
    }


    async getconstructUnitSheet3List(param) {
        //查询分部分项合计
        await this.getconstructUnitSheet2List(param);

        // let args = {
        //     constructId:param.sdConstructId,
        //     singleId:
        //     unitId,
        //     ssConstructId,
        //     ssSingleId,
        //     ssUnitId
        // }
        param['pageNum'] = 1;
        param['pageSize'] = 300000;
        param['isAllFlag'] = true;
        let result = await this.service.shenHeYuSuanProject.ysshQdAssociationService.queryAllQdAssociationListForSheet(param);
        //拿到具有关联关系的清单
        let glqd = result.result.filter(item => ObjectUtils.isNotEmpty(item.ysshAssociation));
        let groupCollection = _.groupBy(glqd, i=>i[YsshssConstant.ysshSysj].sequenceNbr);
        let unit = PricingFileFindUtils.getUnit(param.ssConstructId, param.ssSingleId,param.ssUnitId);
        let map = new Map();
        for (let groupCollectionKey in groupCollection) {
            //得到审定的合计
            let total = groupCollection[groupCollectionKey].reduce( (sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
            //得到送审数据
            let itemBill = unit.itemBillProjects.filter(item => item.sequenceNbr == groupCollectionKey)[0];
            let changeTotal = NumberUtil.subtract(total,itemBill.total);
            map.set(groupCollectionKey,changeTotal);
        }
        for (let i = 0; i < result.result.length; i++) {
            //如果是有关联关系的清单
            if (ObjectUtils.isNotEmpty(result.result[i][YsshssConstant.ysshSysj]) && map.has(result.result[i][YsshssConstant.ysshSysj].sequenceNbr)) {
                //处理增减说明和增减金额
                if (ObjectUtils.isEmpty(result.result[i][YsshssConstant.ysshSysj].changeExplain)) {
                    result.result[i][YsshssConstant.ysshSysj].changeExplain = "一对多";
                }
                if (ObjectUtils.isEmpty(result.result[i][YsshssConstant.ysshSysj].changeTotal)) {
                    result.result[i][YsshssConstant.ysshSysj].changeTotal = map.get(result.result[i][YsshssConstant.ysshSysj].sequenceNbr);
                }

            }
        }
        return result.result;
    }


    async getconstructUnitSheet4List(param) {
        let arg4 = {};
        arg4.pageNum = 1;
        arg4.pageSize = 300000;
        arg4.constructId = param.constructId;
        arg4.ssConstructId = param.ssConstructId;
        arg4.singleId = param.singleId;
        arg4.ssSingleId = param.ssSingleId;
        arg4.unitId = param.unitId;
        arg4.ssUnitId = param.ssUnitId;
        let responseData = await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(arg4);
        let unitCuoshiHeji = {};
        unitCuoshiHeji.sstotal = 0;
        unitCuoshiHeji.sdtotal = 0;
        unitCuoshiHeji.changeTotal = 0;
        if (responseData != null && responseData.length > 0) {
            let jisuanList = responseData.filter(function (element) {
                if (!String(element.dispNo).includes(".") && !ObjectUtils.isNull(element.dispNo)) return element;
            });
            if (!ObjectUtils.isNull(jisuanList) && jisuanList.length > 0) {
                unitCuoshiHeji.sstotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.total == undefined ? 0 : item.ysshSysj.total), 0).toNumber();
                unitCuoshiHeji.sdtotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
                unitCuoshiHeji.changeTotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined ? 0 : item.ysshSysj.changeTotal), 0).toNumber();
            }
        }
        param["unitCuoshiHeji"] = unitCuoshiHeji;
        return responseData;
    }


    async getconstructUnitSheet5List(param) {
        let arg4 = {};
        arg4.constructId = param.constructId;
        arg4.ssConstructId = param.ssConstructId;
        arg4.singleId = param.singleId;
        arg4.ssSingleId = param.ssSingleId;
        arg4.unitId = param.unitId;
        arg4.ssUnitId = param.ssUnitId;
        arg4.levelType = 4;
        let otherList = await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectComparisonList(arg4);

        let unitOtherHeji = {};
        unitOtherHeji.sstotal = 0;
        unitOtherHeji.sdtotal = 0;
        unitOtherHeji.changeTotal = 0;
        if (otherList.result != null && otherList.result.length > 0) {
            let jisuanList = otherList.result.filter(function (element) {
                if (!String(element.dispNo).includes(".")) return element;
            });
            unitOtherHeji.sstotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.total == undefined ? 0 : item.ysshSysj.total), 0).toNumber();
            unitOtherHeji.sdtotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
            unitOtherHeji.changeTotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined ? 0 : item.ysshSysj.changeTotal), 0).toNumber();
        }
        param["unitOtherHeji"] = unitOtherHeji;

        return otherList.result;
    }


    async getconstructUnitSheet6List(param) {
        let arg5 = {};
        arg5.constructId = param.constructId;
        arg5.ssConstructId = param.ssConstructId;
        arg5.singleId = param.singleId;
        arg5.ssSingleId = param.ssSingleId;
        arg5.unitId = param.unitId;
        arg5.ssUnitId = param.ssUnitId;
        arg5.levelType = 4;
        let otherJrgList = await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectDayWorkComparisonList(arg5);

        let unitOtherJrgHeji = {};
        unitOtherJrgHeji.sstotal = 0;
        unitOtherJrgHeji.sdtotal = 0;
        unitOtherJrgHeji.changeTotal = 0;
        let resultList = [];
        if (otherJrgList.result != null && otherJrgList.result.length > 0) {
            let jisuanList = otherJrgList.result.filter(function (element) {
                if (!String(element.dispNo).includes(".")) return element;
            });
            unitOtherJrgHeji.sstotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.total == undefined ? 0 : item.ysshSysj.total), 0).toNumber();
            unitOtherJrgHeji.sdtotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
            unitOtherJrgHeji.changeTotal = jisuanList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined ? 0 : item.ysshSysj.changeTotal), 0).toNumber();

            let insertXiaojiYsshSysj = {};
            insertXiaojiYsshSysj.total = 0;
            insertXiaojiYsshSysj.changeTotal = 0;
            let insertXiaoji = {};
            insertXiaoji.worksName = "小计";
            insertXiaoji.ysshSysj = insertXiaojiYsshSysj
            insertXiaoji.total = 0;
            for (let i = 0; i < otherJrgList.result.length; i++) {
                let resultElement = otherJrgList.result[i];
                if (!String(resultElement.ysshSysj.dispNo).includes(".")) {
                    if (i > 0) {
                        let deepCopy1 = await this.deepCopy(insertXiaoji);
                        resultList.push(deepCopy1);
                    }

                    insertXiaoji.ysshSysj.total = resultElement.ysshSysj.total;
                    insertXiaoji.ysshSysj.changeTotal = resultElement.ysshSysj.changeTotal;
                    insertXiaoji.total = resultElement.total;
                }
                resultList.push(resultElement);
                if (i == otherJrgList.result.length - 1) {
                    let deepCopy1 = await this.deepCopy(insertXiaoji);
                    resultList.push(deepCopy1);
                }
            }
        }
        param["unitOtherJrgHeji"] = unitOtherJrgHeji;

        return resultList;
    }

    async getconstructUnitSheet7List(param) {

        let heji = {};
        heji.sstotal = 0;
        heji.sdtotal = 0;
        heji.changePrice = 0;
        heji.changeTotal = 0;

        let Sheet4List = [];
        //人工
        let arg1 = {};
        arg1.type = 2;
        arg1.kind = 1;
        arg1.constructId = param.constructId;
        arg1.ssConstructId = param.ssConstructId;
        arg1.singleId = param.singleId;
        arg1.ssSingleId = param.ssSingleId;
        arg1.unitId = param.unitId;
        arg1.ssUnitId = param.ssUnitId;
        let rcjComparison1 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg1);
        if (rcjComparison1 != null && rcjComparison1.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison1, "一、", "人工", "人工小计");
            let num1 = 0;
            rcjComparison1.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num1++;
                    o.dispNo = num1;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "一、", "人工");
        }

        //材料
        let arg2 = {};
        arg2.type = 2;
        arg2.kind = 2;
        arg2.constructId = param.constructId;
        arg2.ssConstructId = param.ssConstructId;
        arg2.singleId = param.singleId;
        arg2.ssSingleId = param.ssSingleId;
        arg2.unitId = param.unitId;
        arg2.ssUnitId = param.ssUnitId;
        let rcjComparison2 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg2);
        if (rcjComparison2 != null && rcjComparison2.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison2, "二、", "材料", "材料小计");
            let num2 = 0;
            rcjComparison2.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num2++;
                    o.dispNo = num2;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "二、", "材料");
        }

        //机械
        let arg3 = {};
        arg3.type = 2;
        arg3.kind = 3;
        arg3.constructId = param.constructId;
        arg3.ssConstructId = param.ssConstructId;
        arg3.singleId = param.singleId;
        arg3.ssSingleId = param.ssSingleId;
        arg3.unitId = param.unitId;
        arg3.ssUnitId = param.ssUnitId;
        let rcjComparison3 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg3);
        if (rcjComparison3 != null && rcjComparison3.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison3, "三、", "机械", "机械小计");
            let num3 = 0;
            rcjComparison3.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num3++;
                    o.dispNo = num3;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "三、", "机械");
        }

        //设备
        let arg4 = {};
        arg4.type = 2;
        arg4.kind = 4;
        arg4.constructId = param.constructId;
        arg4.ssConstructId = param.ssConstructId;
        arg4.singleId = param.singleId;
        arg4.ssSingleId = param.ssSingleId;
        arg4.unitId = param.unitId;
        arg4.ssUnitId = param.ssUnitId;
        let rcjComparison4 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg4);
        if (rcjComparison4 != null && rcjComparison4.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison4, "四、", "设备", "设备小计");
            let num4 = 0;
            rcjComparison4.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num4++;
                    o.dispNo = num4;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "四、", "设备");
        }

        //主材
        let arg5 = {};
        arg5.type = 2;
        arg5.kind = 5;
        arg5.constructId = param.constructId;
        arg5.ssConstructId = param.ssConstructId;
        arg5.singleId = param.singleId;
        arg5.ssSingleId = param.ssSingleId;
        arg5.unitId = param.unitId;
        arg5.ssUnitId = param.ssUnitId;
        let rcjComparison5 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg5);
        if (rcjComparison5 != null && rcjComparison5.length > 0) {
            heji = await this.insertTopAndBottom(heji, rcjComparison5, "五、", "主材", "主材小计");
            let num5 = 0;
            rcjComparison5.forEach(o => {
                if (o.dispNo == undefined || o.dispNo == null) {
                    num5++;
                    o.dispNo = num5;
                }
                Sheet4List.push(o);
            })
        } else {
            await this.insertRcjTop(Sheet4List, "五、", "主材");
        }

        // //预拌混凝土
        // let arg6 = {};
        // arg6.type = 1;
        // arg5.kind = 5;
        // arg6.sdConstructId = param.sdConstructId;
        // arg6.ssConstructId = param.ssConstructId;
        // arg6.sdSingleId = param.sdSingleId;
        // arg6.ssSingleId = param.ssSingleId;
        // arg6.sdUnitId = param.sdUnitId;
        // arg6.ssUnitId = param.ssUnitId;
        // let rcjComparison6 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg6);
        // if (rcjComparison6 != null && rcjComparison6.length > 0) {
        //     heji = await this.insertTopAndBottom(heji, rcjComparison6, "六、", "预拌混凝土");
        //     let num6 = 0;
        //     rcjComparison6.forEach(o => {
        //         if (o.dispNo == undefined || o.dispNo == null) {
        //             num6++;
        //             o.dispNo = num6;
        //         }
        //         Sheet4List.push(o);
        //     })
        // }

        param['unitRcjHeji1'] = heji;

        return Sheet4List;
    }

    async getconstructUnitSheet8List(param) {
        let Sheet4List = [];
        //人工
        let arg1 = {};
        arg1.type = 2;
        arg1.kind = 1;
        arg1.constructId = param.constructId;
        arg1.ssConstructId = param.ssConstructId;
        arg1.singleId = param.singleId;
        arg1.ssSingleId = param.ssSingleId;
        arg1.unitId = param.unitId;
        arg1.ssUnitId = param.ssUnitId;
        let rcjComparison1 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg1);
        await this.insertRcjTop(Sheet4List, "一", "人工");
        let dispNoSort = 0;
        if (rcjComparison1 != null && rcjComparison1.length > 0) {
            rcjComparison1.forEach(item => {
                item.dispNo = dispNoSort++;
                item.dwjc = NumberUtil.subtract(item.marketPrice, item.dePrice);   //审定单位价差
                item.jchj = NumberUtil.multiply(item.dwjc, item.totalNumber).toFixed(2);   //审定价差合计
                if (undefined != item.ysshSysj) {
                    item.ysshSysj.dwjc = NumberUtil.subtract(item.ysshSysj.marketPrice, item.dePrice);   //送审单位价差
                    item.ysshSysj.jchj = NumberUtil.multiply(item.ysshSysj.dwjc, item.ysshSysj.totalNumber).toFixed(2);   //送审价差合计
                    item.ysshSysj.zjjc = NumberUtil.subtract(item.jchj, item.ysshSysj.jchj);   //送审增减价差
                }
                Sheet4List.push(item);
            })
        }

        //材料
        let arg2 = {};
        arg2.type = 2;
        arg2.kind = 2;
        arg2.constructId = param.constructId;
        arg2.ssConstructId = param.ssConstructId;
        arg2.singleId = param.singleId;
        arg2.ssSingleId = param.ssSingleId;
        arg2.unitId = param.unitId;
        arg2.ssUnitId = param.ssUnitId;
        let rcjComparison2 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg2);
        await this.insertRcjTop(Sheet4List, "二", "材料");
        if (rcjComparison2 != null && rcjComparison2.length > 0) {
            dispNoSort = 0;
            rcjComparison2.forEach(item => {
                dispNoSort++;
                item.dispNo = dispNoSort;
                item.dwjc = NumberUtil.subtract(item.marketPrice, item.dePrice);   //审定单位价差
                item.jchj = NumberUtil.multiply(item.dwjc, item.totalNumber).toFixed(2);   //审定价差合计
                if (undefined != item.ysshSysj) {
                    item.ysshSysj.dwjc = NumberUtil.subtract(item.ysshSysj.marketPrice, item.dePrice);   //送审单位价差
                    item.ysshSysj.jchj = NumberUtil.multiply(item.ysshSysj.dwjc, item.ysshSysj.totalNumber).toFixed(2);   //送审价差合计
                    item.ysshSysj.zjjc = NumberUtil.subtract(item.jchj, item.ysshSysj.jchj);   //送审增减价差
                }
                Sheet4List.push(item);
            })

        }

        //机械
        let arg3 = {};
        arg3.type = 2;
        arg3.kind = 3;
        arg3.constructId = param.constructId;
        arg3.ssConstructId = param.ssConstructId;
        arg3.singleId = param.singleId;
        arg3.ssSingleId = param.ssSingleId;
        arg3.unitId = param.unitId;
        arg3.ssUnitId = param.ssUnitId;
        let rcjComparison3 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg3);
        await this.insertRcjTop(Sheet4List, "三", "机械");
        if (rcjComparison3 != null && rcjComparison3.length > 0) {
            dispNoSort = 0;
            rcjComparison3.forEach(item => {
                dispNoSort++;
                item.dispNo = dispNoSort;
                item.dwjc = NumberUtil.subtract(item.marketPrice, item.dePrice);   //审定单位价差
                item.jchj = NumberUtil.multiply(item.dwjc, item.totalNumber).toFixed(2);   //审定价差合计
                if (undefined != item.ysshSysj) {
                    item.ysshSysj.dwjc = NumberUtil.subtract(item.ysshSysj.marketPrice, item.dePrice);   //送审单位价差
                    item.ysshSysj.jchj = NumberUtil.multiply(item.ysshSysj.dwjc, item.ysshSysj.totalNumber).toFixed(2);   //送审价差合计
                    item.ysshSysj.zjjc = NumberUtil.subtract(item.jchj, item.ysshSysj.jchj);   //送审增减价差
                }
                Sheet4List.push(item);
            })

        }

        //设备
        let arg4 = {};
        arg4.type = 2;
        arg4.kind = 4;
        arg4.constructId = param.constructId;
        arg4.ssConstructId = param.ssConstructId;
        arg4.singleId = param.singleId;
        arg4.ssSingleId = param.ssSingleId;
        arg4.unitId = param.unitId;
        arg4.ssUnitId = param.ssUnitId;
        let rcjComparison4 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg4);
        await this.insertRcjTop(Sheet4List, "四", "设备");
        if (rcjComparison4 != null && rcjComparison4.length > 0) {
            dispNoSort = 0;
            rcjComparison4.forEach(item => {
                dispNoSort++;
                item.dispNo = dispNoSort;
                item.dwjc = NumberUtil.subtract(item.marketPrice, item.dePrice);   //审定单位价差
                item.jchj = NumberUtil.multiply(item.dwjc, item.totalNumber).toFixed(2);   //审定价差合计
                if (undefined != item.ysshSysj) {
                    item.ysshSysj.dwjc = NumberUtil.subtract(item.ysshSysj.marketPrice, item.dePrice);   //送审单位价差
                    item.ysshSysj.jchj = NumberUtil.multiply(item.ysshSysj.dwjc, item.ysshSysj.totalNumber).toFixed(2);   //送审价差合计
                    item.ysshSysj.zjjc = NumberUtil.subtract(item.jchj, item.ysshSysj.jchj);   //送审增减价差
                }
                Sheet4List.push(item);
            })

        }

        //主材
        let arg5 = {};
        arg5.type = 2;
        arg5.kind = 5;
        arg5.constructId = param.constructId;
        arg5.ssConstructId = param.ssConstructId;
        arg5.singleId = param.singleId;
        arg5.ssSingleId = param.ssSingleId;
        arg5.unitId = param.unitId;
        arg5.ssUnitId = param.ssUnitId;
        let rcjComparison5 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg5);
        await this.insertRcjTop(Sheet4List, "五", "主材");
        if (rcjComparison5 != null && rcjComparison5.length > 0) {
            dispNoSort = 0;
            rcjComparison5.forEach(item => {
                dispNoSort++;
                item.dispNo = dispNoSort;
                item.dwjc = NumberUtil.subtract(item.marketPrice, item.dePrice);   //审定单位价差
                item.jchj = NumberUtil.multiply(item.dwjc, item.totalNumber).toFixed(2);   //审定价差合计
                if (undefined != item.ysshSysj) {
                    item.ysshSysj.dwjc = NumberUtil.subtract(item.ysshSysj.marketPrice, item.dePrice);   //送审单位价差
                    item.ysshSysj.jchj = NumberUtil.multiply(item.ysshSysj.dwjc, item.ysshSysj.totalNumber).toFixed(2);   //送审价差合计
                    item.ysshSysj.zjjc = NumberUtil.subtract(item.jchj, item.ysshSysj.jchj);   //送审增减价差
                }
                Sheet4List.push(item);
            })
        }

        //预拌混凝土
        // let arg6 = {};
        // arg6.type = 1;
        // arg5.kind = 5;
        // arg6.sdConstructId = param.sdConstructId;
        // arg6.ssConstructId = param.ssConstructId;
        // arg6.sdSingleId = param.sdSingleId;
        // arg6.ssSingleId = param.ssSingleId;
        // arg6.sdUnitId = param.sdUnitId;
        // arg6.ssUnitId = param.ssUnitId;
        // let rcjComparison6 = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(arg6);
        // if (rcjComparison6 != null && rcjComparison6.length > 0) {
        //     heji = await this.insertTopAndBottom(heji, rcjComparison6, "六、", "预拌混凝土");
        //     let num6 = 0;
        //     rcjComparison6.forEach(o => {
        //         if (o.dispNo == undefined || o.dispNo == null) {
        //             num6++;
        //             o.dispNo = num6;
        //         }
        //         Sheet4List.push(o);
        //     })
        // }

        let heji = {};
        heji.ssjchi = 0;
        heji.sdjchj = 0;
        heji.zjjc = 0;
        if (Sheet4List != null && Sheet4List.length > 0) {
            heji.ssjchi = Sheet4List.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.jchj == undefined ? 0 : item.ysshSysj.jchj), 0).toNumber();
            heji.sdjchj = Sheet4List.reduce((sum, item) => Decimal.add(sum, item.jchj == "" || item.jchj == undefined ? 0 : item.jchj), 0).toNumber();
            heji.zjjc = Sheet4List.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.zjjc == undefined ? 0 : item.ysshSysj.zjjc), 0).toNumber();
        }
        param['unitRcjHeji2'] = heji;

        return Sheet4List;
    }


    async getconstructUnitSheet9List(param) {
        let heji = {};
        heji.ssjxTotal = 0;
        heji.ssxxTotal = 0;
        heji.sdjxTota = 0;
        heji.sdxxTotal = 0;
        heji.jxszjje = 0;

        let newVar = await this.getShenHeClJxSbZzsjsb(param.constructId, param.singleId, param.unitId, param.ssConstructId, param.ssSingleId, param.ssUnitId);
        let result = [];

        if (null == newVar) {
            param['unitcljxsbHeji'] = heji;
            return result;
        }

        if (newVar.clList.length > 0) {
            heji = await this.insertTopAndBottom9(heji, newVar.clList, "", "材料", "小计");
            for (let i = 0; i < newVar.clList.length; i++) {
                let element = newVar.clList[i];
                result.push(element);
            }
        } else {
            await this.insertRcjTop9(result, "", "材料");
        }


        if (newVar.jxList.length > 0) {
            heji = await this.insertTopAndBottom9(heji, newVar.jxList, "", "机械", "小计");
            for (let i = 0; i < newVar.jxList.length; i++) {
                let element = newVar.jxList[i];
                result.push(element);
            }
        } else {
            await this.insertRcjTop9(result, "", "机械");
        }


        if (newVar.sbList.length > 0) {
            heji = await this.insertTopAndBottom9(heji, newVar.sbList, "", "设备", "小计");
            for (let i = 0; i < newVar.sbList.length; i++) {
                let element = newVar.sbList[i];
                result.push(element);
            }
        } else {
            await this.insertRcjTop9(result, "", "设备");
        }

        param['unitcljxsbHeji'] = heji;

        return result;
    }


    async getconstructUnitSheet10List(param) {
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId} = param;
        const ssGfeeResult = await this.service.gfeeService.getGfeeFee({
            constructId: ssConstructId,
            singleId: ssSingleId,
            unitId: ssUnitId
        });
        const sdGfeeResult = await this.service.gfeeService.getGfeeFee({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });

        let resultListObj = await this.service.shenHeYuSuanProject.ysshCostSummaryService.getGfeeFeeComparisonData({
            ssGfeeResult,
            sdGfeeResult
        });

        let resultList = resultListObj.result;
        let unitGuifeiHeji = {};
        unitGuifeiHeji.sstotal = 0;
        unitGuifeiHeji.sdtotal = 0;
        unitGuifeiHeji.changeTotal = 0;
        if (resultList != null && resultList.length > 0) {
            unitGuifeiHeji.sstotal = resultList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.costFeeBase == undefined || item.ysshSysj.costFeeBase == "" ? 0 : item.ysshSysj.costFeeBase), 0).toNumber();
            unitGuifeiHeji.sdtotal = resultList.reduce((sum, item) => Decimal.add(sum, item.costFeeBase == undefined || item.costFeeBase == "" ? 0 : item.costFeeBase), 0).toNumber();
            unitGuifeiHeji.changeTotal = resultList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined || item.ysshSysj.changeTotal == "" ? 0 : item.ysshSysj.changeTotal), 0).toNumber();
        }
        param["unitGuifeiHeji"] = unitGuifeiHeji;


        return resultList;
    }

    async getconstructUnitSheet11List(param) {
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId} = param;
        const ssSafeFeeResult = await this.service.safeFeeService.getSafeFee({
            constructId: ssConstructId,
            singleId: ssSingleId,
            unitId: ssUnitId
        });
        const sdSafeFeeResult = await this.service.safeFeeService.getSafeFee({
            constructId: constructId,
            singleId: singleId,
            unitId: unitId
        });
        let resultListObj = await this.service.shenHeYuSuanProject.ysshCostSummaryService.getSafeFeeComparisonData({
            ssSafeFeeResult,
            sdSafeFeeResult
        });

        let resultList = resultListObj.result;
        let unitAnwenfeiHeji = {};
        unitAnwenfeiHeji.sstotal = 0;
        unitAnwenfeiHeji.sdtotal = 0;
        unitAnwenfeiHeji.changeTotal = 0;
        if (resultList != null && resultList.length > 0) {
            unitAnwenfeiHeji.sstotal = resultList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.costFeeBase == undefined || item.ysshSysj.costFeeBase == "" ? 0 : item.ysshSysj.costFeeBase), 0).toNumber();
            unitAnwenfeiHeji.sdtotal = resultList.reduce((sum, item) => Decimal.add(sum, item.costFeeBase == undefined || item.costFeeBase == "" ? 0 : item.costFeeBase), 0).toNumber();
            unitAnwenfeiHeji.changeTotal = resultList.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.changeTotal == undefined || item.ysshSysj.changeTotal == "" ? 0 : item.ysshSysj.changeTotal), 0).toNumber();
        }
        param["unitAnwenfeiHeji"] = unitAnwenfeiHeji;

        return resultList;
    }


    async getconstructUnitSheet12List(param) {
        let resultList = [];

        //分部分项
        let arg2 = {};
        arg2.pageNum = 1;
        arg2.pageSize = 300000;
        arg2.isAllFlag = true;
        arg2.constructId = param.constructId;
        arg2.ssConstructId = param.ssConstructId;
        arg2.singleId = param.singleId;
        arg2.ssSingleId = param.ssSingleId;
        arg2.unitId = param.unitId;
        arg2.ssUnitId = param.ssUnitId;
        let responseDataFbfx = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxListComparison(arg2);
        if (responseDataFbfx != null && responseDataFbfx.length > 0) {
            for (let j = 0; j < responseDataFbfx.length; j++) {
                let dataFbfx = responseDataFbfx[j];
                let gclOBJ = {};
                gclOBJ.dispNo = dataFbfx.bdCode;
                if (undefined != dataFbfx.redArray && dataFbfx.redArray.length > 0) {
                    gclOBJ.dispNo = gclOBJ.dispNo + dataFbfx.redArray;
                }
                if (undefined != dataFbfx.blackArray && dataFbfx.blackArray.length > 0) {
                    gclOBJ.dispNo = gclOBJ.dispNo + dataFbfx.blackArray;
                }
                gclOBJ.bdName = dataFbfx.bdName;
                if (undefined != dataFbfx.projectAttr && null != dataFbfx.projectAttr) {
                    gclOBJ.bdName = gclOBJ.bdName + "\t\n" + dataFbfx.projectAttr;
                }
                gclOBJ.quantity = dataFbfx.quantity;
                gclOBJ.unit = dataFbfx.unit;
                gclOBJ.quantityExpression = dataFbfx.quantityExpression;
                gclOBJ.xj = await this.caclGclXiaoji(gclOBJ.quantity, gclOBJ.unit);
                resultList.push(gclOBJ);
            }
        }


        //措施项目
        let arg4 = {};
        arg4.pageNum = 1;
        arg4.pageSize = 300000;
        arg4.constructId = param.constructId;
        arg4.ssConstructId = param.ssConstructId;
        arg4.singleId = param.singleId;
        arg4.ssSingleId = param.ssSingleId;
        arg4.unitId = param.unitId;
        arg4.ssUnitId = param.ssUnitId;
        let responseDataCuoshi = await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(arg4);
        if (responseDataCuoshi != null && responseDataCuoshi.length > 0) {
            for (let i = 0; i < responseDataCuoshi.length; i++) {
                let cuoshiElement = responseDataCuoshi[i];
                let gclOBJ = {};
                gclOBJ.dispNo = cuoshiElement.fxCode;
                gclOBJ.bdName = cuoshiElement.name;
                gclOBJ.quantity = cuoshiElement.quantity;
                gclOBJ.unit = cuoshiElement.unit;
                gclOBJ.quantityExpression = cuoshiElement.quantityExpression;
                gclOBJ.xj = await this.caclGclXiaoji(gclOBJ.quantity, gclOBJ.unit);
                resultList.push(gclOBJ);
            }
        }

        return resultList;
    }

    async caclGclXiaoji(quantity, unit) {
        let xiaoji = "";
        if (undefined != quantity && quantity != null && quantity != "") {     //没有工程量时
            if (!/^[\d]/.test(unit)) {   //当单位不是数字开头
                xiaoji = String(quantity);
            } else {
                let newVar = unit.match(/\d+(\.\d+)?/g) || [];
                if (newVar.length > 0) {
                    xiaoji = String(NumberUtil.multiply(newVar[0], quantity).toFixed(2));
                }
            }
        }

        return xiaoji;
    }


    async getconstructUnitSheet13List(param) {
        let newVar = await this.getShenHeZzsJxs(param.constructId, param.singleId, param.unitId, param.ssConstructId, param.ssSingleId, param.ssUnitId);
        let unitZzsjxseHeji = {};
        unitZzsjxseHeji.sstotal = 0;
        unitZzsjxseHeji.sdtotal = 0;
        unitZzsjxseHeji.zjje = 0;
        if (newVar.list != null && newVar.list.length > 0) {
            unitZzsjxseHeji.sstotal = newVar.list.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.total == undefined ? 0 : item.ysshSysj.total), 0).toNumber();
            unitZzsjxseHeji.sdtotal = newVar.list.reduce((sum, item) => Decimal.add(sum, item.total == undefined ? 0 : item.total), 0).toNumber();
            unitZzsjxseHeji.zjje = newVar.list.reduce((sum, item) => Decimal.add(sum, item.ysshSysj == undefined || item.ysshSysj.zjje == undefined ? 0 : item.ysshSysj.zjje), 0).toNumber();
        }
        param["unitZzsjxseHeji"] = unitZzsjxseHeji;
        return newVar.list;
    }


    getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
        return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async showSheetStyleSample() {

        let excelPath = this.getProjectRootPath() + "\\excelTemplate\\unit\\sample.xlsx";
        let sheetName = "表1-6 分部分项工程量清单与计价表";
        let worksheet = await ShenHeExcelUtil.read(excelPath, sheetName);
        let result = await ShenHeExcelUtil.findCellStyleList(worksheet);
        return ResponseData.success(result);
    }

    //获取措施项目对应的清单和定额
    getCSXMQdAndDe(allData, pointLine, unInfluce) {
        if (pointLine.kind === BranchProjectLevelConstant.top) {
            return {
                "begin": 0, "end": allData.length, "datas": allData
            };
        }
        if (pointLine.kind === BranchProjectLevelConstant.fb || pointLine.kind === BranchProjectLevelConstant.zfb || pointLine.kind === BranchProjectLevelConstant.qd) {
            let begin = -1;
            let end = allData.length;
            let parents = this.findParents(allData, pointLine);
            for (let index = 0; index < allData.length; ++index) {
                if (allData[index].sequenceNbr === pointLine.sequenceNbr) {
                    begin = index;
                    continue;
                }
                if (begin > -1) {
                    // 1.下一行级别高于当前行，2.parentId在当前行的 [parentIds] 中 则认为结束
                    if (Number.parseInt(pointLine.kind) > Number.parseInt(allData[index].kind) || parents[allData[index].parentId]) {
                        end = index;
                        break;
                    }
                }
            }
            return {
                "begin": begin, "end": end, "datas": allData.slice(begin, end)
            }; // [bengin, end) bengin为被选中的行
        }
        return {
            "begin": 0, "end": 0, "datas": this.findLine(allData, pointLine)
        };
    }

    findParents(allData, line) {
        let parents = {};
        parents[line.parentId] = true;
        let lineIndex;
        let buffer = line;
        for (let index = 0; index < allData.length; ++index) {
            if (line.sequenceNbr === allData[index].sequenceNbr) {
                lineIndex = index;
                break;
            }
        }
        while (lineIndex > 0) {
            if (allData[lineIndex].sequenceNbr === buffer.parentId) {
                buffer = allData[lineIndex];
                parents[buffer.parentId] = true;
            }
            --lineIndex;
        }

        return parents;
    }

    findLine(allData, pointLine) {
        let res = [];
        for (let i = 0; i < allData.length; ++i) {
            if (pointLine.sequenceNbr === allData[i].sequenceNbr) {
                res.push(allData[i]);
            }
        }
        return res;
    }

    /**
     * 获取审核 材料、机械、设备增值税计算表
     * @param sdConstructId
     * @param sdSingleId
     * @param sdUnitId
     * @param ssConstructId
     * @param ssSingleId
     * @param ssUnitId
     * @returns {Promise<{}|null>}
     */
    async getShenHeClJxSbZzsjsb(sdConstructId, sdSingleId, sdUnitId, ssConstructId, ssSingleId, ssUnitId) {
        let jsb = {};
        let unit = PricingFileFindUtils.getUnit(sdConstructId, sdSingleId, sdUnitId);
        let single = PricingFileFindUtils.getSingleProject(sdConstructId, sdSingleId);

        //单位工程名称
        if (single != null) {
            jsb.name = `${single.projectName} ${unit.upName}`;
        } else {
            jsb.name = `${unit.upName}`;
        }


        let args = {};
        args.ssConstructId = ssConstructId;
        args.ssSingleId = ssSingleId;
        args.ssUnitId = ssUnitId;
        args.constructId = sdConstructId;
        args.singleId = sdSingleId;
        args.unitId = sdUnitId;
        args.type = 1;
        args.kind = null;

        //单位人材机汇总
        let unitConstructProjectRcjQuery = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(args);
        if (ObjectUtils.isEmpty(unitConstructProjectRcjQuery)) {
            return null;
        }

        //过滤 解析的父类
        let ts = unitConstructProjectRcjQuery.filter(i => !(i.markSum === 1 && (i.levelMark === 1 || i.levelMark === 2)));

        let array = new Array();
        for (let t of ts) {
            let dx = {};
            dx.materialCode = t.materialCode;
            dx.materialName = ObjectUtils.isEmpty(t.specification) ? t.materialName : t.materialName + " " + t.specification;
            dx.unit = t.unit;
            //数量
            dx.totalNumber = t.totalNumber;
            //除税系数
            dx.taxRemoval = t.taxRemoval;
            //含税价格
            dx.marketPrice = t.marketPrice;
            //含税合计
            dx.total = t.total;
            //除税价格
            dx.csPrice = null;
            //除税价格合计
            dx.csTotal = null;
            //进项税额合计
            dx.jxTotal = t.jxTotal;
            //销项税额合计
            dx.xxTotal = null;

            dx.kind = t.kind;

            let sssj = {};
            //送审数据
            let ssRcj = t[YsshssConstant.ysshSysj];

            //数量
            sssj.totalNumber = ssRcj.totalNumber;
            //除税系数
            sssj.taxRemoval = ssRcj.taxRemoval;
            //含税价格
            sssj.marketPrice = ssRcj.marketPrice;
            //进项税额合计
            sssj.jxTotal = ssRcj.jxTotal;
            //销项税额合计
            sssj.xxTotal = null;
            //进项税增减金额
            sssj.jxszjje = NumberUtil.subtract(t.jxTotal, ssRcj.jxTotal);

            dx[YsshssConstant.ysshSysj] = sssj;

            array.push(dx);
        }
        //材料部分
        let clList = array.filter(i => i.kind !== 1 && i.kind !== 3 && i.kind !== 4 && i.kind !== 5);
        // await this.getListTotal(clList);

        //机械部分
        let jxList = array.filter(i => i.kind === 3);
        // await this.getListTotal(jxList);

        //设备部分
        let sbList = array.filter(i => i.kind === 4);
        // await this.getListTotal(sbList);

        jsb.clList = clList;
        jsb.jxList = jxList;
        jsb.sbList = sbList;

        return jsb;

    }


    /**
     * 审核 增值税进项税额计算汇总表
     * @param sdConstructId
     * @param sdSingleId
     * @param sdUnitId
     * @param ssConstructId
     * @param ssSingleId
     * @param ssUnitId
     * @returns {Promise<{}>}
     */
    async getShenHeZzsJxs(sdConstructId, sdSingleId, sdUnitId, ssConstructId, ssSingleId, ssUnitId) {
        let sjb = {};
        let unit = PricingFileFindUtils.getUnit(sdConstructId, sdSingleId, sdUnitId);
        let single = PricingFileFindUtils.getSingleProject(sdConstructId, sdSingleId);

        //单位工程名称
        if (single != null) {
            sjb.name = `${single.projectName} ${unit.upName}`;
        } else {
            sjb.name = `${unit.upName}`;
        }


        let unitCostCodePrices = unit.unitCostCodePrices;
        let array = new Array();
        //送审单位工程
        let ssUnit = ssUnitId ? PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId) : null;

        for (let unitCostCodePrice of unitCostCodePrices) {
            let sort = null;
            switch (unitCostCodePrice.code) {
                case "CLFJXSE":
                    sort = 1;
                    break;
                case "JXFJXSE":
                    sort = 2;
                    break;
                case "SBFJXSE":
                    sort = 3;
                    break;
                case "AQWMSGFJXSE":
                    sort = 4;
                    break;
                case "FLJXSE":
                    sort = 5;
                    break;
                case "GLFJXSE":
                    sort = 6;
                    break;
                case "ZLJEJXSE":
                    sort = 7;
                    break;
                case "ZYGCZGJJXSE":
                    sort = 8;
                    break;
                case "JRGJXSE":
                    sort = 9;
                    break;
            }

            if (!ObjectUtils.isEmpty(sort)) {
                let dx = {};
                dx.sortNo = sort;
                dx.name = unitCostCodePrice.name;
                dx.total = unitCostCodePrice.price;

                let sssj = {};
                if (!ObjectUtils.isEmpty(ssUnit)) {
                    //送审对象
                    let ss = ssUnit.unitCostCodePrices.find(i => i.code == unitCostCodePrice.code);
                    //金额
                    sssj.total = !ObjectUtils.isEmpty(ss) ? ss.price : null;
                    //增减金额
                    sssj.zjje = NumberUtil.subtract(dx.total, sssj.total);
                } else {
                    //金额
                    sssj.total = null
                    //增减金额
                    sssj.zjje = dx.total;
                }
                dx[YsshssConstant.ysshSysj] = sssj;

                array.push(dx);
            }
        }

        sjb.list = array;

        return sjb;
    }
}


ShenHeExportViewService.toString = () => '[class ShenHeExportViewService]';
module.exports = ShenHeExportViewService;
