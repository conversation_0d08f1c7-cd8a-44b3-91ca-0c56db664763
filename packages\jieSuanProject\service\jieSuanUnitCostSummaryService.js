const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {UnitCostSummary} = require("../../../electron/model/UnitCostSummary");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Service} = require("../../../core");
const fyhz_ybjs_htw = require("../jsonData/fyhz_ybjs_htw_js.json");
const fyhz_ybjs_htn = require("../jsonData/fyhz_ybjs_htn_js.json");
const fyhz_jyjs_htw = require("../jsonData/fyhz_jyjs_htw_js.json");
const fyhz_jyjs_htn = require("../jsonData/fyhz_jyjs_htn_js.json");
const jxsmx = require("../jsonData/fyhz_jxsmx_jiesuan.json");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");

class jieSuanUnitCostSummaryService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    async initUnitCostSummary(args){
        let {constructId,singleId, unitId} = args;
        let unitObj=await PricingFileFindUtils.getUnit(constructId,singleId, unitId)
        //存储预算费用汇总值用来计算造价分析的值
        unitObj.unitCostSummarys_ys = ConvertUtil.deepCopy(unitObj.unitCostSummarys)
        await this.getDefaultUnitCostSummary(unitObj)
    }

    async getDefaultUnitCostSummary(unitObj) {

        let projectTaxCalculation = unitObj.projectTaxCalculation
        //费用代码
        let unitCostCodePriceArray = unitObj.unitCostCodePrices

        let args = {
            constructId: unitObj.constructId,
            singleId: unitObj.spId,
            unitId: unitObj.sequenceNbr
        }
        let template;
        //一般计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {

            template = '12_ybjs_fyhz_1'
            //仅12定额标准、一般计税情况下需要增加进项税明细
            await this.initInputTaxDetails(unitObj);
            await this.service.inputTaxDetailsService.countInputTaxDetails(unitObj);
        } else {
            template = '12_jyjs_fyhz_1'
        }
        args.template = template;
        let countUnitCostSummaryArray =ConvertUtil.deepCopy(unitObj.unitCostSummarys)//刚新建项目后，合同内的费率，取预算文件的值
        await this.jiesuanCostSummary(unitCostCodePriceArray,countUnitCostSummaryArray, unitObj, projectTaxCalculation);


        let resArray =await this.countUnitCostSummary(unitCostCodePriceArray, PricingFileFindUtils.getUnitCostSummary(unitObj.constructId,unitObj.spId, unitObj.sequenceNbr), unitObj,true);
        unitObj.unitCostSummarys = resArray;


    }

    jiesuanCostSummary(unitCostCodePriceArray,countUnitCostSummaryArray, unitObj, projectTaxCalculation) {
        for (const key in countUnitCostSummaryArray) {
            if (countUnitCostSummaryArray.hasOwnProperty(key)) {
                // 给每个属性的设置合同内
                countUnitCostSummaryArray[key].originalFlag = true;
            }
        }
        let fyhz = [];
        // 是否是合同内
        let originalFlag = unitObj.originalFlag
        //一般计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {

            if (originalFlag) {
                fyhz = fyhz_ybjs_htn;
            } else {
                fyhz = fyhz_ybjs_htw;
            }
        } else {
            if (originalFlag) {
                fyhz = fyhz_jyjs_htn;
            } else {
                fyhz = fyhz_jyjs_htw;
            }
        }
        //用于动态获取合计对应编码
        let lastCode = countUnitCostSummaryArray[countUnitCostSummaryArray.length-1].code
        let sort = countUnitCostSummaryArray.length + 1;
        let xxseSummary =  countUnitCostSummaryArray.find(item =>item.type =='销项税额');
        let fjsfSummary =  countUnitCostSummaryArray.find(item =>item.type =='附加税费');
        for (let i = 0; i < fyhz.length; i++) {
            sort++;
            let obj = new UnitCostSummary();
            ConvertUtil.setDstBySrc(fyhz[i], obj)
            obj.sequenceNbr = Snowflake.nextId();

            if(obj.name =='价差销项税额'){
                obj.rate = ObjectUtils.isEmpty(xxseSummary.rate)?100:xxseSummary.rate
            }else if(obj.name =='价差附加税费') {
                obj.rate = ObjectUtils.isEmpty(fjsfSummary.rate)?100:fjsfSummary.rate
            }else {
                obj.rate = 100;
            }


            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.price = 0;
            obj.unitId = unitObj.sequenceNbr;
            obj.originalFlag = true;
            obj.jieSuanPrice = 0;
            obj.sourceFlag = '1';

            countUnitCostSummaryArray.push(obj);
        }
        //一般计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {
            countUnitCostSummaryArray[countUnitCostSummaryArray.length-1].calculateFormula = lastCode+'+JCHJ'
        }else {
            countUnitCostSummaryArray[countUnitCostSummaryArray.length-1].calculateFormula = lastCode+'+JC+JCGF+JCAQWMSGF +JCSJ'
        }
        let resArray = this.countUnitCostSummary(unitCostCodePriceArray, countUnitCostSummaryArray, unitObj,false);
        unitObj.unitCostSummarys = resArray;

    }





    countUnitCostSummary(unitCostCodePriceArray, unitCostSummaryArray, unit,jieSuan) {
        //费用代码<费用代码,price>
        let priceMap = new Map();
        let jieSuanPriceMap =new Map();
        //计算基数 <费用汇总费用代号,calculateFormula>
        let codeFormulaMap = new Map();
        //费用汇总费率
        let codeRateMap = new Map();
        //费用代码
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            priceMap.set(unitCostCodePrice.code, unitCostCodePrice.price)
        }
        //费用汇总
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            let unitCostSummary=unitCostSummaryArray[i];
            codeFormulaMap.set(unitCostSummary.code,unitCostSummary.calculateFormula)
            codeRateMap.set(unitCostSummary.code,ObjectUtils.isEmpty(unitCostSummary.rate)?100:unitCostSummary.rate)
            if('增值税应纳税额'==unitCostSummary.type){
                if(unitCostSummary.price<0){
                    unitCostSummary.price =0
                    priceMap.set(unitCostSummary.code,unitCostSummary.price)
                }
            }

        }
        for (let i = 0; i < unitCostSummaryArray.length; i++) {
            let unitCostSummary = unitCostSummaryArray[i];
            //计算基数
            let calculateFormula = unitCostSummary.calculateFormula;
            if (ObjectUtils.isEmpty(calculateFormula)) {
                continue
            }
            // 分解字符串成表达式和变量名
            const variablesToReplace = calculateFormula.replace(/\s/g, '').split(/[\+\-\*\/\(\)]+/);
            //存放替换后的计算公式
            let afterCalculateFormula = calculateFormula;

            //递归计算费用汇总
            afterCalculateFormula = this.service.unitCostSummaryService.recursionSummary(calculateFormula, afterCalculateFormula, codeFormulaMap, priceMap, variablesToReplace, codeRateMap)



            let result =0;
            if (ObjectUtils.isNotEmpty(unitCostSummary.rate)) {
                result = parseFloat((eval(afterCalculateFormula) * unitCostSummary.rate / 100).toFixed(2));
            } else {
                result = NumberUtil.numberScale2(eval(afterCalculateFormula)) ;
            }
            if('增值税应纳税额'==unitCostSummary.type){
                //费用汇总中费用类别为“增值税应纳税额”，若费用汇总行对应该费用类别时，其计算结果小于0时，按0计算展示
                if(result<0){
                    result =0;
                    priceMap.set(unitCostSummary.code,result)
                }
            }

            //如果是赋值结算金额
            if(jieSuan){
                unitCostSummary.price = result; //【由于其他模块调用预算代码会调用预算费用代码计算修改 price值】
            }else {
                unitCostSummary.jieSuanPrice = result; //【由于其他模块调用预算代码会调用预算费用代码计算修改 price值】
            }


        }
        //更新造价分析
        this.setCostAnalysisData(unit, unitCostSummaryArray, unitCostCodePriceArray)
        return unitCostSummaryArray
    }

    initInputTaxDetails(unitObj){
        let inputTaxDetailsArray = new Array();
        let sort =1;
        for (let i in jxsmx) {
            sort++;
            let obj = new UnitCostSummary();
            ConvertUtil.setDstBySrc(jxsmx[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            obj.orderNum=sort;
            obj.unitId = unitObj.sequenceNbr;
            obj.price = 0;
            inputTaxDetailsArray.push(obj);
        }
        unitObj.inputTaxDetails = inputTaxDetailsArray;
    }



    /**
     * 选择费用汇总模板
     * @returns {Promise<void>}
     */
    async selectCostSummaryTemplate(args) {
        let template = args.template;
        //根据名称和路径获取模板数据
        let fyhz_template = require("../../../electron/jsonData/" + template + ".json");

        let unitObj = await PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
        let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(unitObj.constructId) == ConstantUtil.DE_STANDARD_22;
        let projectTaxCalculation = unitObj.projectTaxCalculation
        //费用代码
        let unitCostCodePriceArray = unitObj.unitCostCodePrices
        let countUnitCostSummaryArray = new Array();
        let sort = 1;

        for (let i in fyhz_template) {
            sort++;
            let obj = new UnitCostSummary();
            ConvertUtil.setDstBySrc(fyhz_template[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            if ('销项税额' === obj.type) {
                obj.rate = projectTaxCalculation.outputTaxRate
                obj.whetherTax = 1;
            } else if ('附加税费' === obj.type) {
                obj.rate = projectTaxCalculation.additionalTaxRate
                obj.whetherTax = 1;
            } else if ('税金' === obj.type) {

                if (unitIs2022) {
                    obj.rate = projectTaxCalculation.taxRate
                } else {
                    if (ObjectUtils.isEmpty(projectTaxCalculation.simpleRate)) {
                        //12 一般
                        obj.rate = 100
                    } else {
                        obj.rate = projectTaxCalculation.simpleRate
                    }

                }

                obj.whetherTax = 1;
            } else {
                if (ObjectUtils.isEmpty(obj.rate)) {
                    obj.rate = 100;
                }
            }
            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.unitId = unitObj.sequenceNbr;
            obj.price = 0;
            countUnitCostSummaryArray.push(obj);
        }

        await this.jiesuanCostSummary(unitCostCodePriceArray,countUnitCostSummaryArray, unitObj, projectTaxCalculation);



    }

    /**
     * 根据模板名称获取数据
     */
    getTemplateData(args) {
        let template = args.template;
        //根据名称和路径获取模板数据
        let fyhz_template = require("../../../electron/jsonData/" + template + ".json");
        let countUnitCostSummaryArray = new Array();
        let unitObj = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
        let sort = 1;

        for (let i in fyhz_template) {
            sort++;
            let obj = new UnitCostSummary();
            ConvertUtil.setDstBySrc(fyhz_template[i], obj)
            obj.sequenceNbr = Snowflake.nextId();
            if (ObjectUtils.isEmpty(obj.rate)) {
                obj.rate = 100;
            }
            obj.orderNum = sort;
            obj.whetherPrint = 1;
            obj.unitId = unitObj.sequenceNbr;
            obj.price = 0;
            countUnitCostSummaryArray.push(obj);
        }

        return countUnitCostSummaryArray;
    }



    setCostAnalysisData(unitProject, unitCostSummaryArray, unitCostCodePriceArray) {

        //工程总造价含设备及其税金(不含甲供 小写)
        // 取费用汇总中：
        // 工程造价     加上以下金额
        //
        //
        // 取费用代码中：
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费
        // ZGJSBHJ-暂估设备费   减去
        // （JGRGF 甲供人工费 +
        // JGCLF 甲供材料费 +
        // JGJXF 甲供机械费 +
        // JGZCF 甲供主材费 +
        // JGSBF-甲供设备费）


        //工程总造价含设备及其税金(含甲供 小写)
        //取费用汇总中：
        // 工程造价      加上以下金额
        //
        //
        // 取费用代码中：
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费 +
        // ZGJSBHJ-暂估设备费

        //工程总造价含设备及其税金(不含甲供) //gczj +SBF+DJCS_SBF+QTZJCS_SBF+ZGJSBHJ -JGRGF-JGCLF-JGJXF -JGZCF -JGSBF
        let gczjsbsj = 0;
        //工程总造价含设备及其税金(含甲供)   //gczj +SBF+DJCS_SBF+QTZJCS_SBF+JGSBF+ZGJSBHJ
        let gczjsbsjjg = 0;


        //设备费及其税金(不含甲供)
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费
        // ZGJSBHJ-暂估设备费   减去
        // （JGRGF 甲供人工费 +
        // JGCLF 甲供材料费 +
        // JGJXF 甲供机械费 +
        // JGZCF 甲供主材费 +
        // JGSBF-甲供设备费）
        let sbfsj = 0; // SBF+DJCS_SBF+QTZJCS_SBF+ZGJSBHJ - JGRGF -JGCLF -JGJXF - JGZCF -JGSBF

        //设备费及其税金(含甲供)
        // SBF-分部分项设备费  +
        // DJCS_SBF-单价措施设备费 +
        // QTZJCS_SBF-其他总价措施设备费 +
        //
        // ZGJSBHJ-暂估设备费

        let sbfsjjg = 0; //SBF+DJCS_SBF+QTZJCS_SBF+JGSBF+ZGJSBHJ

        //费用代码
        for (let i = 0; i < unitCostCodePriceArray.length; i++) {
            let unitCostCodePrice = unitCostCodePriceArray[i];
            switch (unitCostCodePrice.code) {
                case "SBF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "DJCS_SBF":
                    //工程总造价含设备及其税金
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "QTZJCS_SBF":
                    //工程总造价含设备及其税金
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "ZGJSBHJ":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.add(gczjsbsj, unitCostCodePrice.price);
                    gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostCodePrice.price);
                    sbfsj = NumberUtil.add(sbfsj, unitCostCodePrice.price);
                    sbfsjjg = NumberUtil.add(sbfsjjg, unitCostCodePrice.price);
                    break;
                case "JGRGF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);

                    sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;
                case "JGCLF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;
                case "JGJXF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;
                case "JGZCF":
                    //工程总造价含设备及其税金计算需要
                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);
                    break;

                case "JGSBF":

                    gczjsbsj = NumberUtil.subtract(gczjsbsj, unitCostCodePrice.price);
                    //工程总造价含设备及其税金计算需要
                    sbfsj = NumberUtil.subtract(sbfsj, unitCostCodePrice.price);

                    break;
                case "FBFXHJ":
                    unitProject.fbfxhj = unitCostCodePrice.price;
                    break;
                case "RGF":
                    unitProject.fbfxrgf = unitCostCodePrice.price;
                    break;
                case "CLF":
                    unitProject.fbfxclf = unitCostCodePrice.price;
                    break;
                case "JXF":
                    unitProject.fbfxjxf = unitCostCodePrice.price;
                    break;
                case "ZCF":
                    unitProject.fbfxzcf = unitCostCodePrice.price;
                    break;

                case "FBFX_GLF":
                    unitProject.fbfxglf = unitCostCodePrice.price;
                    break;
                case "FBFX_LR":
                    unitProject.fbfxlr = unitCostCodePrice.price;
                    break;
                case "CSXMHJ":
                    unitProject.csxhj = unitCostCodePrice.price;
                    break;
                case "DJCSF":
                    unitProject.djcsxhj = unitCostCodePrice.price;
                    break;
                case "DJCS_RGF":
                    unitProject.djcsxrgf = unitCostCodePrice.price;
                    break;
                case "DJCS_CLF":
                    unitProject.djcsxclf = unitCostCodePrice.price;
                    break;
                case "DJCS_JXF":
                    unitProject.djcsxjxf = unitCostCodePrice.price;
                    break;
                case "DJCS_ZCF":
                    unitProject.djcsxzcf = unitCostCodePrice.price;
                    break;

                case "DJCS_GLF":
                    unitProject.djcsxglf = unitCostCodePrice.price;
                    break;
                case "DJCS_LR":
                    unitProject.djcsxlr = unitCostCodePrice.price;
                    break;
                case "QTZJCSF":
                    unitProject.zjcsxhj = unitCostCodePrice.price;
                    break;
                case "QTZJCS_RGF":
                    unitProject.zjcsxrgf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_CLF":
                    unitProject.zjcsxclf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_JXF":
                    unitProject.zjcsxjxf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_ZCF":
                    unitProject.zjcsxzcf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_GLF":
                    unitProject.zjcsxglf = unitCostCodePrice.price;
                    break;
                case "QTZJCS_LR":
                    unitProject.zjcsxlr = unitCostCodePrice.price;
                    break;
                case "QTXMHJ":
                    unitProject.qtxmhj = unitCostCodePrice.price;
                    break;
                case "ZLJE":
                    unitProject.qtxmzlje = unitCostCodePrice.price;
                    break;
                case "ZYGCZGJ":
                    unitProject.qtxmzygczgj = unitCostCodePrice.price;
                    break;
                case "ZCBFWF":
                    unitProject.qtxmzcbfwf = unitCostCodePrice.price;
                    break;
                case "JRG":
                    unitProject.qtxmjrg = unitCostCodePrice.price;
                    break;
                case "GFHJ":
                    unitProject.gfee = unitCostCodePrice.price;
                    break;
                case "AQWMSGF":
                    unitProject.safeFee = unitCostCodePrice.price;
                    break;


                case "JSJC":  // 结算价差合计
                    unitProject.jsjc= unitCostCodePrice.price;
                    break;
                case "JS_JGCLF":  // 结算甲供材料费
                    unitProject.jsjgclf= unitCostCodePrice.price;
                    break;
                case "JS_JCRGF":  // 结算人工价差
                    unitProject.jsjcrgf= unitCostCodePrice.price;
                    break;
                case "JS_JCCLF":  // 结算材料价差
                    unitProject.jsjcclf= unitCostCodePrice.price;
                    break;
                case "JS_JCJXF":  // 结算机械价差
                    unitProject.jsjcjxf= unitCostCodePrice.price;
                    break;
                case "JS_JCZGJ":  // 结算暂估价差
                    unitProject.jsjczgj= unitCostCodePrice.price;
                    break;
                case "JS_JCSBF":  // 结算设备费价差
                    unitProject.jsjcsbf= unitCostCodePrice.price;
                    break;
                case "JS_JCJGCLF":  // 结算甲供材料费价差
                    unitProject.jsjcjgclf= unitCostCodePrice.price;
                    break;
                case "JS_JCJGZCF":  // 结算甲供主材费价差
                    unitProject.jsjcjgzcf= unitCostCodePrice.price;
                    break;
                case "JS_JCJGSBF":  // 结算甲供设备费价差
                    unitProject.jsjcjgsbf= unitCostCodePrice.price;
                    break;
                case "JCHJ_JGFHSJ":  // 价差合计(计规费和税金)
                    unitProject.jchjjgfhsj= unitCostCodePrice.price;
                    break;
                case "JCGFHJ":  // 价差规费合计(计规费和税金)
                    unitProject.jcgfhj= unitCostCodePrice.price;
                    break;
                case "JCAQWMSGFHJ":  // 价差安全生产、文明施工费合计（计安、文施工费和税金)
                    unitProject.jcaqwmsgfhj= unitCostCodePrice.price;
                    break;
                case "JCJXSE":  // 价差进项税额
                    unitProject.jcjxse= unitCostCodePrice.price;
                    break;
                case "JCCLFJXSE":  // 价差材料费进项税额
                    unitProject.jcclfjxse= unitCostCodePrice.price;
                    break;
                case "JCJXFJXSE":  // 价差机械费进项税额
                    unitProject.jcjxfjxse= unitCostCodePrice.price;
                    break;
                case "JCSBFJXSE":  // 价差设备费进项税额
                    unitProject.jcsbfjxse= unitCostCodePrice.price;
                    break;
                case "JCAQWMSGF":  // 价差安全生产、文明施工费进项税额
                    unitProject.jcaqwmsgf= unitCostCodePrice.price;
                    break;
                default:
                    break;
            }
        }
        //费用汇总
        let projectTaxCalculation = unitProject.projectTaxCalculation

        //一般计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code) {
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                switch (unitCostSummary.type) {
                    case "其中:进项税额":
                        unitProject.jxse = unitCostSummary.price;
                        break;
                    case "销项税额":
                        unitProject.xxse = unitCostSummary.price;
                        break;
                    case "增值税应纳税额":
                        unitProject.zzsynse = unitCostSummary.price;
                        break;
                    case "附加税费":
                        unitProject.fjse = unitCostSummary.price;
                        break;
                    case "税金":
                        unitProject.sj = unitCostSummary.price;
                        break;
                    case "工程造价":
                        unitProject.gczj = unitCostSummary.price;
                        unitProject.gczj_ys = ObjectUtils.isNotEmpty(unitProject.unitCostSummarys_ys.find(item =>item.type ==='工程造价'))
                            ?unitProject.unitCostSummarys_ys.find(item =>item.type ==='工程造价').price:0
                        gczjsbsj = NumberUtil.add(gczjsbsj, unitCostSummary.price);
                        gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostSummary.price);
                        break;
                    case "调差后工程造价":
                        unitProject.jck = unitCostSummary.price;

                        break;
                    default:
                        break;
                }
            }
        } else {
            for (let i = 0; i < unitCostSummaryArray.length; i++) {
                let unitCostSummary = unitCostSummaryArray[i];
                switch (unitCostSummary.type) {
                    case "税金":
                        unitProject.sj = unitCostSummary.price;
                        break;
                    case "工程造价": //22定额简易计税模板名字叫 工程造价
                        unitProject.gczj = unitCostSummary.price;
                        unitProject.gczj_ys = ObjectUtils.isNotEmpty(unitProject.unitCostSummarys_ys.find(item =>item.type ==='工程造价'))
                            ?unitProject.unitCostSummarys_ys.find(item =>item.type ==='工程造价').price:0
                        gczjsbsj = NumberUtil.add(gczjsbsj, unitCostSummary.price);
                        gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostSummary.price);
                        break;
                    case "含税工程造价":
                        unitProject.gczj = unitCostSummary.price;
                        gczjsbsj = NumberUtil.add(gczjsbsj, unitCostSummary.price);
                        gczjsbsjjg = NumberUtil.add(gczjsbsjjg, unitCostSummary.price);
                        break;
                    case "调差后工程造价":
                        unitProject.jck = unitCostSummary.price;

                        break;
                    default:
                        break;
                }
            }
        }

        //处理造价分析中22定额设备费税金计算
        let unitIs2022 = PricingFileFindUtils.getConstructDeStandard(unitProject.constructId) == ConstantUtil.DE_STANDARD_22;
        if (unitIs2022) {
            if (unitProject.projectTaxCalculation.taxCalculationMethod==TaxCalculationMethodEnum.GENERAL.code
            ) {
                sbfsj = NumberUtil.numberScale2(NumberUtil.multiply(sbfsj, ConstantUtil.SHEBEI_RATE));
                sbfsjjg = NumberUtil.numberScale2(NumberUtil.multiply(sbfsjjg, ConstantUtil.SHEBEI_RATE));
            }else {
                sbfsj = NumberUtil.numberScale2(NumberUtil.multiply(sbfsj, ConstantUtil.SHEBEI_RATE_SIMPLE));
                sbfsjjg = NumberUtil.numberScale2(NumberUtil.multiply(sbfsjjg, ConstantUtil.SHEBEI_RATE_SIMPLE));
            }
        }else {
            if (unitProject.projectTaxCalculation.taxCalculationMethod==TaxCalculationMethodEnum.SIMPLE.code
            ){
                sbfsj = NumberUtil.numberScale2(NumberUtil.multiply(sbfsj, ConstantUtil.SHEBEI_RATE_SIMPLE_12));
                sbfsjjg = NumberUtil.numberScale2(NumberUtil.multiply(sbfsjjg, ConstantUtil.SHEBEI_RATE_SIMPLE_12));
            }
        }
        unitProject.gczjsbsj = NumberUtil.add(unitProject.gczj, sbfsj);//由于产品需求变更 所以原来的工程总造价含设备费及其税金 不含甲供或含甲供 的计算方式作废
        unitProject.gczjsbsjjg = NumberUtil.add(unitProject.gczj, sbfsjjg);
        unitProject.sbfsj = sbfsj;
        unitProject.sbfsjjg = sbfsjjg;
        if (ObjectUtils.isNotEmpty(unitProject.average) && unitProject.average != 0) {
            unitProject.unitcost = NumberUtil.divide(unitProject.gczj, unitProject.average);//计算单方造价
        }

        /**
         * 计算单单项工程
         */
        // PricingFileWriteUtils.countSingleProject(unitProject.constructId,unitProject.spId)
        this.countSingleProjectFromBottom(unitProject.constructId, unitProject.spId);
        /**
         * 计算工作台底部价格汇总计算
         */
        // PricingFileWriteUtils.countConstructProject(unitProject.constructId)
    }


    countSingleProjectFromBottom(constructId,singleId){


        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);

        if(ObjectUtils.isEmpty(singleProject)){
            return
        }

        let unitProjects = singleProject.unitProjects;
        //如果是中间层级的单项 累计下一层级的单项之和
        if(ObjectUtils.isEmpty(unitProjects)&& ObjectUtils.isNotEmpty(singleProject.subSingleProjects)){
            let total = 0;//工程造价(预算)
            let jck = 0;//工程造价(调差后)
            let gfee  = 0;
            let safeFee  = 0;
            let jcaqwmsgfhj  = 0;
            let jcgfhj  = 0;
            let sbfTax = 0;
            let jsjc = 0;
            let qtxmzlje = 0;
            let qtxmzygczgj = 0;
            let jsjcsbf = 0;//价差设备费税金
            for (let i = 0; i < singleProject.subSingleProjects.length; i++) {
                let subSingleProject = singleProject.subSingleProjects[i];
                total = NumberUtil.add(total,subSingleProject.total);
                jck = NumberUtil.add(jck,subSingleProject.jck);
                gfee = NumberUtil.add(gfee,subSingleProject.gfee);
                safeFee = NumberUtil.add(safeFee,subSingleProject.safeFee);
                jcaqwmsgfhj = NumberUtil.add(jcaqwmsgfhj,subSingleProject.jcaqwmsgfhj);
                jcgfhj = NumberUtil.add(jcgfhj,subSingleProject.jcgfhj);
                sbfTax = NumberUtil.add(sbfTax,subSingleProject.sbfTax);
                jsjc = NumberUtil.add(jsjc,subSingleProject.jsjc);
                qtxmzlje = NumberUtil.add(qtxmzlje,subSingleProject.qtxmzlje);
                qtxmzygczgj = NumberUtil.add(qtxmzygczgj,subSingleProject.qtxmzygczgj);
                jsjcsbf = NumberUtil.add(jsjcsbf,subSingleProject.jsjcsbf);
            }
            singleProject.total = NumberUtil.numberScale2(total);
            singleProject.jck = NumberUtil.numberScale2(jck);
            singleProject.gfee = NumberUtil.numberScale2(gfee);
            singleProject.safeFee = NumberUtil.numberScale2(safeFee);
            singleProject.jcaqwmsgfhj = NumberUtil.numberScale2(jcaqwmsgfhj);
            singleProject.jcgfhj = NumberUtil.numberScale2(jcgfhj);
            singleProject.sbfTax = NumberUtil.numberScale2(sbfTax);
            singleProject.jsjc = NumberUtil.numberScale2(jsjc);
            singleProject.qtxmzlje = NumberUtil.numberScale2(qtxmzlje);
            singleProject.qtxmzygczgj = NumberUtil.numberScale2(qtxmzygczgj);
            singleProject.jsjcsbf = NumberUtil.numberScale2(jsjcsbf);

        }else if (ObjectUtils.isNotEmpty(unitProjects)){   //如果是含有单位工程的这一层级单项 进行计算
            let total = 0;
            let jck = 0;
            let gfee  = 0;
            let safeFee  = 0;
            let jcaqwmsgfhj  = 0;
            let jcgfhj  = 0;
            let sbfTax = 0;
            let jsjc = 0;
            let qtxmzlje = 0;
            let qtxmzygczgj = 0;
            let jsjcsbf = 0;
            for (let i = 0; i < unitProjects.length; i++) {
                let unitProject = unitProjects[i];
                total = NumberUtil.add(total,unitProject.gczj);
                jck = NumberUtil.add(jck,unitProject.jck);
                gfee = NumberUtil.add(gfee,unitProject.gfee);
                safeFee = NumberUtil.add(safeFee,unitProject.safeFee);
                jcaqwmsgfhj = NumberUtil.add(safeFee,unitProject.jcaqwmsgfhj);
                jcgfhj = NumberUtil.add(safeFee,unitProject.jcgfhj);
                sbfTax = NumberUtil.add(sbfTax,unitProject.sbfsj);
                jsjc = NumberUtil.add(jsjc,unitProject.jsjc);
                qtxmzlje = NumberUtil.add(qtxmzlje,unitProject.qtxmzlje);
                qtxmzygczgj = NumberUtil.add(qtxmzygczgj,unitProject.qtxmzygczgj);
                jsjcsbf = NumberUtil.add(jsjcsbf,unitProject.jsjcsbf);
            }
            singleProject.total = NumberUtil.numberScale2(total);
            singleProject.jck = NumberUtil.numberScale2(jck);
            singleProject.gfee = NumberUtil.numberScale2(gfee);
            singleProject.safeFee = NumberUtil.numberScale2(safeFee);
            singleProject.jcaqwmsgfhj = NumberUtil.numberScale2(jcaqwmsgfhj);
            singleProject.jcgfhj = NumberUtil.numberScale2(jcgfhj);
            singleProject.sbfTax = NumberUtil.numberScale2(sbfTax);
            singleProject.jsjc = NumberUtil.numberScale2(jsjc);
            singleProject.qtxmzlje = NumberUtil.numberScale2(qtxmzlje);
            singleProject.qtxmzygczgj = NumberUtil.numberScale2(qtxmzygczgj);
            singleProject.jsjcsbf = NumberUtil.numberScale2(jsjcsbf);
        }

        //找到单项的父级单项id
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        let parentSpId = PricingFileWriteUtils.getParentSpId(projectObjById.singleProjects,null,singleId);

        this.countSingleProjectFromBottom(constructId,parentSpId);
    }
}


jieSuanUnitCostSummaryService.toString = () => '[class jieSuanUnitCostSummaryService]';
module.exports = jieSuanUnitCostSummaryService;