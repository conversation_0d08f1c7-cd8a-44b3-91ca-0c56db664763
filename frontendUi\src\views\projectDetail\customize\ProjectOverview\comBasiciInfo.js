/*
 * @Descripttion:项目概况公共代码提取
 * @Author: wangru
 * @Date: 2024-01-30 10:12:01
 * @LastEditors: k<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-12-10 10:48:22
 */

import { ref, toRaw, reactive, onMounted } from 'vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import ysshcsProject from '@/api/shApi';
import { message, Modal } from 'ant-design-vue';
import infoMode from '@/plugins/infoMode.js';
import redo from '@/hooks/redo';

export const comBasicInfoFun = ({ activeKey, pageType }) => {
  //pageType---基本工程信息  type：0    工程特征 type：1
  let operate = {
    ys: csProject,
    yssh: ysshcsProject,
    jieSuan: csProject,
  };
  const projectStore = projectDetailStore();

  let disposeCurrentIndex = ref(null); //复制粘贴操作后的选中行index
  let dafaultParams = reactive({});
  onMounted(() => {
    refreshParams();
  });
  const refreshParams = () => {
    dafaultParams = {
      levelType: projectStore.currentTreeInfo?.levelType,
      code: activeKey.value,
      constructId: projectStore.currentTreeGroupInfo?.constructId,
      singleId:
        projectStore.currentTreeInfo?.levelType === 3
          ? projectStore.currentTreeGroupInfo?.singleId
          : null,
      unitId:
        projectStore.currentTreeInfo?.levelType === 3 ? projectStore.currentTreeInfo?.id : null,
      type: pageType === 'jbgcxx' ? 0 : 1,
    };
  };
  const getPageData = async () => {
    refreshParams();
    if (dafaultParams.levelType !== 1 && !dafaultParams.singleId) return {};
    let resData = await operate[projectStore.type].getBasicInfo(dafaultParams);
    console.log(pageType === 'jbgcxx' ? '基本工程信息' : '工程特征', dafaultParams, resData);
    return resData;
  };
  const delOperateFun = async seq => {
    let apiData = {
      ...dafaultParams,
      sequenceNbr: seq,
    };
    let resData = await csProject.deleteBasicInfo(apiData);
    console.log(pageType === 'jbgcxx' ? '基本工程信息' : '工程特征', '删除操作', resData);
    return resData;
  };
  const saveAndUpdateOperate = async (data,newValue,oldValue,title) => {
    redo.addnoMatchedRedoList({
      sequenceNbr: data.sequenceNbr,
      columnTitle: title,
      newValue,
      oldValue,
    });
    let apiData = {
      ...dafaultParams,
      projectOverviewList: toRaw(data),
      isChange: true,
    };
    let resData = await csProject.saveOrUpdateBasicInfo(apiData);
    console.log(
      pageType === 'jbgcxx' ? '基本工程信息' : '工程特征',
      '---saveAndUpdateOperate',
      resData
    );
    return resData;
  };
  const lockOperate = async lockFlag => {
    let apiData = {
      ...dafaultParams,
      lockFlag: lockFlag ? 0 : 1,
    };
    let resData = await csProject.lockBasicInfo(apiData);
    console.log('项目概况---lockOperate', resData);
    return resData;
  };
  return {
    getPageData,
    delOperateFun,
    saveAndUpdateOperate,
    lockOperate,
    dafaultParams,
  };
};
