const path = require('path');
const YsshssConstant = require("../enum/YsshssConstant");
const fs = require('fs');
const {FileUtils} = require("../utils/FileUtils");
const {dialog} = require("electron");
const {NumberUtil} = require("../../../common/NumberUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {Service} = require("../../../core");
const { Snowflake } = require('../../../electron/utils/Snowflake');
const Log = require('../../../core/log');
const StepItemCostLevelConstant = require("../../../electron/enum/StepItemCostLevelConstant");
const {DateUtils} = require("../../../electron/utils/DateUtils");
const {YiJuFile} = require("../model/YiJuFile");
const { exec } = require('child_process');
const util = require("util");



class YsshYiJuService extends Service{

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 导入依据文件
     * @param args
     * @returns {Promise<void>}
     */
    async importYiJuFile(args){
        const { constructId, singleId, unitId,sequenceNbr } = args;
        //获取清单数据 (只有分部分项有导入依据)
        const fbfxList = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if(ObjectUtils.isEmpty(fbfxList)){
            Log.warn("分部分项数据为空！");
            return;
        }

        //获取清单数据
        let qdData = fbfxList.find(item => item.sequenceNbr === sequenceNbr);

        if(ObjectUtils.isNotEmpty(qdData) && qdData.kind === StepItemCostLevelConstant.qd){
            /*
                选择依据文件
             */
            const selectedFilePath = await this.selectYiJuFile();
            if(FileUtils.checkFileExistence(selectedFilePath)){
                //获取文件详情文件名：fileName，文件路径（不含文件名）：filePathDir，文件大小(字节)：fileSize, 文件后缀：fileExtension
                const fileInfo = await FileUtils.getFileInfo(selectedFilePath);
                //为文件改名
                const fileId = Snowflake.nextId();
                //选择文件存在的话
                const createYiJuFilePath = await this.getYiJuFilePath(constructId, fileId.concat(fileInfo.fileExtension));
                //复制文件到新路径
                await FileUtils.copyFile(selectedFilePath, createYiJuFilePath);
                //内存数据对象为空则创建
                if(ObjectUtils.isEmpty(qdData.yiJuFileList)){
                    qdData.yiJuFileList = [];
                }
                //依据文件数据
                let yiJuFileList = qdData.yiJuFileList;
                if(ObjectUtils.isNotEmpty(qdData.yiJuFileList)){
                    //删除旧文件，以防影响打包
                    let oldyiJuFile = qdData.yiJuFileList[0];
                    if(oldyiJuFile.sequenceNbr && oldyiJuFile.filePath){
                        try{
                            if(FileUtils.checkFileExistence(oldyiJuFile.filePath)){
                                fs.unlinkSync(oldyiJuFile.filePath);
                                Log.info("旧依据文件删除成功！%s", oldyiJuFile.filePath);
                            }
                        }catch (e) {
                            Log.error("旧依据文件删除成功", e);
                        }
                    }

                }
                //依据文件对象  目前一条清单只存一个文件
                let yiJuFile = ObjectUtils.isNotEmpty(qdData.yiJuFileList) ? qdData.yiJuFileList[0] : new YiJuFile();
                //数据ID
                yiJuFile.sequenceNbr = fileId;
                //绑定清单ID
                yiJuFile.qdId = qdData.sequenceNbr;
                //存储路径
                yiJuFile.filePath = createYiJuFilePath;
                //原文件名称（带后缀）
                yiJuFile.name = fileInfo.fileName;//文件名称
                //文件后缀（.txt）
                yiJuFile.fileType = fileInfo.fileExtension;//后缀
                //文件大小
                yiJuFile.fileSize = fileInfo.fileSize;//文件大小
                //创建时间
                yiJuFile.createTime = DateUtils.format(new Date());
                //装入缓存数据中
                if (ObjectUtils.isEmpty(qdData.yiJuFileList)){
                    //为空则添加
                    qdData.yiJuFileList.push(yiJuFile);
                }
                Log.info("依据文件导入成功！%s", JSON.stringify(qdData.yiJuFileList));
                return;
            }else{
                Log.warn("依据文件不存在！" + JSON.stringify(args));
            }
        }

    }


    /**
     * 删除依据文件
     * @param args
     * @returns {Promise<void>}
     */
    async removeYiJuFile(args) {
        //参数
        const {constructId, singleId, unitId, sequenceNbr, yiJuFileId} = args;
        //获取清单数据
        //获取清单数据
        const fbfxList = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        if(!fbfxList || ObjectUtils.isEmpty(fbfxList)){
            Log.warn("分部分项数据为空！");
            return;
        }
        //获取清单数据
        let qdData = fbfxList.find(item => item.sequenceNbr === sequenceNbr);
        if(ObjectUtils.isEmpty(qdData) || ObjectUtils.isEmpty(qdData.yiJuFileList)){
            Log.warn("清单数据为空！"+JSON.stringify(args));
            return;
        }
        let yiJuFileList = qdData.yiJuFileList;
        //获取依据文件信息
        const yiJuFileInfo = yiJuFileList.find(f => f.sequenceNbr === yiJuFileId);
        if(ObjectUtils.isNotEmpty(yiJuFileInfo)){
            try {
                //删除本地文件
                fs.unlinkSync(yiJuFileInfo.filePath);
                Log.info(`文件 ${yiJuFileInfo.filePath} 已被删除`);
            } catch (err) {
                Log.error(`删除文件失败: ${err}`);
            }
        }else{
            Log.warn("未找到依据文件！" + JSON.stringify(qdData.yiJuFileList));

        }
        //获取依据文件数组索引
        let yiJuDataIndex = yiJuFileList.findIndex(f => f.sequenceNbr === yiJuFileId);
        //清除数据
        if(yiJuDataIndex > -1){
            yiJuFileList.splice(yiJuDataIndex,1);
            Log.info(`依据数据 ${yiJuFileId} 已被删除!`);
        }

    }

    /**
     * 打开依据文件
     * @param args
     * @returns {Promise<Buffer>}
     */
    async openYiJuFile(args) {
        const {filePath} = args;
        //读取文件流，返给前端
        // const result = fs.readFileSync(filePath);
        //本地应用打开文件
        return await this.openFile(filePath);
    }


    /**
     * 依据文件存储目录
     * @param constructId 项目工程ID
     * @param fileName 文件名称(带后缀)
     */
    async getYiJuFilePath(constructId, fileName) {
        //保存路径：存储路径  {content:userHistoryData.DEF_SAVE_PATH}
        const filesStoragePath = await this.service.commonService.getSetStoragePath(null);
        /*
            此处设计：在导出审核数据时，将${filesStoragePath}\\shenHe\\${sdConstructId}文件夹下的文件打包到zip文件中
            即在ysh打包文件中添加ysYiJuFiles文件夹；
            因此，在打开ysh文件时，需将打包文件中的ysYiJuFiles目录下的文件全部复制到本地对应目录
         */
        return filesStoragePath.concat(path.sep).concat('shenHe')
            .concat(path.sep).concat(constructId)
            .concat(path.sep).concat('ysYiJuFiles')
            .concat(path.sep).concat(fileName);
    }


    /**
     * 选择依据文件，目前支持单个文件选择
     * @returns {undefined}
     */
    async selectYiJuFile() {
        //默认存储路径
        const filesStoragePath = await this.service.commonService.getSetStoragePath(null);
        const options = {
            properties: ['openFile'],
            //defaultPath: defaultStoragePath.toString(), // 默认保存路径
            defaultPath: filesStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: ['doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'pdf']} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return;
        }
        //获取选中的路径
        return result[0];
    }


    async openFileOnWindows(filePath) {
        try{
            // 将exec转换为返回Promise的版本
            const execPromise = util.promisify(exec);
            const {stdout, stderr} = await execPromise(`start "" "${filePath}"`);
            console.log('stdout:', stdout);
            if (stderr) {
                console.error('stderr:', stderr);
                return false;
            }
        }catch (e) {
            console.error('exec error:', e);
            return false;
        }
        return true;
    }

    async openFileOnMacOrLinux(filePath) {
        try{
            // 将exec转换为返回Promise的版本
            const execPromise = util.promisify(exec);
            const {stdout, stderr} = await execPromise(`open "${filePath}"`);
            console.log('stdout:', stdout);
            if (stderr) {
                console.error('stderr:', stderr);
                return false;
            }
        }catch (e) {
            console.error('exec error:', e);
            return false;
        }
        return true;

    }

    /**
     * 本地应用打开文件
     * @param filePath
     */
    async openFile(filePath){
        if(!filePath){
            return;
        }
        console.log("打开文件，文件路径为：%s", filePath);
        // 根据操作系统调用不同的函数
        if (process.platform === 'win32') {
            return this.openFileOnWindows(filePath);
        } else {
            return this.openFileOnMacOrLinux(filePath);
        }
    }

}


YsshYiJuService.toString = () => '[class YsshYiJuService]';
module.exports = YsshYiJuService;