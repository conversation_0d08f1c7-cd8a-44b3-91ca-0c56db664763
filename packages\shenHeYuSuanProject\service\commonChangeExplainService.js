'use strict';

const { ResponseData } = require('../../../common/ResponseData');
const { Service } = require('../../../core');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const YsshssConstant = require('../enum/YsshssConstant');
const ChangeExplainEnum = require('../enum/ChangeExplainEnum');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');

/**
 * 增减说明统一修改入口Service
 * @class
 */
class CommonChangeExplainService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  async updateChangeExplain(args) {
    let { constructId, singleId, unitId, sequenceNbr, changeExplain, changeType, materialCode } = args;
    if (ObjectUtil.isEmpty(changeExplain)) {
      // 如果增减说明为空  那么说明需要把增减说明改为空的   空字符串容易被判断为空  不利于判断  所以此处改为空格字符串
      changeExplain = ' ';
    }
    let targetObj = [];
    switch (changeType) {
      case ChangeExplainEnum.FBFX.code:
        targetObj = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.CSXM.code:
        targetObj = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.RCJ.code:
        targetObj = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.QTXM.code:
        targetObj = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.QTXM_ZLJ.code:
        targetObj = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.QTXM_ZGJ.code:
        targetObj = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.QTXM_ZCBFWF.code:
        targetObj = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
        break;
      case ChangeExplainEnum.QTXM_JRG.code:
        targetObj = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
        break;
      default:
        throw new Error('参数错误');
    }
    if (ObjectUtil.isEmpty(targetObj)) {
      throw new Error('数据不存在');
    }
    if (ChangeExplainEnum.RCJ.code === changeType) {
      // 人材机汇总页面的数据是实时统计的  并没有在单位级别存储  所以此处单独特殊处理
      if (ObjectUtil.isEmpty(targetObj)) {
        throw new Error('数据不存在');
      }
      if (ObjectUtil.isEmpty(targetObj.changeExplainRecord)) {
        targetObj.changeExplainRecord = {};
        const rcjMaterialCodeChangeExplain = {
          changeExplain: '',
          materialCode: ''
        };
        rcjMaterialCodeChangeExplain.changeExplain = changeExplain;
        rcjMaterialCodeChangeExplain.materialCode = materialCode;
        targetObj.changeExplainRecord[materialCode] = rcjMaterialCodeChangeExplain;
      } else {
        targetObj.changeExplainRecord[materialCode].changeExplain = changeExplain;
      }
    } else {
      const targetData = targetObj.find(targetObj => sequenceNbr === targetObj.sequenceNbr);
      if (ObjectUtil.isEmpty(targetData)) {
        throw new Error('数据不存在');
      }
      targetData[YsshssConstant.changeExplain] = changeExplain;
    }
    return ResponseData.success(true);
  }


}


CommonChangeExplainService.toString = () => '[class CommonChangeExplainService]';
module.exports = CommonChangeExplainService;

