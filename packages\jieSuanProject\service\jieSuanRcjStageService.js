'use strict';




const {Service} = require("../../../core");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const BranchProjectLevelConstant = require("../../../electron/enum/BranchProjectLevelConstant");
const {RcjStageSetVo} = require("../model/RcjStageSetVo");
const {StageQuantitiesListVo} = require("../model/StageQuantitiesListVo");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const JieSuanMethodEnum = require("../enum/JieSuanMethodEnum");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const XLSX = require('xlsx');
const fs = require('fs');
const JieSuanPriceAdjustmentMethodEnum = require("../enum/JieSuanPriceAdjustmentMethodEnum");
const ConstructProjectRcjService = require("../../../electron/service/rcj/constructProjectRcjService");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {JieSuanConstructProjectRcj} = require("../model/JieSuanConstructProjectRcj");
const {ConstructProjectRcj} = require("../../../electron/model/ConstructProjectRcj");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {JieSuanDifferencePrice} = require("../model/JieSuanDifferencePrice");
const {RcjDifferenceInfo} = require("../model/RcjDifferenceInfo");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const {JieSuanRcjStageUtils} = require("../utils/JieSuanRcjStageUtils");
/**
 * 示例服务
 * @class
 */
class JieSuanRcjStageService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    jieSuanRcjProcess = this.service.jieSuanProject.jieSuanRcjProcess;
    commonService = this.service.commonService;
    rcjProcess = this.service.rcjProcess;
    jieSuanItemBillProjectService = this.service.jieSuanProject.jieSuanItemBillProjectService;

    /**
     * 清单级别的分期修改
     * @param args
     */
    qdRcjStageUpdate(args) {
        let {constructId, singleId, unitId, qdId, stageQuantitiesList} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //分部分项清单
        let itemBillqd = unit.itemBillProjects.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr == qdId);
        //措施项目清单
        let measureProjectqd = unit.measureProjectTables.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr == qdId);

        if (ObjectUtil.isNotEmpty(itemBillqd)) {
            this.qdStageHandler(itemBillqd, stageQuantitiesList);
            let allData = PricingFileFindUtils.getFbFx(constructId, singleId, unitId);
            //计算量差
            this.jieSuanItemBillProjectService.mathQuantityDifference(constructId, singleId, unitId, itemBillqd);
            //计算定额的结算工程量
            this.jieSuanItemBillProjectService.updateDeQuantity(constructId, singleId, unitId, itemBillqd.sequenceNbr, "fbfx");

        }
        if (ObjectUtil.isNotEmpty(measureProjectqd)) {
            this.qdStageHandler(measureProjectqd, stageQuantitiesList);
            let allData = PricingFileFindUtils.getCSXM(constructId, singleId, unitId);
            //计算量差
            this.jieSuanItemBillProjectService.mathQuantityDifference(constructId, singleId, unitId, measureProjectqd);
            this.jieSuanItemBillProjectService.updateDeQuantity(constructId, singleId, unitId, measureProjectqd.sequenceNbr, "csxm");

        }
        return ResponseData.success(true);
    }

    /**
     * 获取单位的分期数
     * @param args
     * @return {number}
     */
    getUnitStage(args) {
        let {constructId, singleId, unitId} = args;
        let {rcjStageSet} = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //单位分期设置
        let stage = 0;
        if (ObjectUtil.isNotEmpty(rcjStageSet)) {
            stage = rcjStageSet.periods
        }
        return stage;


    }

    /**
     * 单个清单分期处理
     * @param qd
     * @param stageQuantitiesList
     */
    qdStageHandler(qd, stageQuantitiesList) {

        //判断是否为安文费清单
        if (!ObjectUtils.isEmpty(qd.zjcsClassCode) && qd.zjcsClassCode == 0) {
            return;
        }
        //分期方式 1：按分期比例输入  2：按分期工程量输入
        let stageType = qd.stageType;
        //2：按分期工程量输入
        if (JieSuanConstantUtil.STAGE_QUANTITY == stageType) {
            let sum = stageQuantitiesList.map(function (obj) {
                obj.stageQuantity = Number(obj.stageQuantity);
                return obj.stageQuantity;
            }).reduce(function (acc, curr) {
                return acc + curr;
            }, 0);
            qd.quantity = NumberUtil.numberScale(sum,6);
            qd.quantityExpression = NumberUtil.numberScale(sum,6);

        }
        if (JieSuanConstantUtil.STAGE_RATIO == stageType) {
            //按分期比例
            stageQuantitiesList.forEach(k => {
                k.stageRatio = NumberUtil.numberScale(Number(k.stageRatio),2);
                k.stageQuantity = NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide100(k.stageRatio), qd.quantity));
            });

        }
        qd.stageQuantitiesList = stageQuantitiesList;
    }


    /**
     * 分期比例批量应用
     */
    stageRatioBatchUse(args) {
        //useType:   1:应用到当前分部   2：应用到分部分项下所有清单
        let {constructId, singleId, unitId, useType, qdId} = args;

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //分部分项清单
        let itemBillqd = unit.itemBillProjects.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr == qdId);
        //措施项目清单
        let measureProjectqd = unit.measureProjectTables.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr == qdId);
        if (ObjectUtil.isNotEmpty(itemBillqd)) {
            this.qdRcjStageRatioDataHandler(itemBillqd, unit.itemBillProjects, useType, unit);
        }
        if (ObjectUtil.isNotEmpty(measureProjectqd)) {
            this.qdRcjStageRatioDataHandler(measureProjectqd, unit.measureProjectTables, useType, unit);
        }
        return ResponseData.success(true);
    }

    /**
     * 人材机分期调整
     * @param args
     */
    rcjStageSet(args) {
        let {constructId, singleId, unitId, isStage, periods, stageType} = args;
        //获取单位
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //是否分期
        let rcjStageSetVo = new RcjStageSetVo();
        if (isStage) {
            rcjStageSetVo.isStage = isStage;
            rcjStageSetVo.periods = periods;
            rcjStageSetVo.stageType = stageType;

        } else {
            rcjStageSetVo = null;
            //设置人材机的期数设置初始化
            unit.rcjDifference.forEach(k => {
                k.frequencyList = [];
                k.rcjPeriodsSet = 1;
            });
        }
        //单位分期设置
        unit.rcjStageSet = rcjStageSetVo;
        //设置后分期清单数据默认值初始化
        this.rcjStageInit(constructId, singleId, unitId);

        //人材机调差设置
        this.rcjDifferenceInit(constructId, singleId, unitId, null, null, null);
        return ResponseData.success(true);

    }


    /*
    *
    * 人材机单双期设置
    *
    */
    rcjDifferenceInit(constructId, singleId, unitId, values, type, kind) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //获取单位的分期设置
        let rcjStageSet = unit.rcjStageSet;
        if (ObjectUtil.isEmpty(rcjStageSet)) {
            //不分期
            unit.rcjDifference.forEach(k => {
                k.frequencyList = [];
                if (ObjectUtil.isNotEmpty(type)) {
                    k.rcjPeriodsSet = type;
                }
            });
            //人材机单双期数据处理
            this._rcjStagePriceInit(unit, kind);
            return;
        }

        //分期
        //参数处理
        if (ObjectUtil.isEmpty(values)) {
            let array = new Array();
            //获取分期数
            let periods = rcjStageSet.periods;
            for (let i = 0; i < periods; i++) {
                let temp = {};
                temp.num = i + 1;
                temp.scope = "";
                array.push(temp);
            }
            values = array;
        }
        //分期次数
        let array = new Array();
        for (let i = 0; i < values.length; i++) {
            let temp = {};
            temp.num = i + 1;
            temp.scope = values[i];
            array.push(temp);
        }
        unit.rcjDifference.forEach(k => {
            if (ObjectUtil.isEmpty(kind)) {
                k.frequencyList = array;
                if (ObjectUtil.isNotEmpty(type)) {
                    k.rcjPeriodsSet = type;
                }
            } else {
                if (k.kind == kind) {
                    k.frequencyList = array;
                    if (ObjectUtil.isNotEmpty(type)) {
                        k.rcjPeriodsSet = type;
                    }
                }
            }
        });
        //人材机单双期数据处理
        this._rcjStagePriceInit(unit, kind);
    }

    /**
     * 人材机各期数据处理
     * @private
     */
    _rcjStagePriceInit(unit, kind) {
        //人材机数据
        let rcjList = unit.constructProjectRcjs;
        let rcjDetailList = unit.rcjDetailList;
        //获取单位的分期设置
        let rcjStageSet = unit.rcjStageSet;
        if (ObjectUtil.isEmpty(rcjStageSet)) {
            rcjList.forEach(k => {
                //调整法
                let adjustmentMethodList = new Array();
                //单价信息集合
                //k.jieSuanRcjDifferenceTypeList.forEach(a => {
                    //人材机四种调整法默认值设置

                for (let i = 0; i < 4; i++) {
                    let rcjDifferenceInfo = new RcjDifferenceInfo();
                    rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
                    rcjDifferenceInfo.jieSuanBasePrice = k.marketPrice;//结算基期价默认值
                    let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                    jieSuanDifferencePrice.jieSuanPrice = k.marketPrice;//结算单价默认值
                    jieSuanDifferencePrice.jieSuanPriceSource = k.marketSourcePrice;//结算单价来源
                    rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                    adjustmentMethodList.push(rcjDifferenceInfo);
                }

                //})
                k.jieSuanRcjDifferenceTypeList = adjustmentMethodList;

                //第n期除税系数
                k.jieSuanStagetaxRemovalList = [];
                //结算第n期单位价差
                k.jieSuanUnitPriceDifferencList = [];
                //变值权重B分期数
                k.jieSuanStageValuetWeightB = [];
            })
            rcjDetailList.forEach(k => {

                //调整法
                let adjustmentMethodList = new Array();
                //单价信息集合
                //k.jieSuanRcjDifferenceTypeList.forEach(a => {
                //人材机四种调整法默认值设置

                for (let i = 0; i < 4; i++) {
                    let rcjDifferenceInfo = new RcjDifferenceInfo();
                    rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
                    rcjDifferenceInfo.jieSuanBasePrice = k.marketPrice;//结算基期价默认值
                    let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                    jieSuanDifferencePrice.jieSuanPrice = k.marketPrice;//结算单价默认值
                    jieSuanDifferencePrice.jieSuanPriceSource = k.marketSourcePrice;//结算单价来源
                    rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                    adjustmentMethodList.push(rcjDifferenceInfo);
                }

                //})
                k.jieSuanRcjDifferenceTypeList = adjustmentMethodList;
                //第n期除税系数
                k.jieSuanStagetaxRemovalList = [];
                //结算第n期单位价差
                k.jieSuanUnitPriceDifferencList = [];
                //变值权重B分期数
                k.jieSuanStageValuetWeightB = [];
            })
            return;
        }

        if (ObjectUtil.isNotEmpty(kind)) {
            //人材机类型
            if (kind == JieSuanRcjDifferenceEnum.CAILIAO.code) {
                rcjList = rcjList.filter(k => [2, 4, 5, 6, 8, 9, 10].includes(k.kind));
            }
            //暂估价
            if (kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code) {
                rcjList = rcjList.filter(k => k.ifProvisionalEstimate == 1);
            }
            rcjList = rcjList.filter(k => k.kind == kind);
        }

        //调差方式
        let rcjDifference = unit.rcjDifference;
        //取第一个，因为初始化的期数都是一样的
        let frequencyList = rcjDifference[0].frequencyList;
        if (ObjectUtil.isNotEmpty(kind)) {
            let find = rcjDifference.find(k => k.kind == kind);
            if (!ObjectUtil.isEmpty(find)) {
                frequencyList = find.frequencyList;
            }
        }

        rcjList.forEach(i => {
            //获取人材机的调整法集合
            let jieSuanDifferencePriceList = new Array();
            frequencyList.forEach(k => {
                let rcjDifferenceInfo = new RcjDifferenceInfo();
                rcjDifferenceInfo.jieSuanBasePrice = i.marketPrice;//结算基期价默认值

                let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                jieSuanDifferencePrice.jieSuanPrice = i.marketPrice;//结算单价默认值
                jieSuanDifferencePrice.jieSuanPriceSource = i.marketSourcePrice;//结算单价来源
                jieSuanDifferencePriceList.push(jieSuanDifferencePrice);//单价信息集合
                if (ObjectUtils.isEmpty(i.jieSuanStagetaxRemovalList)){
                    i.jieSuanStagetaxRemovalList = [];
                }
                i.jieSuanStagetaxRemovalList.push(i.taxRemoval);
                //结算第n期单位价差
                if (ObjectUtils.isEmpty(i.jieSuanUnitPriceDifferencList)){
                    i.jieSuanUnitPriceDifferencList = [];
                }
                i.jieSuanUnitPriceDifferencList.push(0);

            });
            if (ObjectUtils.isEmpty(i.jieSuanRcjDifferenceTypeList)){
                let adjustmentMethodList = new Array();
                for (let a = 0; a < 4; a++) {
                    let rcjDifferenceInfo = new RcjDifferenceInfo();
                    rcjDifferenceInfo.rcjDifferenceType = a+1;//人材机调整类型
                    rcjDifferenceInfo.jieSuanBasePrice = i.marketPrice;//结算基期价默认值
                    let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                    jieSuanDifferencePrice.jieSuanPrice = i.marketPrice;//结算单价默认值
                    jieSuanDifferencePrice.jieSuanPriceSource = i.marketSourcePrice;//结算单价来源
                    rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                    adjustmentMethodList.push(rcjDifferenceInfo);
                }
                i.jieSuanRcjDifferenceTypeList = adjustmentMethodList;
            }

            i.jieSuanRcjDifferenceTypeList.forEach(a => {
                a.jieSuanDifferencePriceList = ObjectUtil.cloneDeep(jieSuanDifferencePriceList);
            })
        });

        rcjDetailList.forEach(i => {
            //获取人材机的调整法集合
            let jieSuanDifferencePriceList = new Array();
            frequencyList.forEach(k => {
                let rcjDifferenceInfo = new RcjDifferenceInfo();
                rcjDifferenceInfo.jieSuanBasePrice = i.marketPrice;//结算基期价默认值

                let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                jieSuanDifferencePrice.jieSuanPrice = i.marketPrice;//结算单价默认值
                jieSuanDifferencePrice.jieSuanPriceSource = i.marketSourcePrice;//结算单价来源
                jieSuanDifferencePriceList.push(jieSuanDifferencePrice);//单价信息集合
                if (ObjectUtils.isEmpty(i.jieSuanStagetaxRemovalList)){
                    i.jieSuanStagetaxRemovalList = [];
                }
                i.jieSuanStagetaxRemovalList.push(i.taxRemoval);
                //结算第n期单位价差
                if (ObjectUtils.isEmpty(i.jieSuanUnitPriceDifferencList)){
                    i.jieSuanUnitPriceDifferencList = [];
                }
                i.jieSuanUnitPriceDifferencList.push(0);

            });
            if (ObjectUtils.isEmpty(i.jieSuanRcjDifferenceTypeList)){
                let adjustmentMethodList = new Array();
                for (let a = 0; a < 4; a++) {
                    let rcjDifferenceInfo = new RcjDifferenceInfo();
                    rcjDifferenceInfo.rcjDifferenceType = a+1;//人材机调整类型
                    rcjDifferenceInfo.jieSuanBasePrice = i.marketPrice;//结算基期价默认值
                    let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                    jieSuanDifferencePrice.jieSuanPrice = i.marketPrice;//结算单价默认值
                    jieSuanDifferencePrice.jieSuanPriceSource = i.marketSourcePrice;//结算单价来源
                    rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                    adjustmentMethodList.push(rcjDifferenceInfo);
                }
                i.jieSuanRcjDifferenceTypeList = adjustmentMethodList;
            }

            if (ObjectUtils.isNotEmpty(i.jieSuanRcjDifferenceTypeList)){
            i.jieSuanRcjDifferenceTypeList.forEach(a => {
                a.jieSuanDifferencePriceList = ObjectUtil.cloneDeep(jieSuanDifferencePriceList);
            })
            }
        });
    }


    /**
     * 人材机调差设置
     * @param rcjStageSetVo
     */
    rcjDifferenceSet(unit, rcjStageSetVo) {
        let rcjDifferenceSetList = [JieSuanRcjDifferenceEnum.RENGONG.code, JieSuanRcjDifferenceEnum.CAILIAO.code
            , JieSuanRcjDifferenceEnum.JIXIE.code];
        for (const item of rcjDifferenceSetList) {
            let rcjDifference = unit.rcjDifference.find(k => k.kind == item);
            //总期数
            for (let i = 0; i < rcjStageSetVo.periods; i++) {
                let temp = {num: i + 1, scope: ""};
                rcjDifference.frequencyList.push(temp);
            }
        }
    }


    /**
     * 获取单位级别的人材机分期设置
     * @param args
     */
    getRcjStageSet(args) {
        let {constructId, singleId, unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        return ResponseData.success(unit.rcjStageSet);
    }

    /**
     * 清单级别人材机分期方式切换
     * @param args
     */
    rcjStageSwitch(args) {
        let {constructId, singleId, unitId, qdId, stageType} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //分部分项清单
        let itemBillqd = unit.itemBillProjects.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr == qdId);
        //措施项目清单
        let measureProjectqd = unit.measureProjectTables.find(k => k.kind === BranchProjectLevelConstant.qd && k.sequenceNbr == qdId);
        if (ObjectUtil.isNotEmpty(itemBillqd)) {
            this.qdRcjStageSwitchDataHandler(itemBillqd, stageType);
        }
        if (ObjectUtil.isNotEmpty(measureProjectqd)) {
            this.qdRcjStageSwitchDataHandler(measureProjectqd, stageType);
        }
        return ResponseData.success(true);

    }

    /**
     * 清单分期比例数据处理
     */
    qdRcjStageRatioDataHandler(baseQd, qdList, useType, unit) {

        //useType:   1:应用到当前分部   2：应用到分部分项下所有清单

        let tempQdList = [];
        if (useType == 1) {
            tempQdList = qdList.filter(k => k.parentId == baseQd.parentId);
        }
        if (useType == 2) {
            tempQdList = unit.itemBillProjects.filter(k => k.kind === BranchProjectLevelConstant.qd);
        }

        let stageQuantitiesList = baseQd.stageQuantitiesList;
        for (let item of tempQdList) {
            if (item.sequenceNbr == baseQd.sequenceNbr || item.stageType == JieSuanConstantUtil.STAGE_QUANTITY) {
                continue;
            }
            item.stageQuantitiesList = ObjectUtil.cloneDeep(stageQuantitiesList);

            //按分期比例
            item.stageQuantitiesList.forEach(k => {
                k.remark = null;
                k.stageQuantity = NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide100(k.stageRatio), item.quantity));
            });
            let index = null;
            for (let i = item.stageQuantitiesList.length - 1; i >= 0; i--) {
                if (item.stageQuantitiesList[i].stageQuantity !== 0) {
                    index = i;
                    break;
                }
            }
            if (ObjectUtil.isNotEmpty(index)) {
                let sum = 0;
                item.stageQuantitiesList.slice(0, index).forEach(function (obj) {
                    sum +=Number(obj.stageQuantity);
                });
                //比例不足时做100补齐处理
                item.stageQuantitiesList[index].stageQuantity = NumberUtil.subtract(item.quantity, sum);
            }
        }
    }


    /**
     * 清单分期工程量数据处理
     */
    qdRcjStageSwitchDataHandler(qd, stageType) {
        if (ObjectUtil.isEmpty(qd.quantity)) {
            return;
        }
        //分期类型
        qd.stageType = stageType;
        //获取分期工程量明细
        let stageQuantitiesList = qd.stageQuantitiesList;
        if (stageType == JieSuanConstantUtil.STAGE_QUANTITY) {
            //按工程量
            stageQuantitiesList.forEach(k => {
                k.stageRatio = 0;
            });
        }
        if (stageType == JieSuanConstantUtil.STAGE_RATIO) {
            //按分期比例
            stageQuantitiesList.forEach(k => {
                k.stageRatio = k.stageQuantity == 0 ? 0 : Number(NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(k.stageQuantity, qd.quantity), 100)));
            });
            let index = null;
            for (let i = stageQuantitiesList.length - 1; i >= 0; i--) {
                if (stageQuantitiesList[i].stageQuantity !== 0) {
                    index = i;
                    break;
                }
            }
            if (ObjectUtil.isNotEmpty(index)) {
                let sum = 0;
                stageQuantitiesList.slice(0, index).forEach(function (obj) {
                    sum += obj.stageRatio;
                });
                //比例不足时做100补齐处理
                stageQuantitiesList[index].stageRatio = NumberUtil.subtract(100, sum);
            }
        }
    }


    /**
     * 分期工程量设置后的初始化数据
     */
    rcjStageInit(constructId, singleId, unitId) {
        //获取指定的单位信息
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //分部分项清单
        let itemBillqd = unit.itemBillProjects.filter(k => k.kind === BranchProjectLevelConstant.qd);
        //措施项目清单
        let measureProjectqd = unit.measureProjectTables.filter(k => k.kind === BranchProjectLevelConstant.qd);

        //获取单位级别的分期设置
        let rcjStageSet = unit.rcjStageSet;
        if (ObjectUtil.isEmpty(rcjStageSet)) {
            //不分期
            itemBillqd.forEach(k => {
                k.stageType = null;
                k.stageQuantitiesList = null;
            });
            measureProjectqd.forEach(k => {
                k.stageType = null;
                k.stageQuantitiesList = null;
            });
            return;
        }

        //分期
        //总期数
        let periods = rcjStageSet.periods;
        let array = new Array();
        for (let i = 0; i < periods; i++) {
            let stageQuantitiesListVo = new StageQuantitiesListVo();
            stageQuantitiesListVo.stage = i + 1;
            stageQuantitiesListVo.stageRatio = 0;
            stageQuantitiesListVo.stageQuantity = 0;
            stageQuantitiesListVo.remark = "";
            array.push(stageQuantitiesListVo);
        }

        itemBillqd.forEach(k => {
            k.stageQuantitiesList = this.stageQuantitiesListInit(rcjStageSet, array, k);
            k.stageType = rcjStageSet.stageType;
        });
        measureProjectqd.forEach(k => {
            //按总价包干
            // if (k.settlementMethodValue != JieSuanMethodEnum.METHOD2.code) {
            //     k.stageQuantitiesList = this.stageQuantitiesListInit(rcjStageSet, array, k);
            //     k.stageType = rcjStageSet.stageType;
            // }
            //按总价包干
            k.stageQuantitiesList = this.stageQuantitiesListInit(rcjStageSet, array, k);
            k.stageType = rcjStageSet.stageType;
        });
    }

    /**
     *分期工程量明细数据初始化
     * @param rcjStageSet
     * @param stageQuantitiesList
     */
    stageQuantitiesListInit(rcjStageSet, stageQuantitiesList, qd) {
        let tempStageQuantitiesList = ObjectUtil.cloneDeep(stageQuantitiesList);
        //默认按照按工程量
        tempStageQuantitiesList[0].stageQuantity = qd.quantity;

        //按分期比例
        if (rcjStageSet.stageType == JieSuanConstantUtil.STAGE_RATIO) {
            tempStageQuantitiesList[0].stageRatio = 100;
        }
        return tempStageQuantitiesList;

    }


    /**
     * 批量调整结算除税系数
     * @param constructId
     * @param singleId
     * @param unitId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list  所选调差数据集合
     * @param value  批量调整除税系数
     */
    async batchUpdateTaxRemoval(args) {
        //获取参数
        let {constructId, singleId, unitId, tcType, list, value} = args;
        //获取选中的调差数据
        // 1. 单独调整：用户可进入调差明细表内对各调差人材机数据行结算除税系数进行编辑，类型为"人工" 的数据行其值默认为空且不可编辑
        // 2. 批量调整：用户可对当前调差标题分类&调差分期明细表下（仅分期时）对页面下批量选中（键鼠操作）的数据行进行批量调整，批量调整数据对类型为"人工"、机械人工（JXPB-005）、机械人工费（JXPB-006）的调差数据行不起作用，这些数据行结算除税系数%为"0" 且不可更改
        //如果集合数据为空表示修改的是当前单位改分类下的所有数据
        let updateData;
        if (ObjectUtils.isEmpty(list)) {
            //查询分类数据
            let obj = {
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                kind: tcType
            };
            //单位下的人材机汇总查询
            updateData = this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(obj);
        } else {
            updateData = list;
        }
        //过滤掉   机械人工（JXPB-005）、机械人工费（JXPB-006）
        updateData = updateData.filter(rcj => (rcj.materialCode != "JXPB-005" && rcj.materialCode != "JXPB-006" && rcj.kind!=1));
        //封装修改参数
        let constructProjectRcj = {
            jieSuanTaxRemoval: value
        };
        //更新人材机数据
         await this.batchchangeRcj(constructId, singleId, unitId, 2, updateData, constructProjectRcj)
    }


    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type  1 工程项目  2 单位工程
     * @param rclList  要修改的人材机集合
     * @param constructProjectRcj   要改的字段名和值
     * @returns {Promise<void>}
     */
    async batchchangeRcj(constructId, singleId, unitId, type, rclList, constructProjectRcj) {
        let rcjObj = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            type: type
        };
        if(ObjectUtils.isNotEmpty(rclList)){
            for (const rcj of rclList) {
                rcjObj.sequenceNbr = rcj.sequenceNbr;
                rcjObj.constructProjectRcj = constructProjectRcj;
                await this.changeRcjNewJieSuan(rcjObj);
            }
        }

    }


    /**
     * 批量调整结算除税系数---工程项目
     * @param constructId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list  所选调差数据集合  为空表示当前多有分类下的数据
     * @param value  批量调整除税系数
     */
    async constructBatchUpdateTaxRemoval(args) {
        //获取参数
        let {constructId, tcType, list, value} = args;
        let constructProjectRcjList = [];
        let rcjObj = {
            constructId: constructId,
            type: 1
        };

        if (ObjectUtils.isEmpty(list)) {
            let type = 1;
            if (tcType == 20) {
                type = 2
            }
            let obj = {
                constructId: constructId,
                type: type,
                kind: tcType
            };
            list = this.jieSuanRcjProcess.projectRcjList(obj);
        }

        //过滤掉   机械人工（JXPB-005）、机械人工费（JXPB-006）
        list = list.filter(rcj => (rcj.materialCode != "JXPB-005" && rcj.materialCode != "JXPB-006" && rcj.kind!=1));
        for (const rcj of list) {
            let constructProjectRcj = {
                jieSuanTaxRemoval: value
            };
            rcjObj.sequenceNbr = rcj.sequenceNbr;
            rcjObj.constructProjectRcj = constructProjectRcj;
            await this.changeRcjNewJieSuan(rcjObj);
        }

    }


    /**
     * 人材机分期查看
     * @param args
     */
    getRcjStageList(args) {

        let {constructId, singleId, unitId, kind} = args;
        //获取指定的单位信息
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        // 判断是否进行了分期
        if (ObjectUtil.isNotEmpty(unit.rcjStageSet)) {
            let periods = unit.rcjStageSet.periods;

            // 材料父级
            let copyRcjs = ObjectUtil.cloneDeep(unit.constructProjectRcjs);
            //材料子级
            let childDetails = ObjectUtil.cloneDeep(unit.rcjDetailList);
            // //获取费用定额的ID
            let costDeIdList = this.jieSuanRcjProcess._getCostDeIdList(unit);

            // 遍历人材机父级
            if (ObjectUtil.isNotEmpty(copyRcjs)) {
                for (let i = 0; i < copyRcjs.length; i++) {
                    let rcj = copyRcjs[i];
                    let jieSuanTotalNumber = 0;
                    let jieSuanStageDifferenceQuantity = 0;
                    rcj.isChild = false;

                    // 按照分期数，遍历添加列数
                    let de;
                    let qd;
                    if (ObjectUtil.isNotEmpty(periods)) {
                        // 通过父级人材机deId查询对应的定额
                        de = this.getDeByRcjId(constructId, singleId, unitId, rcj.deId);
                        if (ObjectUtil.isEmpty(de)) {
                            rcj.jieSuanTotalNumber = 0;
                            rcj.jieSuanStageDifferenceQuantity = 0;
                        } else {
                            // 通过定额Id查询清单
                            qd = PricingFileFindUtils.getQdByDeId(constructId, singleId, unitId, de.sequenceNbr);
                            for (let j = 1; j <= periods; j++) {
                                // 添加期数对应的列数
                                // 人材机分期工程量计算   a. 第i分期定额工程量=第i期清单工程量*定额含量（即结算定额工程量/结算清单工程量）
                                //                    b. 第i期A材料工程量 =∑ （第i分期定额工程量*材料A消耗量）
                                // 第i期A材料工程量
                                if (ObjectUtil.isEmpty(de.jieSuanQuantity) || de.jieSuanQuantity == 0) {
                                    rcj['column' + j] = 0
                                } else {
                                    // 获取该清单的第i工程量
                                    let periodQuantitiy = qd.stageQuantitiesList.find(i => i.stage === j).stageQuantity;
                                    console.log("---第i期清单工程含量----", qd.stageQuantitiesList[j - 1].stageQuantity)
                                    rcj['column' + j] = NumberUtil.multiply(NumberUtil.divide(Number(de.jieSuanQuantity) ? Number(de.jieSuanQuantity) : 0,
                                        Number(qd.jieSuanQuantity) ? Number(qd.jieSuanQuantity) : 0), rcj.resQty ? rcj.resQty : 0, periodQuantitiy);
                                }
                                // 计算该人材机子集的工程量，并添加人材机子集标识
                                jieSuanTotalNumber = jieSuanTotalNumber + rcj['column' + j];
                            }
                        }

                        //费用定额人材机以及二次解析的父级材料
                        if (costDeIdList.includes(rcj.deId) || (rcj.markSum === 1 && (rcj.levelMark === 1 || rcj.levelMark === 2))) {
                            //置灰标识
                            rcj.isGray = true;
                        }
                        // 获取该人材机的子集材料，通过rcjId过滤
                        let childDetailList = childDetails.filter(childDetail => childDetail.rcjId == rcj.sequenceNbr);
                        //是否二次解析（如果二次解析需要将子级材料也填充进来）
                        if (ObjectUtil.isNotEmpty(childDetailList) && rcj.markSum === 1) {
                            childDetailList.forEach(childDetail => {
                                let childJieSuanTotalNumber = 0;
                                for (let k = 1; k <= periods; k++) {
                                    childDetail['column' + k] = NumberUtil.multiply(rcj['column' + k], childDetail.resQty);
                                    childJieSuanTotalNumber = childJieSuanTotalNumber + childDetail['column' + k];
                                }
                                childDetail.jieSuanTotalNumber = childJieSuanTotalNumber;
                                childDetail.jieSuanStageDifferenceQuantity = childJieSuanTotalNumber;
                                childDetail.isChild = true;
                            })
                            copyRcjs.push(...childDetailList);
                        } else {
                            rcj.jieSuanTotalNumber = jieSuanTotalNumber;
                            rcj.jieSuanStageDifferenceQuantity = jieSuanTotalNumber;
                        }

                    } else {
                        rcj.jieSuanTotalNumber = 0;
                        rcj.jieSuanStageDifferenceQuantity = 0;
                    }
                }
            }

            copyRcjs = copyRcjs.filter(rcj => ObjectUtil.isEmpty(rcj.isGray) || rcj.isGray === false);
            //分组
            let copyRcjMap = copyRcjs.reduce((accumulator, currentValue) => {
                // 将分组作为对象的 key，相同分组的项放入同一个数组
                (accumulator[currentValue.materialCode] = accumulator[currentValue.materialCode] || []).push(currentValue);
                return accumulator;
            }, {});

            let rcjList = [];
            // 遍历map并同时遍历每个id对应的人材机列表
            for (let item in copyRcjMap) {

                //取第一条数据
                let firstRcj = copyRcjMap[item][0];
                rcjList.push(firstRcj);

                let jieSuanTotalNumber = 0;
                if (ObjectUtil.isNotEmpty(periods)) {
                    for (let j = 1; j <= periods; j++) {
                        let jieSuanNumber = 0;
                        // 再次遍历rcjs数组
                        for (let m = 0; m < copyRcjMap[item].length; m++) {
                            let rcj = copyRcjMap[item][m];
                            jieSuanNumber += rcj['column' + j];
                        }
                        firstRcj['column' + j] = parseFloat(jieSuanNumber.toFixed(2));
                        jieSuanTotalNumber += jieSuanNumber;
                    }
                }
                firstRcj.jieSuanTotalNumber = parseFloat(jieSuanTotalNumber.toFixed(2));
            }
            return rcjList;
        } else {
            return null;
        }
    }


    async getRcjStageList1(args){
        let {constructId, singleId, unitId, kind} = args;

        //获取指定的单位信息
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);


        if (ObjectUtil.isNotEmpty(unit.rcjStageSet)){


            let periods = unit.rcjStageSet.periods;
            if (ObjectUtils.isEmpty(periods)){
                return null;
            }
            //args.num =1;
            let unitRcjQuery = await this.jieSuanRcjProcess.unitRcjQuery(args);

            for (let j = 1; j <= periods; j++){
                args.num =j;
                let unitRcjQueryJ = await this.jieSuanRcjProcess.unitRcjQuery(args);

                for (let i = 0; i < unitRcjQueryJ.length; i++) {
                    unitRcjQuery[i]['column' + j] = unitRcjQueryJ[i].jieSuanDifferenceQuantity;
                    //unitRcjQuery[i]['column' + 1] = unitRcjQuery[i].jieSuanDifferenceQuantity;
                }
            }


            for (let unitRcjQueryKey of unitRcjQuery) {
                let jieSuanTotalNumber = unitRcjQueryKey.jieSuanTotalNumber;
                let totalNumber = unitRcjQueryKey.totalNumber;

                unitRcjQueryKey.totalNumber = jieSuanTotalNumber;
                unitRcjQueryKey.jieSuanTotalNumber = totalNumber;

            }

            return unitRcjQuery;
        }else {
            return null;
        }
    }

    /**
     * 通过人材机id 获取定额
     * @param constructId
     * @param singleId
     * @param unitId
     * @param sequenceNbr
     * @returns {null|T} 人材机id
     */
    getDeByRcjId(constructId, singleId, unitId, sequenceNbr) {
        let unitProject = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // 分部分项
        let itemBillProjects = unitProject.itemBillProjects;
        let t = itemBillProjects.find(k => k.sequenceNbr === sequenceNbr);
        if (!ObjectUtils.isEmpty(t)) {
            let t1 = itemBillProjects.find(i => i.sequenceNbr == t.sequenceNbr);
            return t1;
        }
        // 措施项目
        let measureProjectTables = unitProject.measureProjectTables;
        let t2 = measureProjectTables.find(k => k.sequenceNbr === sequenceNbr);
        if (!ObjectUtils.isEmpty(t2)) {
            let t3 = measureProjectTables.find(i => i.sequenceNbr == t2.sequenceNbr);
            return t3;
        }
        return null;
    }


    /**
     * 人材机分期查看导出Excel
     * @param args
     */
    async exportRcjStageListExcel(args) {

        //导出的xlsx
        let {constructId, singleId, unitId, kind} = args;
        //获取指定的单位信息
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        let defaultStoragePath = await this.commonService.getSetStoragePath(unit.upName + '_分期工程量', JieSuanConstantUtil.JIESUAN_FILE_SUFFIX_XLSX);


        let toRemove = '.YSF';
        let index = defaultStoragePath.lastIndexOf(toRemove);

        defaultStoragePath = (index !== -1) ? defaultStoragePath.slice(0, index) + defaultStoragePath.slice(index + toRemove.length):defaultStoragePath;
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.toString(),
            filters: [{name: '云算房文件', extensions: ['xlsx']}],
        };
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (ObjectUtils.isEmpty(filePath)) {
            console.log("未选中任何文件");
            return ResponseData.fail("未选中任何文件");
        }
        if (filePath && !filePath.canceled) {
            if (!filePath.endsWith(".xlsx")) {
                filePath += ".xlsx";
            }

            // 人材机分期查看列表
            let copyRcjList = this.getRcjStageList(args);
            let rcjList = copyRcjList.filter(rcj => ObjectUtil.isEmpty(rcj.isGray) || rcj.isGray === false);
            // 获取所有对象的所有属性名以生成动态列
            const allKeys = [];
            rcjList.forEach(obj => {
                Object.keys(obj).forEach(key => {
                    if (!allKeys.includes(key) && (key === 'materialCode' || key === 'materialName' || key === 'specification' || key === 'unit'
                        || key === 'kind' || key === 'totalNumber' || key.includes('column'))) {
                        allKeys.push(key);
                    }
                });
            });

            // 创建一个二维数组来表示Excel表格的内容
            const tableData = [allKeys].concat(rcjList.map(obj => allKeys.map(key => obj[key] || '')));


            // 遍历数组并转换指定列的数据    kind(0：其他费；1：人工费；2：材料费；3：机械费；4：设备费；5：主材费；6：商砼；7：砼；8：浆；9：商浆；10：配比)
            const columnToTranslateIndex = 4; // 假设 "columnToTranslate" 是数组中的第 5 列
            tableData.forEach(item => {
                switch (item[columnToTranslateIndex]) {
                    case 1:
                        item[columnToTranslateIndex] = "人工费";
                        break;
                    case 2:
                        item[columnToTranslateIndex] = "材料费";
                        break;
                    case 3:
                        item[columnToTranslateIndex] = "机械费";
                        break;
                    case 4:
                        item[columnToTranslateIndex] = "设备费";
                        break;
                    case 5:
                        item[columnToTranslateIndex] = "主材费";
                        break;
                    case 6:
                        item[columnToTranslateIndex] = "商砼";
                        break;
                    case 7:
                        item[columnToTranslateIndex] = "砼";
                        break;
                    case 8:
                        item[columnToTranslateIndex] = "浆";
                        break;
                    case 9:
                        item[columnToTranslateIndex] = "商浆";
                        break;
                    case 10:
                        item[columnToTranslateIndex] = "配比";
                        break;
                    default:
                        item[columnToTranslateIndex] = "其他费";
                        break;
                }

            });


            // 定义英文标题与中文标题的映射关系
            const titleMap = {
                materialCode: '编码',
                materialName: '名称',
                specification: '规格型号',
                unit: '单位',
                kind: '类别',
                totalNumber: '合同数量',
                jieSuanTotalNumber: '结算数量'
            };
            // 将二维数组的第一行（标题）转换为汉字标题
            const translatedHeaders = tableData[0].map((title) => {
                // 如果找到了，则返回对应的中文标题
                if (titleMap[title]) {
                    title = titleMap[title];
                } else {
                    // 如果没找到，则保持原样返回/进行特殊处理
                    if (title.includes("column")) {
                        let column = title.split('column')[1];
                        title = "第" + column + '期';
                    }
                }
                // 如果找到了，则返回对应的中文标题；如果没找到，则保持原样返回
                // return titleMap[title] || title;
                return title;
            });
            // 创建新的二维数组，包含转换后的标题和原数据
            const translatedData = [translatedHeaders].concat(tableData.slice(1));


            // 将数据转换为工作表对象
            const worksheet = XLSX.utils.aoa_to_sheet(translatedData);
            // 创建工作簿并填充数据
            const workbook = XLSX.utils.book_new();

            // 定义单元格样式
            const headerStyle = {
                font: {bold: true},
                alignment: {horizontal: 'center'},
                fill: {patternType: 'solid', fgColor: {argb: 'FF808080'}}  // 设置背景色为浅灰色

            };
            // // 设置第一行（标题行）的样式
            // XLSX.utils.sheet_set_row(worksheet, 0, null, headerStyle);

            // 遍历第一行（标题行）并设置每个单元格的样式
            // for (let col = 0; col < translatedData[0].length; col++) {
            //     const cellAddress = {r: 0, c: col}; // 行号从0开始，列号从0开始
            //     const cellRef = XLSX.utils.encode_cell(cellAddress);
            //
            //     // 获取当前单元格，并设置其样式
            //     const cell = worksheet[XLSX.utils.decode_cell(cellRef)];
            //     cell.s = headerStyle;
            // }

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

            // 导出为Excel文件  当前单位工程名称+分期工程量
            // XLSX.writeFile(workbook,  unitName + '分期工程量.xlsx');

            // 将工作簿转换为二进制数据流
            const wbBuffer = XLSX.write(workbook, {bookType: 'xlsx', type: 'buffer'});

            // 将二进制数据流保存到桌面，需要指定保存的路径和文件名
            fs.writeFileSync(filePath, wbBuffer);
            //-------------------------------------------------------------------------------
        }

        //
        // let {constructId, singleId, unitId, kind} = args;
        // //获取指定的单位信息
        // let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // // 人材机分期查看列表
        // let copyRcjList = this.getRcjStageList(args);
        //
        // // 获取所有对象的所有属性名以生成动态列
        // const allKeys = [];
        // copyRcjList.forEach(obj => {
        //     Object.keys(obj).forEach(key => {
        //         if (!allKeys.includes(key) && (key === 'materialCode' || key ==='materialName' || key ==='specification' || key ==='unit'
        //             || key ==='kind' || key ==='totalNumber' || key.includes('column'))) {
        //             allKeys.push(key);
        //         }
        //     });
        // });
        //
        // // 创建一个二维数组来表示Excel表格的内容
        // const tableData = [allKeys].concat(copyRcjList.map(obj => allKeys.map(key => obj[key] || '')));
        //
        // // 将数据转换为工作表对象
        // const worksheet = XLSX.utils.aoa_to_sheet(tableData);
        // // 创建工作簿并填充数据
        // const workbook = XLSX.utils.book_new();
        //
        // // 添加工作表到工作簿
        // XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
        //
        // // 导出为Excel文件  当前单位工程名称+分期工程量
        // // XLSX.writeFile(workbook,  unitName + '分期工程量.xlsx');
        //
        // // 将工作簿转换为二进制数据流
        // const wbBuffer = XLSX.write(workbook, {bookType: 'xlsx', type: 'buffer'});
        //
        // // 将二进制数据流保存到桌面，需要指定保存的路径和文件名
        // fs.writeFileSync('C:\\Users\\<USER>\\Desktop\\' + unit.upName + '_分期工程量.xlsx', wbBuffer);
    }



    /**
     * 人材机参与调差
     * @param args
     */
    rcjParticipateInAdjustment(args) {

        let unitListN = [];
        let unitListW = [];
        //  判断是单位层级的按钮还是工程项目层级的按钮
        if (ObjectUtil.isNotEmpty(args.unitId)) {
            // 合同内的所有单位
            unitListN = PricingFileFindUtils.getUnitList(args.constructId).filter(unit => unit.originalFlag == true);
            // 查询指定单位   合同外
            let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
            unitListW.push(unit);
        } else {
            // 查询工程項目下所有的合同内单位
            unitListN = PricingFileFindUtils.getUnitList(args.constructId).filter(unit => unit.originalFlag == true);
            unitListW = PricingFileFindUtils.getUnitList(args.constructId).filter(unit => unit.originalFlag != true);
        }

        let rcjListN = [];
        let unitNMap;
        // 遍历合同内单位，获取合同内单位的所有人材机数据
        if (ObjectUtil.isNotEmpty(unitListN)) {
            for (let i = 0; i < unitListN.length; i++) {
                let unitN = unitListN[i];
                let rcjN = unitN.constructProjectRcjs;
                let childRcjN = unitN.rcjDetailList;

                //获取费用定额的ID
                let costDeIdList = this.jieSuanRcjProcess._getCostDeIdList(unitN);
                if (ObjectUtil.isNotEmpty(rcjN)) {
                    rcjN.forEach(k => {
                        //费用定额人材机以及二次解析的父级材料
                        /*if (costDeIdList.includes(k.deId) || (k.markSum == 1 && (k.levelMark == 1 || k.levelMark == 2))) {
                            //置灰标识
                            k.isGray = true;
                        }*/
                        //是否二次解析（如果二次解析需要将子级材料也填充进来）
                        if (k.markSum === 1 && ObjectUtil.isNotEmpty(childRcjN)) {
                            let childRcjs = childRcjN.filter(i => i.rcjId === k.sequenceNbr);
                            rcjN.push(...childRcjs);
                            rcjListN = rcjListN.concat(rcjN);
                        }
                    });
                }
            }
            // 合同内单位进行id分组
            unitNMap = unitListN.reduce((accumulator, currentItem) => {
                // 将分组作为对象的 key，相同分组的项放入同一个数组
                (accumulator[currentItem.sequenceNbr] = accumulator[currentItem.sequenceNbr] || []).push(currentItem);
                return accumulator;
            }, {});
        }

        let rcjListW;
        let childRcjListW;
        let unitWMap;
        // 遍历合同外单位，获取合同外的父级人材机、子级人材机
        if (ObjectUtil.isNotEmpty(unitListW)) {
            for (let i = 0; i < unitListW.length; i++) {
                let unitW = unitListW[i];
                rcjListW = unitW.constructProjectRcjs;
                childRcjListW = unitW.rcjDetailList;
                // 设置合同外单位人材机参与调差标识
                if (!unitW.originalFlag == true) {
                    unitW.isDifference = true;
                }
            }
            // 合同外单位进行id分组
            unitWMap = unitListW.reduce((accumulator, currentItem) => {
                // 将分组作为对象的 key，相同分组的项放入同一个数组
                (accumulator[currentItem.sequenceNbr] = accumulator[currentItem.sequenceNbr] || []).push(currentItem);
                return accumulator;
            }, {});

        }

        // 遍历合同内的人材机，判断合同外是否匹配合同内的人材机
        if (ObjectUtil.isNotEmpty(rcjListN)) {
            for (let i = 0; i < rcjListN.length; i++) {
                let rcjN = rcjListN[i];
                rcjN.tempcol = rcjN.materialCode.concat(rcjN.kind).concat(rcjN.materialName).concat(rcjN.specification).concat(rcjN.unit).concat(rcjN.dePrice);
                // 判断合同内是非甲供
                if (ObjectUtil.isEmpty(rcjN.ifDonorMaterial) || rcjN.ifDonorMaterial == JieSuanConstantUtil.IF_DONORMATERIAL_0) {
                    if (ObjectUtil.isNotEmpty(rcjListW)) {
                        for (let i = 0; i < rcjListW.length; i++) {
                            let rcjW = rcjListW[i];
                            // 添加初始默认值
                            if (ObjectUtil.isNotEmpty(rcjW.marketPrice)) {
                                if (ObjectUtil.isEmpty(rcjW.jieSuanBasePrice)) {
                                    rcjW.jieSuanBasePrice = rcjW.marketPrice;
                                }
                                if (ObjectUtil.isEmpty(rcjW.jieSuanPrice)) {
                                    rcjW.jieSuanPrice = rcjW.marketPrice;
                                }
                            }

                            // 设置合同外人材机的调差标识
                            // rcjW.isDifference = true;
                            rcjW.tempcol = rcjW.materialCode.concat(rcjW.kind).concat(rcjW.materialName).concat(rcjW.specification).concat(rcjW.unit).concat(rcjW.dePrice);
                            // 调差计算
                            this.adjustmentCalculation(rcjN, rcjW, unitNMap, unitWMap);
                        }
                    }

                    if (ObjectUtil.isNotEmpty(childRcjListW)) {
                        for (let i = 0; i < childRcjListW.length; i++) {
                            let childRcjW = childRcjListW[i];
                            // 设置合同外人材机的调差标识
                            // childRcjW.isDifference = true;
                            childRcjW.tempcol = childRcjW.materialCode.concat(childRcjW.kind).concat(childRcjW.materialName).concat(childRcjW.specification).concat(childRcjW.unit).concat(childRcjW.dePrice);
                            // 调差计算
                            this.adjustmentCalculation(rcjN, childRcjW, unitNMap, unitWMap);
                        }
                    }
                }

            }
        }
    }


    /**
     * 判断 结算项目中是否有分期单位工程
     * @returns {Promise<ResponseData>}
     */
    async jieSuanConstructProjectFq(args){
        let constructId = args.constructId;

        let unitList = PricingFileFindUtils.getUnitList(constructId);

        if (ObjectUtils.isEmpty(unitList)){
            return false;
        }

        for (let unitListKey of unitList) {

            if (!ObjectUtils.isEmpty(unitListKey.rcjStageSet)){
                let periods = unitListKey.rcjStageSet.periods;
                if (!ObjectUtils.isEmpty(periods)){
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 调差计算
     *
     * @param rcjN  合同内所有人材机
     * @param rcjW  合同外父级人材机/子级人材机
     * @param unitNMap  合同内单位
     * @param unitWMap  合同外单位
     */
    adjustmentCalculation(rcjN, rcjW, unitNMap, unitWMap) {
        if (rcjN.tempcol == rcjW.tempcol) {

            // 设置合同外人材机的调差标识


            // 合同内第一个单位的，遍历数结构，查询当前符合条件的合同内材料，并非甲供数据
            // 获取该人材机的单位是否为分期
            if (unitNMap.hasOwnProperty(rcjN.unitId)) {
                let unit = unitNMap[rcjN.unitId];

                // 若与合同外相同的合同内关联人材机数据采用【价格指数调整法】或应用了【人材机分期】调整
                // 则对应的合同外人材机仅继承是否调差，风险幅度范围、取费设置，结算单价和基期价格默认等于其合同/确认单价；默认采用造价信息差额调整法
                if ((ObjectUtil.isNotEmpty(unit.rcjDifference) && unit.rcjDifference.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD4.code) || ObjectUtil.isNotEmpty(unit.rcjStageSet)) {
                    rcjW.isDifference = rcjN.isDifference;
                    rcjW.riskAmplitudeRangeMin = rcjN.riskAmplitudeRangeMin;
                    rcjW.riskAmplitudeRangeMax = rcjN.riskAmplitudeRangeMax;
                    rcjW.jieSuanFee = rcjN.jieSuanFee;
                    rcjW.jieSuanPrice = rcjN.jieSuanPrice;
                    rcjW.jieSuanBasePrice = rcjN.jieSuanBasePrice;
                    rcjW.jieSuanUnitPriceDifferencList = rcjN.jieSuanUnitPriceDifferencList;
                    rcjW.isDifference = rcjN.isDifference;

                } else {
                    // 若与合同外相同的合同内关联人材机数据未采用【价格指数调整法】且不应用【人材机分期】调整，
                    // 则对应的合同外人材机继承对应合同内人材机数据的调差状态，基期价格、结算单价、风险幅度范围、取费设置以及调差方式
                    rcjW.riskAmplitudeRangeMin = rcjN.riskAmplitudeRangeMin;
                    rcjW.riskAmplitudeRangeMax = rcjN.riskAmplitudeRangeMax;
                    rcjW.jieSuanFee = rcjN.jieSuanFee;
                    rcjW.jieSuanPrice = rcjN.jieSuanPrice;
                    rcjW.jieSuanBasePrice = rcjN.jieSuanBasePrice;
                    rcjW.isDifference = rcjN.isDifference;
                }
                let rcjArray = [];
                rcjArray.push(rcjW);
                // 判断合同内材料是否分期
                if (ObjectUtil.isNotEmpty(unit[0].rcjStageSet)) {
                    for (let i = 0; i < unit[0].rcjStageSet.periods; i++) {
                        let unitW;
                        if (unitWMap.hasOwnProperty(rcjW.unitId)) {
                            unitW = unitWMap[rcjW.unitId];
                            // 【造价信息差额调整法】进行人材机调整
                            this.priceAdjustmentMethod1(rcjArray, rcjArray[0].constructId, unitW.singleId, rcjArray[0].unitId, rcjArray[0].kind, false, null);
                        }
                    }

                    // 不分期
                } else {
                    // 判断是否存在调差方式
                    if (ObjectUtil.isNotEmpty(unit[0].rcjDifference)) {
                        // 判断合同内价格调整方式是否为【价格指数调整法】
                        if (unit[0].rcjDifference.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD4.code) {
                            let unitW;
                            if (unitWMap.hasOwnProperty(rcjW.unitId)) {
                                unitW = unitWMap[rcjW.unitId];
                                // 【造价信息差额调整法】进行人材机调整
                                this.priceAdjustmentMethod1(rcjArray, rcjArray[0].constructId, unitW.singleId, rcjArray[0].unitId, rcjArray[0].kind, false, null);
                            }
                        } else {
                            let unitW;
                            if (unitWMap.hasOwnProperty(rcjW.unitId)) {
                                unitW = unitWMap[rcjW.unitId];
                                // 以对应的人材机结算方式进行价差计算
                                //造价信息价格差额调整法
                                if (unit[0].rcjDifference.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD1.code) {
                                    this.priceAdjustmentMethod1(rcjArray, rcjArray[0].constructId, unitW.singleId, rcjArray[0].unitId, rcjArray[0].kind, false, null);
                                }
                                //结算价与基期价差额调整法
                                if (unit[0].rcjDifference.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD2.code) {
                                    this.priceAdjustmentMethod2(rcjArray, rcjArray[0].constructId, unitW.singleId, rcjArray[0].unitId, rcjArray[0].kind, false, null);
                                }
                                //结算价与合同价差额调整法
                                if (unit[0].rcjDifference.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD3.code) {
                                    this.priceAdjustmentMethod3(rcjArray, rcjArray[0].constructId, unitW.singleId, rcjArray[0].unitId, rcjArray[0].kind, false, null);
                                }
                            }

                        }
                    } else {
                        console.log("调差方式为空。。。");
                    }
                }
            }

        } else {
            if (rcjW.materialCode.replace(/#/g, "") == "10000001" || rcjW.materialCode.replace(/#/g, "") == "10000002" || rcjW.materialCode.replace(/#/g, "") == "10000003") {
                let rcjArray = [];
                rcjArray.push(rcjW);

                let unitW;
                if (unitWMap.hasOwnProperty(rcjW.unitId)) {
                    unitW = unitWMap[rcjW.unitId];
                    // 【造价信息差额调整法】进行人材机调整
                    this.priceAdjustmentMethod1(rcjArray, rcjArray[0].constructId, unitW.singleId, rcjArray[0].unitId, rcjArray[0].kind, false, null);
                }
            }
        }

    }


    /**
     * 取消人材机参与调差
     *
     * @param args
     */
    cancelRcjParticipateInAdjustment(args) {
        let unitList = [];
        if (ObjectUtil.isEmpty(args.unitId)) {
            // 合同外的所有单位
            unitList = PricingFileFindUtils.getUnitList(args.constructId).filter(unit => unit.originalFlag == false);
        } else {
            // 获取指定单位的数据
            let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
            unitList.push(unit);
        }
        // 遍历合同外的人材机
        for (let i = 0; i < unitList.length; i++) {
            let unit = unitList[i];
            // 取消单位的的调差状态
            unit.isDifference = false;
            // 取消人材机父级的调差状态
            let rcjs = unit.constructProjectRcjs;
            if (ObjectUtil.isNotEmpty(rcjs)) {
                for (let j = 0; j < rcjs.length; j++) {
                    if (ObjectUtil.isNotEmpty(rcjs[j].isDifference) && rcjs[j].isDifference == true && rcjs[j].kind != 1
                        && rcjs[j].ifProvisionalEstimate != 1 && rcjs[j].ifDonorMaterial  != 1) {
                        rcjs[j].isDifference = false;
                    }
                }
            }
            // 取消人材机子级的调差状态
            let childRcjs = unit.rcjDetailList;
            if (ObjectUtil.isNotEmpty(childRcjs)) {
                for (let j = 0; j < childRcjs.length; j++) {
                    if (ObjectUtil.isNotEmpty(childRcjs[j].isDifference) && childRcjs[j].isDifference == true && childRcjs[j] != 1
                        && childRcjs[j].ifProvisionalEstimate != 1 && childRcjs[j].ifDonorMaterial  != 1) {
                        childRcjs[j].isDifference = false;
                    }
                }
            }
        }

    }


    /**
     * 批量调整风险幅度范围设置
     * @param constructId
     * @param singleId
     * @param unitId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费  4 暂估材料调差
     * @param list  所选调差数据集合 如果是空表示修改的是整个单位
     */
    async riskAmplitudeRange(args) {
        //获取参数
        let {constructId, singleId, unitId, tcType, list, max, min} = args;
        let updateData;
        if (ObjectUtils.isEmpty(list)) {
            // JieSuanRcjDifferenceEnum.RENGONG.code == kind
            let obj = {
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
                kind: tcType
            };
            updateData = await this.jieSuanRcjProcess.unitRcjQuery(obj);
        } else {
            updateData = list;
        }

        //创建请求参数
        let rcjObj = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            type: 2
        };
        //更新数据
        for (const rcj of updateData) {
            let constructProjectRcj = {
                riskAmplitudeRangeMin: min,
                riskAmplitudeRangeMax: max
            };
            rcjObj.sequenceNbr = rcj.sequenceNbr;
            rcjObj.constructProjectRcj = constructProjectRcj;
            await this.changeRcjNewJieSuan(rcjObj);
        }
    }


    /**
     * 批量调整风险幅度范围设置---工程项目
     * @param constructId
     * @param singleId
     * @param unitId
     * @param tcType  调差类型  1 人工费  2 材料费 3 机械费
     * @param list  所选调差数据集合 如果是空表示修改的是整个单位
     */
    async constructRiskAmplitudeRange(args) {
        //获取参数
        let {constructId, singleId, unitId, tcType, list, max, min} = args;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        projectObjById.riskAmplitudeRangeMax = max;
        projectObjById.riskAmplitudeRangeMin = min;
        let updateData;
        if (ObjectUtils.isEmpty(list)) {
            // JieSuanRcjDifferenceEnum.RENGONG.code == kind
            let obj = {
                constructId: constructId,
                type: 2,
                kind: tcType
            };
            updateData = this.jieSuanRcjProcess.projectRcjList(obj);
        } else {
            updateData = list;
        }

        //创建请求参数
        let rcjObj = {
            constructId: constructId,
        };

        let constructProjectRcjList = [];
        //更新数据
        for (const rcj of updateData) {
            let constructProjectRcj = {
                riskAmplitudeRangeMin: min,
                riskAmplitudeRangeMax: max,
                sequenceNbr: rcj.sequenceNbr
            };
            constructProjectRcjList.push(constructProjectRcj)
        }

        rcjObj.constructProjectRcjList = constructProjectRcjList;
        await this.rcjProcess.changeRcjConstructProject(rcjObj);
    }

    /**
     * 批量应用价差取费设置----工程项目
     * @param constructId
     * @param kind   二级类型
     * @param map  {key1:value1,key2:value2,key3:value3}  key是类型：1（人工）  2（材料） 3（机械），  value：具体指（code）
     */
    async constructPriceDifferenceDeeSetting(args) {
        let {constructId, kind, map} = args;
        //合同外的价差取费设置
        if (kind == 20 || kind == JieSuanRcjDifferenceEnum.ZANGUJIA.code) {
            let arg = {
                constructId: constructId,
                type: 2,
                kind: kind
            }
            let projectRcjList = this.jieSuanRcjProcess.projectRcjList(arg);
            let updateProjectRcjList = [];
            let keys = Object.keys(map);
            let values = Object.values(map);
            if (keys.length < 3) {
                return ResponseData.fail("费用设置请求参数异常");
            }
            //设置数据
            for (const rcj of projectRcjList) {
                if (rcl.kind == 1) {
                    rcj.jieSuanFee = values[0];
                } else if (rcl.kind == 4) {
                    rcj.jieSuanFee = values[2];
                } else {
                    rcj.jieSuanFee = values[1];
                }
                //更新数据this.rcjProcess
                let constructor = {
                    jieSuanFee: rcj.jieSuanFee
                }
                updateProjectRcjList.push(constructor);
            }

            let argRcj = {
                constructId: constructId,
                constructProjectRcjList: updateProjectRcjList
            }
            this.rcjProcess.changeRcjConstructProject(argRcj);
        } else {
            let keys = Object.keys(map);
            let values = Object.values(map);
            if (keys.length < 3) {
                return ResponseData.fail("费用设置请求参数异常");
            }
            for (let i = 0; i < 3; i++) {
                let arg = {
                    constructId: constructId,
                    type: 1,
                    kind: keys[i]
                }
                let projectRcjList = this.jieSuanRcjProcess.projectRcjList(arg);
                let updateProjectRcjList = [];
                //设置数据
                for (const rcj of projectRcjList) {
                    rcj.jieSuanFee = values[i];
                    //更新数据this.rcjProcess
                    let constructor = {
                        jieSuanFee: rcj.jieSuanFee
                    }
                    updateProjectRcjList.push(constructor);
                }

                let argRcj = {
                    constructId: constructId,
                    constructProjectRcjList: updateProjectRcjList
                }
                this.rcjProcess.changeRcjConstructProject(argRcj);
            }

        }

    }

    /**
     * 批量应用价差取费设置
     * @param constructId
     * @param singleId
     * @param unitId
     * @param map  {key1:value1,key2:value2,key3:value3}  key是类型：1（人工）  2（材料） 3（机械），  value：具体指（code）
     */
    async priceDifferenceDeeSetting(args) {
        //获取参数
        let {constructId, singleId, unitId, map} = args;
        //获取指定类型的调差数据
        let rgList;
        let clList;
        let jxList;
        let keys = Object.keys(map);
        let values = Object.values(map);
        if (keys.length < 3) {
            return ResponseData.fail("费用设置请求参数异常");
        }
        //获取各类型数据
        let obj = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            kind: keys[0]
        }
        rgList = await this.jieSuanRcjProcess.unitRcjQuery(obj);
        obj.kind = keys[1];
        clList = await this.jieSuanRcjProcess.unitRcjQuery(obj);
        obj.kind = keys[2];
        jxList = await this.jieSuanRcjProcess.unitRcjQuery(obj);
        //更新数据
        //封装修改参数
        let constructProjectRcj = {
            jieSuanFee: values[0]
        };
        await this.batchchangeRcj(constructId, singleId, unitId, 2, rgList, constructProjectRcj);
        constructProjectRcj.jieSuanFee = values[1];
        await this.batchchangeRcj(constructId, singleId, unitId, 2, clList, constructProjectRcj);
        constructProjectRcj.jieSuanFee = values[2];
        await this.batchchangeRcj(constructId, singleId, unitId, 2, jxList, constructProjectRcj);

    }

    /**
     *
     * @param constructId
     * @param singleId
     * @param unitId
     * @param type  1 工程项目  2 单位工程
     * @param rclList  要修改的人材机集合
     * @param constructProjectRcj   要改的字段名和值
     * @returns {Promise<void>}
     */
    async batchchangeRcj(constructId, singleId, unitId, type, rclList, constructProjectRcj) {
        let constructPro =PricingFileFindUtils.getProjectObjById(constructId);

        let libraryCode = constructPro.libraryCode;

        if (ObjectUtil.isEmpty(libraryCode)){
            libraryCode = "2012";
        }

        let rcjObj = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            type: type,
            libraryCode:libraryCode
        };
        if(ObjectUtils.isNotEmpty(rclList)){
            for (const rcj of rclList) {
                rcjObj.sequenceNbr = rcj.sequenceNbr;
                rcjObj.constructProjectRcj = constructProjectRcj;
                await this.changeRcjNewJieSuan(rcjObj);
            }
        }

    }



    //四种结算人材机调整法---工程项目
    async constructPriceDifferenceAdjustmentMethod(args) {
        let {constructId, clType, methodType} = args;
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        let jieSuanRcjDifferenceTypeList = projectObjById.jieSuanRcjDifferenceTypeList;
        for (const type of jieSuanRcjDifferenceTypeList) {
            if (type.rcjDifferenceType == clType) {
                type.JieSuanPriceAdjustmentMethodType = methodType;
            }
        }
        //获取所有的合同内单项
        let singleProjectList = PricingFileFindUtils.getSingleProjectList(constructId);
        for (const single of singleProjectList) {
            if (single.originalFlag) {
                //获取所有单位
                let unitProjects = single.unitProjects;
                if (!ObjectUtils.isEmpty(unitProjects)){
                    for (let unit of unitProjects) {
                        let obj = {
                            constructId: constructId,
                            singleId: single.sequenceNbr,
                            unitId: unit.sequenceNbr,
                            clType: clType,
                            methodType: methodType
                        }
                        await this.priceDifferenceAdjustmentMethod(obj);
                    }
                }
            }
        }

    }


    /**
     * 初始化工程项目级别的调整法
     * @param obj
     */
    initPriceDifferenceAdjustmentMetho(obj) {
        let jieSuanRcjDifferenceTypeList = [
            {
                rcjDifferenceType: JieSuanRcjDifferenceEnum.RENGONG.code,
                JieSuanPriceAdjustmentMethodType: 1,
            }, {
                rcjDifferenceType: JieSuanRcjDifferenceEnum.CAILIAO.code,
                JieSuanPriceAdjustmentMethodType: 1,
            }, {
                rcjDifferenceType: JieSuanRcjDifferenceEnum.JIXIE.code,
                JieSuanPriceAdjustmentMethodType: 1,
            },{
                rcjDifferenceType: JieSuanRcjDifferenceEnum.ZANGUJIA.code,
                JieSuanPriceAdjustmentMethodType: 1,
            }];
        obj.jieSuanRcjDifferenceTypeList = jieSuanRcjDifferenceTypeList;


    }


    /**
     * 四种结算人材机调整法
     * @param constructId
     * @param singleId
     * @param unitId
     * @param
     */
    async priceDifferenceAdjustmentMethod(args) {
        let {constructId, singleId, unitId, clType, methodType} = args;
        let rcjList;
        //获取工程项目数据
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        /*let jieSuanRcjDifferenceTypeList = projectObjById.jieSuanRcjDifferenceTypeList;
        for (let jieSuanRcjDifferenceTypeListElement of jieSuanRcjDifferenceTypeList) {
            if (jieSuanRcjDifferenceTypeListElement.rcjDifferenceType ==clType){
                jieSuanRcjDifferenceTypeListElement.JieSuanPriceAdjustmentMethodType = methodType;
            }
        }*/

        //projectObjById.rcjDifferenceType = methodType;
        //修改单位中人材机调整类型
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (ObjectUtils.isEmpty(unit)){
            return;
        }
        let rcjDifference = unit.rcjDifference;
        // let rcjDifferenceSetVos = rcjDifference.filter(obj => obj.kind == clType);
        for (const obj of rcjDifference) {
            if (obj.kind == clType) {
                obj.rcjDifferenceType = methodType;
                break;
            }
        }


        //获取人材机数据请求参数
        let obj = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            kind: clType
        }
        //判断单位是否进行了分期
        let fqFlag;
        let fqCs;
        if (ObjectUtils.isEmpty(unit.rcjStageSet)) {
            fqFlag = false;
        } else {
            fqFlag = true;
            //获取分期次数
            fqCs = unit.rcjDifference[0].frequencyList;
        }
        if (fqFlag) {
            for (let i = 0; i < fqCs.length; i++) {
                await this.priceDifferenceCount(constructId, singleId, unitId, clType, fqFlag, i + 1, methodType, obj);
            }
        } else {
            //不分期
            await this.priceDifferenceCount(constructId, singleId, unitId, clType, fqFlag, null, methodType, obj);
        }

        //this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args);
    }


    async priceDifferenceCount(constructId, singleId, unitId, clType, fqFlag, fqNum, methodType, obj) {
        let rcjList;
        obj.num = fqNum;
        if (clType == JieSuanRcjDifferenceEnum.ZANGUJIA.code) {
            await this.zgjPriceAdjustmentMethod(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
        } else {
            //人工
            if (clType == JieSuanRcjDifferenceEnum.RENGONG.code) {
                rcjList = await this.jieSuanRcjProcess.unitRcjQuery(obj);
            }
            //材料
            if (clType == JieSuanRcjDifferenceEnum.CAILIAO.code) {
                rcjList = await this.jieSuanRcjProcess.unitRcjQuery(obj);
            }
            //机械
            if (clType == JieSuanRcjDifferenceEnum.JIXIE.code) {
                rcjList = await this.jieSuanRcjProcess.unitRcjQuery(obj);
            }

            if (ObjectUtils.isEmpty(rcjList)) {
                return;
            }
            //处理调整数据
            //造价信息价格差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD1.code) {
                await this.priceAdjustmentMethod1(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
            //结算价与基期价差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD2.code) {
                await this.priceAdjustmentMethod2(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
            //结算价与合同价差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD3.code) {
                await this.priceAdjustmentMethod3(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
            //价格指数差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD4.code) {
                await this.priceAdjustmentMethod4(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
        }
    }



    /**
     * 结算人材机统一调差
     * @param args
     */
    async jieSuanRcjListTongYiTiaoCha(args){
        //rcjList 调差list
        let {constructId, singleId, unitId, rcjList,clType,fqNum} = args;

        let fqFlag;

        let originalFlag;

        if (ObjectUtils.isEmpty(constructId) || ObjectUtils.isEmpty(rcjList)){
            return [];
        }
        let  map ;

        if (!ObjectUtil.isEmpty(fqNum)){
            fqFlag =true;
        }else {
            fqFlag =false;
        }

        //查询对应的 设置调差 对象
        if (ObjectUtils.isEmpty(unitId)){
            let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
            let rcjDifferenceType = projectObjById.jieSuanRcjDifferenceTypeList;
            map = new Map(rcjDifferenceType.map(item => [item.rcjDifferenceType, item.JieSuanPriceAdjustmentMethodType]));

        }else{

            let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
            if (ObjectUtil.isEmpty(unit)){
                return [];
            }
            let rcjDifferenceType = unit.rcjDifference;
            map = new Map(rcjDifferenceType.map(item => [item.kind, item.rcjDifferenceType]));
            originalFlag = !ObjectUtil.isEmpty(unit.originalFlag)?unit.originalFlag:false;
        }

        // 判断材料 类型
        if (!ObjectUtil.isEmpty(clType) && clType!=0){
            let methodType = map.get(clType);
            if (ObjectUtil.isEmpty(methodType) || !originalFlag ){
                methodType =1;
            }
            //造价信息价格差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD1.code) {
                await this.priceAdjustmentMethod1(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
            //结算价与基期价差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD2.code) {
                await this.priceAdjustmentMethod2(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
            //结算价与合同价差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD3.code) {
                await this.priceAdjustmentMethod3(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
            //价格指数差额调整法
            if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD4.code) {
                await this.priceAdjustmentMethod4(rcjList, constructId, singleId, unitId, clType, fqFlag, fqNum);
            }
        }else {

            let kindMap = new Map();
            kindMap.set(1,1);
            kindMap.set(2,2);
            kindMap.set(3,3);
            kindMap.set(4,2);
            kindMap.set(5,2);
            kindMap.set(6,2);
            kindMap.set(7,2);
            kindMap.set(8,2);
            kindMap.set(9,2);
            kindMap.set(10,2);

            for (let rcj of rcjList) {
                let rcjClType ;
                if (rcj.ifProvisionalEstimate ==1){
                    rcjClType = 8;
                }else {
                    rcjClType = kindMap.get(rcj.kind);
                }
                let methodType = map.get(rcjClType);
                if (ObjectUtil.isEmpty(methodType)  ){
                    methodType =1;
                }
                let array = new Array();
                array.push(rcj);

                //造价信息价格差额调整法
                if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD1.code) {
                    await this.priceAdjustmentMethod1(array, constructId, singleId, unitId, clType, fqFlag, fqNum);
                }
                //结算价与基期价差额调整法
                if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD2.code) {
                    await this.priceAdjustmentMethod2(array, constructId, singleId, unitId, clType, fqFlag, fqNum);
                }
                //结算价与合同价差额调整法
                if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD3.code) {
                    await this.priceAdjustmentMethod3(array, constructId, singleId, unitId, clType, fqFlag, fqNum);
                }
                //价格指数差额调整法
                if (methodType == JieSuanPriceAdjustmentMethodEnum.METHOD4.code) {
                    await this.priceAdjustmentMethod4(array, constructId, singleId, unitId, clType, fqFlag, fqNum);
                }

            }

        }
        if (!ObjectUtils.isEmpty(unitId)){
            let getCostDeIdList = this.service.jieSuanProject.jieSuanRcjProcess._getCostDeIdList(PricingFileFindUtils.getUnit(constructId,singleId,unitId));
            for (let rcjListElement of rcjList) {
                if (rcjListElement.kind ==1 && getCostDeIdList.includes(rcjListElement.deId)){
                    rcjListElement.jieSuanPriceDifferenc = 0;
                    rcjListElement.jieSuanPriceDifferencSum = 0;
                }
            }
        }

        if (ObjectUtil.isEmpty(rcjList)){
            return [];
        }

        return rcjList;
    }


    /**
     *  暂估价单位价差计算
     * @param list
     * @param constructId
     * @param singleId
     * @param unitId
     * @param clType
     * @param isFq
     * @param fqNum
     * @returns {Promise<void>}
     */
    async zgjPriceAdjustmentMethod(list, constructId, singleId, unitId, clType, isFq, fqNum) {
        for (const rcj of list) {
            rcj.jieSuanPriceDifferenc = NumberUtil.subtract(rcj.jieSuanPrice, rcj.marketPrice);
        }
        let constructProjectRcj = {};
        if (isFq) {
            let jieSuanUnitPriceDifferencList = rcj.jieSuanUnitPriceDifferencList;
            jieSuanUnitPriceDifferencList[fqNum] = rcj.jieSuanPriceDifferenc;
            constructProjectRcj.jieSuanUnitPriceDifferencList = jieSuanUnitPriceDifferencList;
        } else {
            constructProjectRcj.jieSuanPriceDifferenc = rcj.jieSuanPriceDifferenc;
        }
        //更新人材机数据
        await this.rcjProcess.batchchangeRcj(constructId, singleId, unitId, 2, list, constructProjectRcj);
    }


//造价信息价格差额调整法
    /**
     *
     * @param list
     * @param constructId
     * @param singleId
     * @param unitId
     * @param clType
     * @param isFq  是否分期
     * @param fqNum 第几期
     * @returns {Promise<void>}
     */
    async priceAdjustmentMethod1(list, constructId, singleId, unitId, clType, isFq, fqNum) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        //政策人工费
        /*let zcRgf;
        if (!ObjectUtils.isEmpty(unit)){

            let rgfId = unit.rgfId;
            //有可能为空
            if (!ObjectUtils.isEmpty(rgfId)) {
                zcRgf = await this.service.basePolicyDocumentService.queryBySequenceNbr(rgfId);
            }
        }*/

        //默认不分期
        let map = new Map();
        let periods = 1
        if (!isFq && !ObjectUtil.isEmpty(unit) && !ObjectUtil.isEmpty(unit.rcjStageSet) && unit.rcjStageSet.isStage ==true ){
            periods = unit.rcjStageSet.periods;

           map = this.service.jieSuanProject.jieSuanRcjProcess.jieSuanFenQiRcjTotalNumber(constructId, singleId, unitId);

        }

        for (let rcj of list) {
            let promise =await this.rcjTiaoCha(rcj);

            if (!promise){
                continue;
            }

            let jiaChaHeJi = 0;
            for (let i = 0; i < periods; i++) {

                if (periods != 1){
                    fqNum = i+1;
                    isFq = true;
                }

                //获取人材机的调整法
                //张老师的 费用记取之后 人材机没有 结算需要的人材机jieSuanRcjDifferenceTypeList属性 特别处理
                await this.rcjJieSuanInit(rcj);
                let jieSuanRcjDifferenceTypeList = rcj.jieSuanRcjDifferenceTypeList;
                let rcjDifferenceInfos = jieSuanRcjDifferenceTypeList.filter(rcj => rcj.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD1.code);
                let k1=rcj.riskAmplitudeRangeMin;
                let k2=rcj.riskAmplitudeRangeMax;
                let htj = Number(rcj.marketPrice);
                // let jqj = Number(rcj.jieSuanBasePrice);
                let jqj = Number(rcjDifferenceInfos[0].jieSuanBasePrice);
                let jsdj;
                if (isFq) {
                    jsdj = Number(rcjDifferenceInfos[0].jieSuanDifferencePriceList[fqNum - 1].jieSuanPrice);
                } else {
                    jsdj = Number(rcjDifferenceInfos[0].jieSuanDifferencePriceList[0].jieSuanPrice);
                }

                //if (rcj.kind != 1){
                if (htj < jqj) {
                    if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) > NumberUtil.divide100(k2)) {
                        rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, NumberUtil.add(1 , NumberUtil.divide100(k2))));
                        //计算涨跌幅
                        rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj),100));
                    } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj) < NumberUtil.divide100(k1)) {
                        rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(htj, NumberUtil.add(1 , NumberUtil.divide100(k1))));
                        //计算涨跌幅
                        rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj),100));
                    } else {
                        rcj.jieSuanPriceDifferenc = 0;
                        rcj.jieSuanPriceLimit=0;
                    }
                } else if (htj > jqj) {
                    if (NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj) > NumberUtil.divide100(k2)) {
                        rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(htj, NumberUtil.add(1 , NumberUtil.divide100(k2))));
                        //计算涨跌幅
                        rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj),100));
                    } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) < NumberUtil.divide100(k1)) {
                        rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, NumberUtil.add(1 , NumberUtil.divide100(k1))));
                        //计算涨跌幅
                        rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj),100));
                    } else {
                        rcj.jieSuanPriceDifferenc = 0;
                        rcj.jieSuanPriceLimit=0;
                    }
                } else if (htj == jqj) {
                    if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) > NumberUtil.divide100(k2)) {
                        rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, NumberUtil.add(1 , NumberUtil.divide100(k2))));
                        //计算涨跌幅
                        rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj,jqj),jqj),100));
                    } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) <k1) {
                        rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, NumberUtil.add(1 , NumberUtil.divide100(k1))));
                        //计算涨跌幅
                        rcj.jieSuanPriceLimit=NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj),100));
                    } else {
                        rcj.jieSuanPriceDifferenc = 0;
                        rcj.jieSuanPriceLimit=0;
                    }
                }
                /*}else {
                    rcj.jieSuanPriceLimit=0;

                    let rgfDj ;
                    if (!ObjectUtils.isEmpty(zcRgf)) {
                        if (rcj.materialCode.includes("10000001")) {
                            //一级
                            rgfDj= zcRgf.zhygLevel1;
                        } else if (rcj.materialCode.includes("10000003")) {
                            //三级
                            rgfDj= zcRgf.zhygLevel3;
                        } else {
                            //二级
                            rgfDj= zcRgf.zhygLevel2;
                        }

                        if (htj< rgfDj){
                            rcj.jieSuanPriceDifferenc=NumberUtil.subtract(rgfDj,htj);
                        }else {
                            rcj.jieSuanPriceDifferenc= 0
                        }
                    }else {
                        rcj.jieSuanPriceDifferenc=0;
                    }
                }*/


                let constructProjectRcj = {};

                rcj.jieSuanDifferenceQuantity = rcj.totalNumber;
                //结算价差合计  价差合计=单位价差*调差工程量
                if (periods ==1){
                    rcj.jieSuanPriceDifferencSum = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceDifferenc, rcj.jieSuanDifferenceQuantity));
                }else {
                    if (!ObjectUtil.isEmpty(map)){
                        let v = map.get(rcj.materialCode+fqNum);
                        v = !ObjectUtils.isEmpty(v)?v:0;
                        rcj.jieSuanPriceDifferencSum = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceDifferenc, v));
                    }

                }

                //结算价差进项税额
                rcj.settlementPriceDifferencInputTax = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.multiply(rcj.jieSuanPriceDifferencSum,rcj.jieSuanTaxRemoval),0.01))

                if (isFq) {
                    let jieSuanUnitPriceDifferencList = rcj.jieSuanUnitPriceDifferencList;
                    jieSuanUnitPriceDifferencList[fqNum - 1] = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanUnitPriceDifferencList = jieSuanUnitPriceDifferencList;

                    //第N期单位价差
                    rcj.jieSuanUnitPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    //第N期价差合计
                    rcj.jieSuanStagePriceDifferencSum = rcj.jieSuanPriceDifferencSum;
                    //第N期涨跌幅
                    rcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;
                    //第n期价差进项税额
                    rcj.jieSuanStagePriceDifferencInputTax = rcj.settlementPriceDifferencInputTax;

                } else {
                    constructProjectRcj.jieSuanPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;
                }

                jiaChaHeJi = NumberUtil.add(jiaChaHeJi,rcj.jieSuanPriceDifferencSum);

            }
            rcj.jieSuanPriceDifferencSum = jiaChaHeJi;

        }


    }


//结算价与基期价差额调整法
    async priceAdjustmentMethod2(list, constructId, singleId, unitId, clType, isFq, fqNum) {

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);


        //默认不分期
        let map = new Map();
        let periods = 1
        if (!isFq && !ObjectUtil.isEmpty(unit) && !ObjectUtil.isEmpty(unit.rcjStageSet) && unit.rcjStageSet.isStage ==true ){
            periods = unit.rcjStageSet.periods;

            map = this.service.jieSuanProject.jieSuanRcjProcess.jieSuanFenQiRcjTotalNumber(constructId, singleId, unitId);

        }


        for (const rcj of list) {
            let promise =await this.rcjTiaoCha(rcj);

            if (!promise){
                continue;
            }

            let jiaChaHeJi = 0;
            for (let i = 0; i < periods; i++) {

                if (periods != 1){
                    fqNum = i+1;
                    isFq = true;
                }

                //获取人材机的调整法
                //张老师的 费用记取之后 人材机没有 结算需要的人材机jieSuanRcjDifferenceTypeList属性 特别处理
                await this.rcjJieSuanInit(rcj);
                let jieSuanRcjDifferenceTypeList = rcj.jieSuanRcjDifferenceTypeList;
                let rcjDifferenceInfos = jieSuanRcjDifferenceTypeList.filter(rcj => rcj.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD2.code);
                let k1=rcj.riskAmplitudeRangeMin;
                let k2=rcj.riskAmplitudeRangeMax;
                let htj = Number(rcj.marketPrice);
                // let jqj = Number(rcj.jieSuanBasePrice);
                let jqj = Number(rcjDifferenceInfos[0].jieSuanBasePrice);
                let jsdj;
                if (isFq) {
                    jsdj = Number(rcjDifferenceInfos[0].jieSuanDifferencePriceList[fqNum - 1].jieSuanPrice);
                } else {
                    jsdj = Number(rcjDifferenceInfos[0].jieSuanDifferencePriceList[0].jieSuanPrice);
                }

                /*if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) > k2) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, 1 + JieSuanConstantUtil.JIEQI_BILI1));
                } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) < k1) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, 1 - JieSuanConstantUtil.JIEQI_BILI1));
                } else {
                    rcj.jieSuanPriceDifferenc = 0;
                }*/


                if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) > NumberUtil.divide100(k2)) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, NumberUtil.add(1 , NumberUtil.divide100(k2))));
                    //计算涨跌幅
                    rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj),100));
                } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj) < NumberUtil.divide100(k1)) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(jqj, NumberUtil.add(1 , NumberUtil.divide100(k1))));
                    //计算涨跌幅
                    rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, jqj), jqj),100));
                } else {
                    rcj.jieSuanPriceDifferenc = 0;
                    rcj.jieSuanPriceLimit = 0;
                }

                let constructProjectRcj = {};
                rcj.jieSuanDifferenceQuantity = rcj.totalNumber;
                //结算价差合计  价差合计=单位价差*调差工程量
                if (periods ==1){
                    rcj.jieSuanPriceDifferencSum = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceDifferenc, rcj.jieSuanDifferenceQuantity));
                }else {
                    if (!ObjectUtil.isEmpty(map)){
                        let v = map.get(rcj.materialCode+fqNum);
                        v = !ObjectUtils.isEmpty(v)?v:0;
                        rcj.jieSuanPriceDifferencSum = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceDifferenc, v));
                    }

                }
                //结算价差进项税额
                rcj.settlementPriceDifferencInputTax = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.multiply(rcj.jieSuanPriceDifferencSum,rcj.jieSuanTaxRemoval),0.01));
                if (isFq) {
                    let jieSuanUnitPriceDifferencList = rcj.jieSuanUnitPriceDifferencList;
                    jieSuanUnitPriceDifferencList[fqNum - 1] = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanUnitPriceDifferencList = jieSuanUnitPriceDifferencList;

                    //第N期单位价差
                    rcj.jieSuanUnitPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    //第N期价差合计
                    rcj.jieSuanStagePriceDifferencSum = rcj.jieSuanPriceDifferencSum;
                    //第N期涨跌幅
                    rcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;

                    //第n期价差进项税额
                    rcj.jieSuanStagePriceDifferencInputTax = rcj.settlementPriceDifferencInputTax;

                } else {
                    constructProjectRcj.jieSuanPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;

                }
                jiaChaHeJi = NumberUtil.add(jiaChaHeJi,rcj.jieSuanPriceDifferencSum);
            }
            rcj.jieSuanPriceDifferencSum = jiaChaHeJi;
        }


    }

//结算价与合同价差额调整法
    async priceAdjustmentMethod3(list, constructId, singleId, unitId, clType, isFq, fqNum) {

        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        //默认不分期
        let map = new Map();
        let periods = 1
        if (!isFq && !ObjectUtil.isEmpty(unit) && !ObjectUtil.isEmpty(unit.rcjStageSet) && unit.rcjStageSet.isStage ==true ){
            periods = unit.rcjStageSet.periods;

            map = this.service.jieSuanProject.jieSuanRcjProcess.jieSuanFenQiRcjTotalNumber(constructId, singleId, unitId);

        }

        for (const rcj of list) {
            let promise =await this.rcjTiaoCha(rcj);

            if (!promise){
                continue;
            }

            let jiaChaHeJi = 0;
            for (let i = 0; i < periods; i++) {

                if (periods != 1){
                    fqNum = i+1;
                    isFq = true;
                }

                //获取人材机的调整法
                //张老师的 费用记取之后 人材机没有 结算需要的人材机jieSuanRcjDifferenceTypeList属性 特别处理
                await this.rcjJieSuanInit(rcj);
                let jieSuanRcjDifferenceTypeList = rcj.jieSuanRcjDifferenceTypeList;
                let rcjDifferenceInfos = jieSuanRcjDifferenceTypeList.filter(rcj => rcj.rcjDifferenceType == JieSuanPriceAdjustmentMethodEnum.METHOD3.code);
                let k1=rcj.riskAmplitudeRangeMin;
                let k2=rcj.riskAmplitudeRangeMax;
                let htj = Number(rcj.marketPrice);
                // let jqj = Number(rcj.jieSuanBasePrice);
                let jqj = Number(rcjDifferenceInfos[0].jieSuanBasePrice);
                let jsdj;
                if (isFq) {
                    jsdj = Number(rcjDifferenceInfos[0].jieSuanDifferencePriceList[fqNum - 1].jieSuanPrice);
                } else {
                    jsdj = Number(rcjDifferenceInfos[0].jieSuanDifferencePriceList[0].jieSuanPrice);
                }
                /*if (NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj) >  NumberUtil.divide100(k2)) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(htj, 1 + JieSuanConstantUtil.JIEQI_BILI1));
                } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj) < k1) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(htj, 1 - JieSuanConstantUtil.JIEQI_BILI1));
                } else {
                    rcj.jieSuanPriceDifferenc = 0;
                }*/
                if (NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj) > NumberUtil.divide100(k2)) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(htj, NumberUtil.add(1 , NumberUtil.divide100(k2))));
                    //计算涨跌幅
                    rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj),100));
                } else if (NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj) < NumberUtil.divide100(k1)) {
                    rcj.jieSuanPriceDifferenc = NumberUtil.subtract(jsdj, NumberUtil.multiply(htj, NumberUtil.add(1 , NumberUtil.divide100(k1))));
                    //计算涨跌幅
                    rcj.jieSuanPriceLimit=NumberUtil.roundHalfUp(NumberUtil.multiply(NumberUtil.divide(NumberUtil.subtract(jsdj, htj), htj),100));
                } else {
                    rcj.jieSuanPriceDifferenc = 0;
                    rcj.jieSuanPriceLimit=0;
                }

                let constructProjectRcj = {};


                rcj.jieSuanDifferenceQuantity = rcj.totalNumber;
                //结算价差合计  价差合计=单位价差*调差工程量
                if (periods ==1){
                    rcj.jieSuanPriceDifferencSum = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceDifferenc, rcj.jieSuanDifferenceQuantity));
                }else {
                    if (!ObjectUtil.isEmpty(map)){
                        let v = map.get(rcj.materialCode+fqNum);
                        v = !ObjectUtils.isEmpty(v)?v:0;
                        rcj.jieSuanPriceDifferencSum = NumberUtil.numberScale2(NumberUtil.multiply(rcj.jieSuanPriceDifferenc, v));
                    }

                }
                //结算价差进项税额
                rcj.settlementPriceDifferencInputTax = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.multiply(rcj.jieSuanPriceDifferencSum,rcj.jieSuanTaxRemoval),0.01));

                if (isFq) {
                    let jieSuanUnitPriceDifferencList = rcj.jieSuanUnitPriceDifferencList;
                    jieSuanUnitPriceDifferencList[fqNum - 1] = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanUnitPriceDifferencList = jieSuanUnitPriceDifferencList;
                    //第N期单位价差
                    rcj.jieSuanUnitPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    //第N期价差合计
                    rcj.jieSuanStagePriceDifferencSum = rcj.jieSuanPriceDifferencSum;
                    //第N期涨跌幅
                    rcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;

                    //第n期价差进项税额
                    rcj.jieSuanStagePriceDifferencInputTax = rcj.settlementPriceDifferencInputTax;

                } else {
                    constructProjectRcj.jieSuanPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;

                }
                jiaChaHeJi = NumberUtil.add(jiaChaHeJi,rcj.jieSuanPriceDifferencSum);

            }

            rcj.jieSuanPriceDifferencSum = jiaChaHeJi;

        }

    }

//价格指数差额调整法
    async priceAdjustmentMethod4(list, constructId, singleId, unitId, clType, isFq, fqNum) {

        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(unit)){
            return;
        }

        let unitCostSummary = unit.unitCostSummarys.find(i=>i.type =='工程造价');
        if (ObjectUtils.isEmpty(unitCostSummary)){
            return;
        }
        //工程造价的 结算金额
        let price1 = unitCostSummary.price;
        //工程造价的 合同金额
        let jieSuanPrice = unitCostSummary.jieSuanPrice;
        if (ObjectUtils.isEmpty(price1) || ObjectUtils.isEmpty(jieSuanPrice)){
            return;
        }
        //P0取值来源，费用汇总工程造价(k)下，结算金额
        //计算B中的分母 费用汇总工程造价(k)下, 合同金额



        //默认不分期
        //let map = new Map();
        let periods = 1
        if (!isFq && !ObjectUtil.isEmpty(unit) && !ObjectUtil.isEmpty(unit.rcjStageSet) && unit.rcjStageSet.isStage ==true ){
            periods = unit.rcjStageSet.periods;

        }

        for (const rcj of list) {

            let jiaChaHeJi = 0;
            for (let i = 0; i < periods; i++) {

                if (periods != 1){
                    fqNum = i+1;
                    isFq = true;
                }

                let f0;
                let ft;
                if (ObjectUtil.isEmpty(fqNum)){
                    f0 = rcj.jieSuanBasePriceF0;
                    ft = rcj.jieSuanCurrentPriceF0;
                }else {
                    let jieSuanBasePriceF0Map = rcj.jieSuanBasePriceF0Map;
                    f0 = ObjectUtil.isEmpty(jieSuanBasePriceF0Map)?null:jieSuanBasePriceF0Map.get(fqNum);
                    let jieSuanCurrentPriceFtMap = rcj.jieSuanCurrentPriceFtMap;
                    ft = ObjectUtil.isEmpty(jieSuanCurrentPriceFtMap)?null:jieSuanCurrentPriceFtMap.get(fqNum);

                    rcj.jieSuanBasePriceF0 = f0;
                    rcj.jieSuanCurrentPriceF0 = ft;
                }
                rcj.jieSuanDifferenceQuantity = rcj.totalNumber;
                let promise =await this.rcjTiaoCha(rcj);

                if (!promise){
                    continue;
                }
                //if (!isFq){
                //计算B
                let b = NumberUtil.numberScale4(NumberUtil.divide(rcj.total,price1));
                rcj.jieSuanValuetWeightB = b;

                //计算A 定制权重
                let a = NumberUtil.numberScale4(NumberUtil.subtract(1,b));
                let constructProjectRcj = {};
                if (!ObjectUtils.isEmpty(f0) && !ObjectUtils.isEmpty(ft)) { //计算Ft除 F0 乘B
                    f0 = Number(f0);
                    ft = Number(ft);


                    //计算公式里 圆括号里面的
                    let x = NumberUtil.numberScale4(NumberUtil.multiply(b, NumberUtil.divide(ft ,f0)));

                    //计算工厂里中括号里面的
                    let z = NumberUtil.numberScale4(NumberUtil.add(a, NumberUtil.add(x, -1)));

                    let deltaP = NumberUtil.numberScale4(NumberUtil.multiply(price1, z));


                    rcj.jieSuanPriceDifferenc = NumberUtil.numberScale4(NumberUtil.divide(deltaP,rcj.totalNumber));
                    rcj.jieSuanPriceDifferencSum = deltaP;

                    //结算价差进项税额
                    rcj.settlementPriceDifferencInputTax = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.multiply(rcj.jieSuanPriceDifferencSum,rcj.jieSuanTaxRemoval),0.01));


                }else {
                    rcj.jieSuanPriceDifferenc = 0;
                    rcj.jieSuanPriceDifferencSum = 0;
                    rcj.settlementPriceDifferencInputTax = 0;

                }

                if (isFq) {
                    //let jieSuanUnitPriceDifferencList = rcj.jieSuanUnitPriceDifferencList;
                    /*jieSuanUnitPriceDifferencList[fqNum - 1] = rcj.jieSuanPriceDifferenc;
                    constructProjectRcj.jieSuanUnitPriceDifferencList = jieSuanUnitPriceDifferencList;*/
                    //第N期单位价差
                    rcj.jieSuanUnitPriceDifferenc = rcj.jieSuanPriceDifferenc;
                    //第N期价差合计
                    rcj.jieSuanStagePriceDifferencSum = rcj.jieSuanPriceDifferencSum;
                    //第N期涨跌幅
                    rcj.jieSuanPriceLimit = rcj.jieSuanPriceLimit;

                    //第n期价差进项税额
                    rcj.jieSuanStagePriceDifferencInputTax = rcj.settlementPriceDifferencInputTax;

                }

                jiaChaHeJi = NumberUtil.add(jiaChaHeJi,rcj.jieSuanPriceDifferencSum);

            }

            rcj.jieSuanPriceDifferencSum = jiaChaHeJi;
        }

    }


    /**
     * 针对 预算人材机在 结算中 没有结算需要的 数据字段 重新进行数据填充
     * 目前特制张老师的 费用记取后 新增的人材机没有 结算需要的 jieSuanRcjDifferenceTypeList属性
     * @param rcj
     * @returns {Promise<void>}
     */
    async rcjJieSuanInit(rcj){
        if (!ObjectUtils.isEmpty(rcj) && ObjectUtils.isEmpty(rcj.jieSuanRcjDifferenceTypeList)){
            let args =[];
            args.push(rcj);
            this.service.jieSuanProject.jieSuanProjectService.rcjDataHandler(args);
        }
    }

    async rcjTiaoCha(rcj){
        if (rcj.kind==1 || rcj.ifProvisionalEstimate ==1 || rcj.isDifference ){

            return true;
        }else {
            rcj.jieSuanDifferenceQuantity = rcj.totalNumber;


            //第N期单位价差
            rcj.jieSuanUnitPriceDifferenc = 0;
            //第N期价差合计
            rcj.jieSuanStagePriceDifferencSum = 0;
            //第N期涨跌幅
            rcj.jieSuanPriceLimit = 0;

            rcj.jieSuanPriceDifferenc = 0
            rcj.jieSuanPriceDifferencSum =0;
            rcj.jieSuanPriceLimit = 0;
            rcj.settlementPriceDifferencInputTax = 0;

            return false;

        }
    }


    /**
     * 查询指定单位
     * @param args
     * @returns {Promise<*>}
     */
    getUnitProjectById(args) {
        let unit = PricingFileFindUtils.getUnit(args.constructId, args.singleId, args.unitId);
        return unit;
    }

    /**
     * 合同外清单工程量修改
     */
    htwQdQuantity(qd){
        JieSuanRcjStageUtils.qdStageCalculate(qd);
    }


    /**
     *
     * @param args
     * @returns {Promise<void>}
     */
    async changeRcjHuiZongJiesuan(args){
        if (args.type ==1 || ObjectUtils.isEmpty(args.type)){
            if (!ObjectUtils.isEmpty(args.constructProjectRcjList)){
                let constructProjectRcjList = args.constructProjectRcjList;

                for (let constructProjectRcjListElement of constructProjectRcjList) {
                    let arg ={};
                    arg.constructId = args.constructId;
                    arg.type = 1;
                    arg.sequenceNbr = constructProjectRcjListElement.sequenceNbr;

                    arg.constructProjectRcj = constructProjectRcjListElement;
                    await this.changeRcjNewJieSuan(arg);
                }
            }
            if (!ObjectUtils.isEmpty(args.constructProjectRcj)){
                let arg ={};
                arg.constructId = args.constructId;
                arg.type = 1;
                arg.sequenceNbr = args.sequenceNbr;

                arg.constructProjectRcj = args.constructProjectRcj;
                await this.changeRcjNewJieSuan(arg);
            }


            //费用汇总计算
            let projectObjById = PricingFileFindUtils.getProjectObjById(args.constructId);
            if (!ObjectUtils.isEmpty(projectObjById)){
                let unitList = PricingFileFindUtils.getUnitList(args.constructId);

                if (!ObjectUtils.isEmpty(unitList)){
                    for (let unitListKey of unitList) {
                        let cs = {};

                        cs.constructId = unitListKey.constructId;
                        cs.singleId = unitListKey.spId;
                        cs.unitId = unitListKey.sequenceNbr;

                        //费用汇总计算
                        await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(cs);
                    }
                }
            }

        }else {
         await this.changeRcjNewJieSuan(args);

         //费用汇总计算
         await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args);
        }
    }




    /**
     * 结算修改人材机
     * @param type 数据类型 1 工程项目层级  2 单位层级
     * @param constructId 工程项目id
     * @param singleId 单项工程id
     * @param unitId 单位工程id
     * @param sequenceNbr 修改目标 对象id
     * @param constructProjectRcj 修改后对象值
     */
    async changeRcjNewJieSuan(args) {
        let { constructId, singleId, unitId, sequenceNbr, type, libraryCode ,fqNum,rcjDifferenceType } = args;
        if (ObjectUtil.isEmpty(libraryCode)){
            libraryCode = '2012';
        }


        //名称
        let materialName = args.constructProjectRcj.materialName;
        //规格及型号
        let specification = args.constructProjectRcj.specification;
        //单位
        let unitBj = args.constructProjectRcj.unit;
        let showMarketPrice = args.constructProjectRcj.marketPrice; //前端自定义展示所用市场价
        //类型
        let kind = args.constructProjectRcj.kind;
        //是否是暂估
        let ifProvisionalEstimate = args.constructProjectRcj.ifProvisionalEstimate;
        //是否是甲供
        let ifDonorMaterial = args.constructProjectRcj.ifDonorMaterial;
        //是否锁定材料价格
        let ifLockStandardPrice = args.constructProjectRcj.ifLockStandardPrice;
        //是否汇总
        let markSum = args.constructProjectRcj.markSum;
        //甲供数量
        let donorMaterialNumber = args.constructProjectRcj.donorMaterialNumber;

        //产地
        let producer = args.constructProjectRcj.producer;
        //厂家
        let manufactor = args.constructProjectRcj.manufactor;
        //品牌
        let brand = args.constructProjectRcj.brand;
        //送达地点
        let deliveryLocation = args.constructProjectRcj.deliveryLocation;
        //质量等级
        let qualityGrade = args.constructProjectRcj.qualityGrade;

        //主材表 输出勾选
        let output = args.constructProjectRcj.output;

        //保管费率
        let jieSuanAdminRate = args.constructProjectRcj.jieSuanAdminRate;
        //是否调差
        let isDifference = args.constructProjectRcj.isDifference;
        //备注
        let description = args.constructProjectRcj.description;
        //风险幅度
        let riskAmplitudeRangeMin = args.constructProjectRcj.riskAmplitudeRangeMin;
        let riskAmplitudeRangeMax = args.constructProjectRcj.riskAmplitudeRangeMax;
        //结算取费下拉
        let jieSuanFee = args.constructProjectRcj.jieSuanFee;
        //基期价
        let jieSuanBasePrice = args.constructProjectRcj.jieSuanBasePrice;
        //基期价来源
        let jieSuanBasePriceSource = args.constructProjectRcj.jieSuanBasePriceSource;

        //除税系数
        let taxRemoval = args.constructProjectRcj.taxRemoval;
        let jieSuanTaxRemoval = args.constructProjectRcj.jieSuanTaxRemoval;
        //单位价差
        let priceDifferenc = args.constructProjectRcj.priceDifferenc;
        //单位价差集合
        let jieSuanUnitPriceDifferencList = args.constructProjectRcj.jieSuanUnitPriceDifferencList;
        //基本 价格指数
        let jieSuanBasePriceF0 = args.constructProjectRcj.jieSuanBasePriceF0;

        //现行价格指数Ft
        let jieSuanCurrentPriceF0 = args.constructProjectRcj.jieSuanCurrentPriceF0;
        //结算第n期除税系数
        let jieSuanStagetaxRemoval = args.constructProjectRcj.jieSuanStagetaxRemoval;
        //调整法价格集合
        let jieSuanRcjDifferenceTypeList = args.constructProjectRcj.jieSuanRcjDifferenceTypeList

        let jieSuanPrice = args.constructProjectRcj.jieSuanPrice;
        //结算价差
        let jieSuanPriceDifferenc=args.constructProjectRcj.jieSuanPriceDifferenc;
        //涨跌幅
        let jieSuanPriceLimit=args.constructProjectRcj.jieSuanPriceLimit;
        //第N期单价
        let jieSuanStagePrice=args.constructProjectRcj.jieSuanStagePrice;
        //单价来源
        let jieSuanPriceSource=args.constructProjectRcj.jieSuanPriceSource;
        //是否载价
        let highlight=args.constructProjectRcj.highlight;

        //备注
        let remark=args.constructProjectRcj.remark;


        //输出标记
        let outputToken=args.constructProjectRcj.outputToken;

        //合同价
        let jieSuanMarketPrice=args.constructProjectRcj.jieSuanMarketPrice;



        let constructRcjArray = new Array();

        //查询 单位工程汇总
        if (type == 2) {
            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
            if (!ObjectUtils.isEmpty(unit)) {
                if (!ObjectUtils.isEmpty(unit.constructProjectRcjs)) {
                    constructRcjArray.push(...unit.constructProjectRcjs);
                }
                if (!ObjectUtils.isEmpty(unit.rcjDetailList)) {
                    constructRcjArray.push(...unit.rcjDetailList);
                }
            } else {
                return null;
            }
        } else {
            //查询工程项目汇总
            let unitList = PricingFileFindUtils.getUnitList(constructId);
            for (let unitListKey of unitList) {
                if (!ObjectUtils.isEmpty(unitListKey.constructProjectRcjs)) {
                    constructRcjArray.push(...unitListKey.constructProjectRcjs);
                }
                if (!ObjectUtils.isEmpty(unitListKey.rcjDetailList)) {
                    constructRcjArray.push(...unitListKey.rcjDetailList);
                }
            }
        }

        //根据id找到对应人材机
        let dx = constructRcjArray.find(i => i.sequenceNbr === sequenceNbr);

        if (ObjectUtils.isEmpty(dx)) {
            return null;
        }
        //根据id找到对应人材机重新copy
        let t = new ConstructProjectRcj();
        ConvertUtil.setDstBySrc(dx, t);


        //确定 #数字到位置
        let map = constructRcjArray.map(i => i.materialCode);

        let from = Array.from(new Set(map));
        let array = new Array();
        for (let fromElement of from) {
            if (fromElement.includes('#')) {
                let s = fromElement.substring(fromElement.lastIndexOf('#') + 1);
                array.push(s);
            }
        }
        let max = 1;
        if (!ObjectUtils.isEmpty(array)) {
            max = Math.max.apply(null, array) + 1;
        }

        //工程项目
        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);

        //计税方式
        let taxCalculationMethod = projectObjById.projectTaxCalculation.taxCalculationMethod;


        //确定 已有编码
        let ts = await this.service.rcjProcess.confirmCode(libraryCode, taxCalculationMethod, constructRcjArray, t);


        if (!ObjectUtils.isEmpty(t)) {
            //甲供 数组 专门处理甲供 系列
            let donorMaterialList = new Array();
            //甲供数量修改后的值
            let updateDonorMaterialNumber = 0;

            for (let constructRcjArrayElement of constructRcjArray) {

                let ifChangeMaterialCode = false;
                //根据22/12定额册寻找 五要素一样数据
                if (await this.service.rcjProcess.getRcjDataBy_22_12(constructRcjArrayElement, t)) {
                    if (!ObjectUtils.isEmpty(materialName)) {
                        constructRcjArrayElement.materialName = materialName;

                        //修改 编码
                        ifChangeMaterialCode = true;
                        await this.service.rcjProcess.changeMaterialCode(constructRcjArrayElement, ts, max);
                        //修改的子
                        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                            await this.service.rcjProcess.updateRcjDetil(constructId, singleId, unitId, constructRcjArrayElement);
                        }
                    }

                    if (!ObjectUtils.is_Undefined(specification)) {
                        if (specification != '') {
                            constructRcjArrayElement.specification = specification;
                        } else {
                            constructRcjArrayElement.specification = null;
                        }
                        //修改 编码
                        ifChangeMaterialCode = true;
                        await this.service.rcjProcess.changeMaterialCode(constructRcjArrayElement, ts, max);


                        //修改的子
                        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                            await this.service.rcjProcess.updateRcjDetil(constructId, singleId, unitId, constructRcjArrayElement);
                        }
                    }

                    if (!ObjectUtils.isEmpty(unitBj)) {
                        constructRcjArrayElement.unit = unitBj;
                        //修改 编码
                        ifChangeMaterialCode = true;
                        await this.service.rcjProcess.changeMaterialCode(constructRcjArrayElement, ts, max);

                        //修改的子
                        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                            await this.service.rcjProcess.updateRcjDetil(constructId, singleId, unitId, constructRcjArrayElement);
                        }
                    }

                    if (!ObjectUtils.isEmpty(showMarketPrice)) {
                        /*if (libraryCode.startsWith(ConstantUtil.YEAR_2022)) {
                            if (taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code) {
                                //含税
                                constructRcjArrayElement.priceMarketTax = showMarketPrice;
                            } else {
                                constructRcjArrayElement.priceMarket = showMarketPrice;
                            }
                        } else {
                            constructRcjArrayElement.marketPrice = showMarketPrice;
                        }*/
                        constructRcjArrayElement.marketPrice = showMarketPrice;

                        constructRcjArrayElement.total = NumberUtil.multiplyToString(showMarketPrice,
                            constructRcjArrayElement.totalNumber, 2);
                        constructRcjArrayElement.jieSuanPriceSource = "自行询价";
                        if (constructRcjArrayElement.marketPrice != constructRcjArrayElement.dePrice){
                            constructRcjArrayElement.sourcePrice = '自行询价';
                        }else {
                            constructRcjArrayElement.sourcePrice = null;
                        }

                        constructRcjArrayElement.highlight = null;

                        //修改的子
                        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                            let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
                            let t2 = unit.constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.rcjId);
                            await this.service.rcjProcess.parentMaterialPrice(unit, t2);
                        }
                    }

                    //类型
                    if (!ObjectUtils.isEmpty(kind)) {

                        constructRcjArrayElement.kind = kind;

                        //材料
                        if (kind == 2 || kind == 5) {
                            constructRcjArrayElement.taxRemoval = constructRcjArrayElement.taxRemovalBackUp;
                            //设备
                        } else if (kind == 4) {
                            constructRcjArrayElement.taxRemoval = ConstructProjectRcjService.sbfTaxRemoval;
                        }

                        ifChangeMaterialCode = true;
                        await this.service.rcjProcess.changeMaterialCode(constructRcjArrayElement, ts, max);

                        //修改的子
                        if (!constructRcjArrayElement.hasOwnProperty('levelMark')) {
                            await this.service.rcjProcess.updateRcjDetil(constructId, singleId, unitId, constructRcjArrayElement);
                        }

                    }
                    //是否是暂估(0:不是，1：是)
                    if (!ObjectUtils.isEmpty(ifProvisionalEstimate)) {
                        constructRcjArrayElement.ifProvisionalEstimate = ifProvisionalEstimate;
                    }
                    //是否是甲供(0:不是，1：是 2:甲定)
                    if (!ObjectUtils.isEmpty(ifDonorMaterial)) {
                        constructRcjArrayElement.ifDonorMaterial = ifDonorMaterial;
                        if (ifDonorMaterial == 0 || ifDonorMaterial == 2) {
                            //不是甲供 或者 甲定 数量为null
                            constructRcjArrayElement.donorMaterialNumber = null;
                            constructRcjArrayElement.donorMaterialNumberManage = null;
                        } else if (ifDonorMaterial == 1) {
                            //甲供 数量等于 人材机数量
                            constructRcjArrayElement.donorMaterialNumber = constructRcjArrayElement.totalNumber;
                            constructRcjArrayElement.donorMaterialNumberManage = -1;
                        }
                    }
                    //是否锁定材料价格(0：否  1：是)
                    if (!ObjectUtils.isEmpty(ifLockStandardPrice)) {
                        constructRcjArrayElement.ifLockStandardPrice = ifLockStandardPrice;
                    }
                    //是否汇总(解析) 1代表解析 0代表不解决  默认是1 解析
                    if (!ObjectUtils.isEmpty(markSum)) {
                        constructRcjArrayElement.markSum = markSum;
                        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
                        await this.service.rcjProcess.changeMarkSum(unit, constructRcjArrayElement);
                    }
                    //甲供数量
                    if (!ObjectUtils.is_Undefined(donorMaterialNumber)) {
                        //constructRcjArrayElement.donorMaterialNumber = donorMaterialNumber;

                        if (donorMaterialNumber === '') {
                            constructRcjArrayElement.ifDonorMaterial = 0;//是否是甲供(0:不是，1：是)
                            constructRcjArrayElement.donorMaterialNumber = null;
                            constructRcjArrayElement.donorMaterialNumberManage = null;
                        } else {
                            constructRcjArrayElement.ifDonorMaterial = 1;
                            updateDonorMaterialNumber = donorMaterialNumber;
                            if (donorMaterialNumber == constructRcjArrayElement.totalNumber){
                                constructRcjArrayElement.donorMaterialNumberManage = -1;
                            }else {
                                constructRcjArrayElement.donorMaterialNumberManage = donorMaterialNumber;
                            }
                            //暂存 符合甲供的人材机
                            donorMaterialList.push(constructRcjArrayElement);
                        }

                    }
                    //产地
                    if (!ObjectUtils.is_Undefined(producer)) {
                        constructRcjArrayElement.producer = producer;
                    }
                    //厂家
                    if (!ObjectUtils.is_Undefined(manufactor)) {
                        constructRcjArrayElement.manufactor = manufactor;
                    }
                    //品牌
                    if (!ObjectUtils.is_Undefined(brand)) {
                        constructRcjArrayElement.brand = brand;
                    }
                    //送达地点
                    if (!ObjectUtils.is_Undefined(deliveryLocation)) {
                        constructRcjArrayElement.deliveryLocation = deliveryLocation;
                    }
                    //质量等级
                    if (!ObjectUtils.is_Undefined(qualityGrade)) {
                        constructRcjArrayElement.qualityGrade = qualityGrade;
                    }

                    //主材表 输出报表
                    if (!ObjectUtils.isEmpty(output)) {
                        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

                        //勾选输出报表
                        if (output ==1){
                            let cancelOutputMaterialCodeList = unit.mainMaterialSetting.cancelOutputMaterialCodeList;
                            if (!ObjectUtils.isEmpty(cancelOutputMaterialCodeList)){
                                //移除
                                cancelOutputMaterialCodeList = cancelOutputMaterialCodeList.filter(i=>i!= constructRcjArrayElement.materialCode)
                            }

                        }else if (output ==0) {
                            //取消输出报表
                            let cancelOutputMaterialCodeList = unit.mainMaterialSetting.cancelOutputMaterialCodeList;
                            //添加
                            if(ObjectUtils.isEmpty(cancelOutputMaterialCodeList)){
                                let array1 = new Array();
                                array1.push(constructRcjArrayElement.materialCode);
                                unit.mainMaterialSetting.cancelOutputMaterialCodeList = array1;
                            }else {
                                if (!cancelOutputMaterialCodeList.includes(constructRcjArrayElement.materialCode)){
                                    cancelOutputMaterialCodeList.push(constructRcjArrayElement.materialCode);
                                }

                            }
                        }


                    }


                    //修改市场价
                    /*if (ifChangeMaterialCode) {
                        await this.service.rcjProcess.marketPriceAlreadyHaveRcj(constructRcjArrayElement, constructRcjArray, null);
                    }*/

                    //******************************结算*****************************

                    //保管费率
                    if (!ObjectUtils.is_Undefined(jieSuanAdminRate)) {
                        constructRcjArrayElement.jieSuanAdminRate = jieSuanAdminRate;
                    }
                    //是否调差
                    if (!ObjectUtils.is_Undefined(isDifference)) {
                        constructRcjArrayElement.isDifference = isDifference;
                    }
                    //备注
                    if (!ObjectUtils.is_Undefined(description)) {
                        constructRcjArrayElement.description = description;
                    }
                    //风险幅度
                    if (!ObjectUtils.is_Undefined(riskAmplitudeRangeMin) && !ObjectUtils.is_Undefined(riskAmplitudeRangeMax)) {
                        constructRcjArrayElement.riskAmplitudeRangeMin = riskAmplitudeRangeMin;
                        constructRcjArrayElement.riskAmplitudeRangeMax = riskAmplitudeRangeMax;
                    }

                    //取费
                    if (!ObjectUtils.is_Undefined(jieSuanFee)) {
                        constructRcjArrayElement.jieSuanFee = jieSuanFee;
                    }

                    //合同价
                    if (!ObjectUtils.is_Undefined(jieSuanMarketPrice)) {
                        constructRcjArrayElement.jieSuanMarketPrice = jieSuanMarketPrice;
                    }
                    //基期价
                    if (!ObjectUtils.is_Undefined(jieSuanBasePrice)) {
                        //获取材料数据
                        //let constructProjectRcj1 = this._findRcjById(constructId,singleId,unitId,sequenceNbr);
                        /*let rcjDifferenceInfo = constructRcjArrayElement.jieSuanRcjDifferenceTypeList.filter(f => f.rcjDifferenceType == rcjDifferenceType)[0];
                        rcjDifferenceInfo.jieSuanBasePrice=jieSuanBasePrice;*/
                        //constructRcjArrayElement.jieSuanRcjDifferenceTypeList = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;

                        if (!ObjectUtil.isEmpty(constructRcjArrayElement.jieSuanRcjDifferenceTypeList)){
                            let jieSuanRcjDifferenceTypeList1 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;
                            for (let rcjDifferenceInfo of jieSuanRcjDifferenceTypeList1) {
                                rcjDifferenceInfo.jieSuanBasePrice=jieSuanBasePrice;
                                rcjDifferenceInfo.jieSuanBasePriceSource="自行询价";
                            }
                        }
                    }
                    // //基期价来源
                    if (!ObjectUtils.is_Undefined(jieSuanBasePriceSource)) {
                        //constructRcjArrayElement.jieSuanBasePriceSource = jieSuanBasePriceSource;

                        if (!ObjectUtil.isEmpty(constructRcjArrayElement.jieSuanRcjDifferenceTypeList)){
                            let jieSuanRcjDifferenceTypeList1 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;
                            for (let rcjDifferenceInfo of jieSuanRcjDifferenceTypeList1) {
                                rcjDifferenceInfo.jieSuanBasePriceSource=jieSuanBasePriceSource;
                            }
                        }
                    }
                    //涨跌幅
                    if (!ObjectUtils.is_Undefined(jieSuanPriceLimit)) {
                        constructRcjArrayElement.jieSuanPriceLimit = jieSuanPriceLimit;
                    }

                    //结算除税系数
                    if (!ObjectUtils.is_Undefined(jieSuanTaxRemoval)) {
                        constructRcjArrayElement.jieSuanTaxRemoval = jieSuanTaxRemoval;
                    }
                    //结算单位价差
                    if (!ObjectUtils.is_Undefined(jieSuanPriceDifferenc)) {
                        constructRcjArrayElement.jieSuanPriceDifferenc = jieSuanPriceDifferenc;
                    }


                    //单位价差集合
                    if (!ObjectUtils.is_Undefined(priceDifferenc)) {
                        constructRcjArrayElement.jieSuanUnitPriceDifferencList = jieSuanUnitPriceDifferencList;
                    }

                    //基本价格指数F0
                    if (!ObjectUtils.is_Undefined(jieSuanBasePriceF0)) {
                        if (ObjectUtils.isEmpty(fqNum)){
                            constructRcjArrayElement.jieSuanBasePriceF0 = jieSuanBasePriceF0;
                        }else {
                            let jieSuanBasePriceF0Map = constructRcjArrayElement.jieSuanBasePriceF0Map;
                            if (ObjectUtils.isEmpty(jieSuanBasePriceF0Map)){
                                let f0Map = new Map();
                                f0Map.set(fqNum,jieSuanBasePriceF0);
                                constructRcjArrayElement.jieSuanBasePriceF0Map =f0Map;
                            }else {
                                jieSuanBasePriceF0Map.set(fqNum,jieSuanBasePriceF0);
                            }
                        }

                    }
                    //现行价格指数Ft
                    if (!ObjectUtils.is_Undefined(jieSuanCurrentPriceF0)) {

                        if (ObjectUtils.isEmpty(fqNum)){
                            constructRcjArrayElement.jieSuanCurrentPriceF0 = jieSuanCurrentPriceF0;
                        }else {
                            let jieSuanCurrentPriceFtMap = constructRcjArrayElement.jieSuanCurrentPriceFtMap;
                            if (ObjectUtils.isEmpty(jieSuanCurrentPriceFtMap)){
                                let f0Map = new Map();
                                f0Map.set(fqNum,jieSuanCurrentPriceF0);
                                constructRcjArrayElement.jieSuanCurrentPriceFtMap =f0Map;
                            }else {
                                jieSuanCurrentPriceFtMap.set(fqNum,jieSuanCurrentPriceF0);
                            }
                        }
                    }

                    //结算单价
                    if (!ObjectUtils.is_Undefined(jieSuanPrice)) {
                        //获取材料数据
                        //let constructProjectRcj1 = this._findRcjById(constructId,singleId,unitId,sequenceNbr);
                        //let rcjDifferenceInfo = constructRcjArrayElement.jieSuanRcjDifferenceTypeList.filter(f => f.rcjDifferenceType == rcjDifferenceType)[0];
                        let index;
                        if(ObjectUtils.isEmpty(fqNum)){
                            index=0;
                        }else {
                            index=fqNum-1;
                        }
                        if (!ObjectUtil.isEmpty(constructRcjArrayElement.jieSuanRcjDifferenceTypeList)){
                            let jieSuanRcjDifferenceTypeList1 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;
                            for (let rcjDifferenceInfo of jieSuanRcjDifferenceTypeList1) {
                                rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPrice=jieSuanPrice;
                                rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPriceSource="自行询价";
                            }
                        }

                        //constructRcjArrayElement.jieSuanRcjDifferenceTypeList = constructProjectRcj1.jieSuanRcjDifferenceTypeList;
                    }


                    //结算单价来源
                    if (!ObjectUtils.is_Undefined(jieSuanPriceSource)) {
                        //获取材料数据
                        //let constructProjectRcj1 = this._findRcjById(constructId,singleId,unitId,sequenceNbr);
                        //let rcjDifferenceInfo = constructRcjArrayElement.jieSuanRcjDifferenceTypeList.filter(f => f.rcjDifferenceType == rcjDifferenceType)[0];
                        let index;
                        if(ObjectUtils.isEmpty(fqNum)){
                            index=0;
                        }else {
                            index=fqNum-1;
                        }
                        if (!ObjectUtil.isEmpty(constructRcjArrayElement.jieSuanRcjDifferenceTypeList)){
                            let jieSuanRcjDifferenceTypeList1 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;
                            for (let rcjDifferenceInfo of jieSuanRcjDifferenceTypeList1) {
                                rcjDifferenceInfo.jieSuanDifferencePriceList[index].jieSuanPriceSource=jieSuanPriceSource;
                            }
                        }

                        //constructRcjArrayElement.jieSuanRcjDifferenceTypeList = constructProjectRcj1.jieSuanRcjDifferenceTypeList;
                    }
                    //结算第n期除税系数
                    if (!ObjectUtils.is_Undefined(jieSuanStagetaxRemoval)) {
                        constructRcjArrayElement.jieSuanStagetaxRemovalList[fqNum - 1] = jieSuanStagetaxRemoval;
                    }

                    //是否载价
                    if (!ObjectUtils.is_Undefined(highlight)) {
                        constructRcjArrayElement.highlight = highlight;
                    }

                    //结算第n期单价
                    if (!ObjectUtils.is_Undefined(jieSuanStagePrice)) {

                        if (type ==2){
                            //let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
                            //let item = unit.unitRcjDifference.find(k => k.kind == rcjDifferenceType);
                            //let rcjDifference = constructRcjArrayElement.jieSuanRcjDifferenceTypeList.find(k => k.rcjDifferenceType == item.rcjDifferenceType);

                            let jieSuanRcjDifferenceTypeList2 = constructRcjArrayElement.jieSuanRcjDifferenceTypeList;
                            for (let jieSuanRcjDifferenceTypeList2Element of jieSuanRcjDifferenceTypeList2) {
                                jieSuanRcjDifferenceTypeList2Element.jieSuanDifferencePriceList[fqNum - 1].jieSuanPrice = jieSuanStagePrice;
                                jieSuanRcjDifferenceTypeList2Element.jieSuanDifferencePriceList[fqNum - 1].jieSuanPriceSource = "自行询价";
                            }

                        }

                    }
                    /*if (!ObjectUtils.is_Undefined(sourcePrice)) {
                        constructRcjArrayElement.sourcePrice = sourcePrice;
                    }
                    if (!ObjectUtils.is_Undefined(jieSuanBasePriceList)) {
                        constructRcjArrayElement.jieSuanBasePriceList = jieSuanBasePriceList;
                    }
                    if (!ObjectUtils.is_Undefined(jieSuanBasePriceSourceList)) {
                        constructRcjArrayElement.jieSuanBasePriceSourceList = jieSuanBasePriceSourceList;
                    }
                    if (!ObjectUtils.is_Undefined(jieSuanRcjStagePrice)) {
                        constructRcjArrayElement.jieSuanRcjStagePrice = jieSuanRcjStagePrice;
                    }
                    if (!ObjectUtils.is_Undefined(jieSuanStagePriceSourceList)) {
                        constructRcjArrayElement.jieSuanStagePriceSourceList = jieSuanStagePriceSourceList;
                    }
                    if (!ObjectUtils.is_Undefined(highlight)) {
                        constructRcjArrayElement.highlight = highlight;
                    }
                    if (!ObjectUtils.is_Undefined(highlightList)) {
                        constructRcjArrayElement.highlightList = highlightList;
                    }*/

                    // 调整法价格集合
                    if (!ObjectUtils.is_Undefined(jieSuanRcjDifferenceTypeList)) {
                        constructRcjArrayElement.jieSuanRcjDifferenceTypeList = jieSuanRcjDifferenceTypeList;
                    }

                    // 备注
                    if (!ObjectUtils.is_Undefined(remark)) {
                        constructRcjArrayElement.remark = remark;
                    }

                    // 备注
                    if (!ObjectUtils.is_Undefined(outputToken)) {
                        constructRcjArrayElement.outputToken = outputToken;
                    }

                    //调用zxz后续逻辑

                    /*if (constructRcjArrayElement.hasOwnProperty('levelMark')) {
                        //修改父级
                        /!**
                         * * rcjFive ： {
                         *     type:XXXX,
                         *     materialName: XXXX,
                         *     specification: XXXXXX,
                         *     unit: XXXXX,
                         *     dePrice: XXXXX
                         * }

                         *!/
                        let constructProjectRcj = new ConstructProjectRcj();

                        constructProjectRcj.type = constructRcjArrayElement.type;
                        constructProjectRcj.materialName = constructRcjArrayElement.materialName;
                        constructProjectRcj.specification = constructRcjArrayElement.specification;
                        constructProjectRcj.unit = constructRcjArrayElement.unit;
                        constructProjectRcj.dePrice = constructRcjArrayElement.dePrice;
                        this.service.unitPriceService.caculateDeByRcj(constructId, singleId, unitId, constructRcjArrayElement,ifChangeMaterialCode);
                    } else {
                        //修改子级
                        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
                        let t = unit.constructProjectRcjs.find(i => i.sequenceNbr === constructRcjArrayElement.rcjId);
                        let constructProjectRcj = new ConstructProjectRcj();
                        constructProjectRcj.type = t.type;
                        constructProjectRcj.materialName = t.materialName;
                        constructProjectRcj.specification = t.specification;
                        constructProjectRcj.unit = t.unit;
                        constructProjectRcj.dePrice = t.dePrice;
                        this.service.unitPriceService.caculateDeByRcj(constructId, singleId, unitId, constructRcjArrayElement,ifChangeMaterialCode);
                    }*/

                }
            }
            //判断是否触发甲供数量修改问题
            if (!ObjectUtils.isEmpty(donorMaterialList)) {
                //甲供数量修改
                await this.service.rcjProcess.donorMaterialNumberUpdate(donorMaterialList, updateDonorMaterialNumber);
            }
        }

    }



    //费用汇总 获取 材料价差 计算
    async getFyhzJc(constructId, singleId, unitId,clType){
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (ObjectUtils.isEmpty(unit)){
            return [];
        }

        if (ObjectUtils.isEmpty(clType) || clType ==0){
            return [];
        }

        let methodType ;
        let rcjDifference = unit.rcjDifference;
        // let rcjDifferenceSetVos = rcjDifference.filter(obj => obj.kind == clType);
        for (const obj of rcjDifference) {
            if (obj.kind == clType) {
                methodType = obj.rcjDifferenceType;
                break;
            }
        }
        if (ObjectUtils.isEmpty(methodType)){
            methodType = 1;
        }
        let args = {
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            clType: clType,
            methodType:methodType
        };

        //设置
        this.priceDifferenceAdjustmentMethod(args);


        let rcjList ={
            constructId: constructId,
            singleId: singleId,
            unitId: unitId,
            kind:clType
        };
        let unitRcjQuery = await this.service.jieSuanProject.jieSuanRcjProcess.unitRcjQuery(rcjList);
        if (ObjectUtil.isEmpty(unitRcjQuery)){
            return []
        }
        return unitRcjQuery;

    }


    _findRcjById(constructId, singleId, unitId, rcjId) {
        let rcjArray = PricingFileFindUtils.getRcjList(constructId, singleId, unitId).filter(f => f.sequenceNbr === rcjId);
        return rcjArray[0];
    }
}

JieSuanRcjStageService.toString = () => '[class JieSuanRcjStageService]';
module.exports = JieSuanRcjStageService;
