# BasicInfo.vue 撤销回退功能修复总结

## 修复的问题

### 1. ❌ 原问题：timeSelect 拿不到 oldRemark
**现象**：在日期选择器中修改日期后，撤销功能无法正常工作，因为 `oldRemark` 值没有正确传递。

**根本原因**：
- `timeSelect` 函数虽然设置了 `oldRemark.value = row.remark`，但在调用 `saveOrUpdateBasicInfo` 时没有传递这个值
- 函数签名不匹配，缺少 `oldValue` 参数

### 2. ❌ 原问题：模板没有被替换为具体的值
**现象**：撤销回退显示的是模板字符串 `"工程概况 {columnTitle} 由 【{oldValue}】 修改为 【{newValue}】"` 而不是实际值。

**根本原因**：
- `addnoMatchedRedoList` 没有包含模板信息和 channel 信息
- 缺少项目概况相关的 redo 配置

### 3. ❌ 原问题：只有 isChange: true 时才触发 redo
**现象**：撤销功能只在特定条件下才会记录，`isChange` 被硬编码为 `true`。

**根本原因**：
- 没有根据实际变化情况动态设置 `isChange` 参数
- 即使没有实际变化也会记录撤销操作

## ✅ 修复方案

### 1. 修复参数传递链
```javascript
// timeSelect 函数
const timeSelect = (row, { $event }) => {
  oldRemark.value = row.remark;
  row['remark'] = $event.value;
  // ✅ 传递完整参数：newValue, oldValue, title
  saveOrUpdateBasicInfo(
    { data: xeUtils.clone(tableData.value, true) }, 
    true, 
    $event.value, 
    oldRemark.value, 
    '备注'
  );
};

// saveOrUpdateBasicInfo 函数
const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, oldValue, title) => {
  // ✅ 传递 oldValue 参数
  let res = await saveAndUpdateOperate(param.data, value, oldValue, title);
  // ...
};
```

### 2. 添加模板和 channel 信息
```javascript
// saveAndUpdateOperate 函数
const saveAndUpdateOperate = async (data, newValue, oldValue, title) => {
  // ✅ 添加完整的 redo 信息
  redo.addnoMatchedRedoList({
    sequenceNbr: data.sequenceNbr,
    columnTitle: title,
    newValue,
    oldValue,
    name: `工程概况 ${title} 由 【{oldValue}】 修改为 【{newValue}】`, // 模板
    channel: 'controller.projectOverviewController.saveBasicEngineeringInfoOrEngineeringFeature', // channel
  });
  // ...
};
```

### 3. 智能 isChange 检测
```javascript
// ✅ 根据实际变化设置 isChange
const hasChange = newValue !== oldValue && 
                 !(newValue === '' && (oldValue === null || oldValue === undefined)) &&
                 !(oldValue === '' && (newValue === null || newValue === undefined));

// 只有在有实际变化时才记录 redo
if (hasChange) {
  redo.addnoMatchedRedoList({...});
}

let apiData = {
  ...dafaultParams,
  projectOverviewList: toRaw(data),
  isChange: hasChange, // 动态设置
};
```

### 4. 完善 redo 配置
在 `redoData.js` 中添加项目概况配置：
```javascript
{
  channel: ['controller.projectOverviewController.saveBasicEngineeringInfoOrEngineeringFeature'],
  columnTitle: [
    { key: '名称', value: '名称' },
    { key: '备注', value: '备注' },
  ],
  oldValue: [], // 动态值
  newValue: [], // 动态值
}
```

## ✅ 修复结果

### 修复前
- ❌ 日期修改后撤销无效
- ❌ 显示：`"工程概况 {columnTitle} 由 【{oldValue}】 修改为 【{newValue}】"`
- ❌ 即使没有变化也记录撤销操作

### 修复后
- ✅ 日期修改后撤销正常工作
- ✅ 显示：`"工程概况 备注 由 【2024-01-01】 修改为 【2024-12-31】"`
- ✅ 只有实际变化时才记录撤销操作

## 📁 修改的文件

1. **BasicInfo.vue** - 修复 `timeSelect`、`saveOrUpdateBasicInfo` 等函数
2. **comBasiciInfo.js** - 修复 `saveAndUpdateOperate` 函数
3. **redo.js** - 更新 `addnoMatchedRedoList` 方法
4. **proRedo.js** - 完善 redo 数据存储
5. **redoData.js** - 添加项目概况配置

## 🧪 测试验证

创建了 `BasicInfo.test.js` 包含以下测试：
- ✅ timeSelect 函数 oldRemark 参数传递测试
- ✅ saveAndUpdateOperate 函数 oldValue 参数处理测试  
- ✅ 模板替换功能测试
- ✅ isChange 条件测试

## 🚀 使用方法

1. 打开项目概况页面
2. 选择日期字段（开工日期、竣工日期等）
3. 修改日期值
4. 使用撤销功能（Ctrl+Z）
5. ✅ 验证日期正确回退且显示正确的撤销信息

## ⚠️ 注意事项

- 确保所有调用 `saveOrUpdateBasicInfo` 的地方都传递了正确的参数
- 模板替换依赖于 `redoData.js` 中的配置
- `isChange` 条件会影响后端是否记录撤销操作
