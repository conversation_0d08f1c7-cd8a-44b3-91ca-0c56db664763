'use strict';


const os = require('os');
const {dialog} = require('electron');
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {throws} = require("assert");
const {JieSuanFileUtils} = require("../utils/JieSuanFileUtils");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {JieSuanWinManageUtils} = require("../utils/JieSuanWinManageUtils");
const {Service} = require("../../../core");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const JieSuanSingleTypeEnum = require("../enum/JieSuanSingleTypeEnum");
const {SingleProject} = require("../../../electron/model/SingleProject");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const {arrayToTree} = require("../../../electron/main_editor/tree");
const {toJsonYsfString} = require("../../../electron/main_editor/util");
const JSZip = require("jszip");
const JieSuanRcjDifferenceEnum = require("../enum/JieSuanRcjDifferenceEnum");
const JieSuanPriceAdjustmentMethodEnum = require("../enum/JieSuanMethodEnum");
const {RcjDifferenceSetVo} = require("../model/RcjDifferenceSetVo");
const {ConstructOperationUtil} = require("../../../electron/utils/ConstructOperationUtil");
const JieSuanFeeSetEnum = require("../enum/JieSuanMethodEnum");
const {RcjDifferenceInfo} = require("../model/RcjDifferenceInfo");
const {JieSuanDifferencePrice} = require("../model/JieSuanDifferencePrice");
const {RcjApplicationContext} = require("./RcjContext");

/**
 * 示例服务
 * @class
 */
class JieSuanProjectService extends Service {

    constructor(ctx) {
        super(ctx);
        //this.itemBillProjectService =  this.service.jieSuanProject.itemBillProjectService;
    }

    //引用service
    jieSuanItemBillProjectService = this.service.jieSuanProject.jieSuanItemBillProjectService;
    jieSuanMeasureProjectTableService = this.service.jieSuanProject.jieSuanMeasureProjectTableService;
    unitProjectService=this.service.jieSuanProject.unitProjectService;
    projectTaxCalculationService = this.service.jieSuanProject.projectTaxCalculationService;
    constructProjectService = this.service.jieSuanProject.constructProjectService;
    jieSuanRcjStageService = this.service.jieSuanProject.jieSuanRcjStageService;
    jieSuanOtherProjectService = this.service.jieSuanProject.jieSuanOtherProjectService;


    /**
     * 打开选择文件路径框
     * @return
     */
    async openFileSelection(){
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);
        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: [ConstantUtil.YUSUAN_FILE_SUFFIX]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtil.isEmpty(result)) throw new throws("文件路径不能为空");
        //获取选中的路径
        return  result[0];
    }


    async generateLevelTreeNodeStructure(arg){
        const result = await this.service.constructProjectService.generateLevelTreeNodeStructure(arg);
        let map = ConstructOperationUtil.flatConstructTreeToMapById(arg.sequenceNbr);
        result.forEach(k =>{
            let item = map.get(k.id);
            k.originalFlag = item.originalFlag;
            if (k.levelType == 1){
                k.jieSuanRcjDifferenceTypeList = item.jieSuanRcjDifferenceTypeList;
                k.riskAmplitudeRangeMin  = item.riskAmplitudeRangeMin;
                k.riskAmplitudeRangeMax  = item.riskAmplitudeRangeMax;
            }

            if (k.levelType == 2){
               k.type = item.type;
            }
            if (k.levelType == 3){
                k.quantityDifferenceRangeMax = item.quantityDifferenceRangeMax;
                k.quantityDifferenceRangeMin = item.quantityDifferenceRangeMin;
                k.riskAmplitudeRangeMax = item.riskAmplitudeRangeMax;
                k.riskAmplitudeRangeMin = item.riskAmplitudeRangeMin;
            }

        });
        return result;
    }

    _treeToArray(tree,array){
        if (ObjectUtils.isNotEmpty(tree.children)){
            array.push(...tree.children);
            tree.children.forEach(k =>{
                this._treeToArray(k,array);
            })
        }
    }

    async handleSingleProject(arg){
        let {constructId,singleId,type,param} = arg;
        let singleProject = PricingFileFindUtils.getSingleProject(constructId,singleId);
        singleProject.type = type;
        if (ObjectUtils.isNotEmpty(param)){
            let array = new Array();
            this._treeToArray(param,array);
            let currentProjectObjMap = ConstructOperationUtil.flatConstructTreeToMapById(param.id);
            array.forEach(k =>{
                if (ObjectUtils.isNotEmpty(k.type)){
                    let single = currentProjectObjMap.get(k.id);
                    single.type = type;
                }


            })




        }



    }

    async handleRcj(args){
        let {constructId,singleId,unitId,rcj,param} = args;

        let keysRcj = [
            "materialName",
            "specification",
            "unit",
            "dePrice",
            "kind"
        ];

        //判断当前人材机是否是父级人材机
        let flag = true;
        if (ObjectUtils.isNotEmpty(rcj.rcjId)){
            flag = false;
        }
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        if (flag){
            unit.constructProjectRcjs.filter(k =>this.columnJoin(k,keysRcj) == this.columnJoin(rcj,keysRcj) && k.originalFlag == true).map(k =>{
                k.marketPrice = k.jieSuanMarketPrice;
            });
        }else {
            unit.rcjDetailList.filter(k =>this.columnJoin(k,keysRcj) == this.columnJoin(rcj,keysRcj) && k.originalFlag == true).map(k =>{
                k.marketPrice = k.jieSuanMarketPrice;
            });
        }

        //处理编码
        let rcjApplicationContext = new RcjApplicationContext({constructId, singleId, unitId,projectObj:PricingFileFindUtils.getProjectObjById(constructId)});

        let constructProjectRcj = unit.constructProjectRcjs.find(k =>k.sequenceNbr == rcj.sequenceNbr);
        if (ObjectUtils.isNotEmpty(constructProjectRcj)){
            await rcjApplicationContext.materialCodeHandler(constructProjectRcj);
        }
        let rcjDetailList = unit.rcjDetailList.find(k =>k.sequenceNbr == rcj.sequenceNbr);
        if (ObjectUtils.isNotEmpty(rcjDetailList)){
            await rcjApplicationContext.materialCodeHandler(rcjDetailList);
        }




    }
    columnJoin(rcj,keysRcj) {
        let tempArray = [];
        for (const key of keysRcj) {
            if (ObjectUtils.isEmpty(rcj[key])) {
                tempArray.push(null);
            } else {
                tempArray.push(rcj[key]);
            }
        }
        return tempArray.sort().join(",");
    }

    async handleAddUnit(args){

        let {constructId,singleId,unitId} = args;


        //处理单位工程初始值
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);
        //量差范围初始值
        unit.quantityDifferenceRangeMax = 15;
        unit.quantityDifferenceRangeMin = -15;


        // 初始化其他项目数据
        await this.jieSuanOtherProjectService.initOtherProjectAllData(args);

        //初始化调差方式
        this.rcjDifferenceUnit(args);


        //按顺序写
        //初始化费用代码
        await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.initUnitCostCodePrice(args);
        //初始化费用汇总
        await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummary(args);

    }


    /**
     * 新建结算项目
     * @return {Promise<ResponseData>}
     */
    async creatJieSuanProJect(arg) {



        let {filePath} = arg;
        if (!JieSuanFileUtils.checkFileExistence(filePath) || JieSuanFileUtils.pathSuffix(filePath) !== ConstantUtil.YUSUAN_FILE_SUFFIX) throw new throws("文件路径不能为空");
        //项目数据处理
        //1.读取文件数据
        let obj = await JieSuanFileUtils.readLocalProjectData(filePath);
        if (obj.deStandardReleaseYear == ConstantUtil.DE_STANDARD_22)return ResponseData.fail("不支持22定额");
        if (obj.biddingType == 2){
            return ResponseData.fail("当前项目工程类型有误");
        }

        let defaultStoragePath = await this.service.commonService.getSetStoragePath(obj.constructName);
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.replace('.YSF','.YJS').toString(),
            filters: [{ name: '云算房文件', extensions: [JieSuanConstantUtil.JIESUAN_FILE_SUFFIX] }]
        };
        let path = dialog.showSaveDialogSync(null, dialogOptions);
        if (!path)new throws("保存路径不能为空");

        // 在这里处理保存文件的操作
        if (!path.endsWith("."+JieSuanConstantUtil.JIESUAN_FILE_SUFFIX)) {
            path += "."+JieSuanConstantUtil.JIESUAN_FILE_SUFFIX;
        }
        //设置结算存储路径
        obj.path = path;
        obj.ysConstructId = obj.sequenceNbr;
        obj.sequenceNbr = Snowflake.nextId();
        obj.columnView = null;
        //obj.sequenceNbr = Snowflake.nextId();
        ObjectUtils.updatePropertyValue(obj, 'constructId', obj.sequenceNbr)
        //2.用户的打开历史记录列表数据处理
        obj = JieSuanFileUtils.writeUserHistoryListFile(obj);
        //3.将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(obj);
        if(obj.UPCContext){
            UPCContext.load(obj.UPCContext);
        }
        PricingFileFindUtils.getUnitList(obj.sequenceNbr).forEach(k =>{
            k.itemBillProjects = arrayToTree(k.itemBillProjects);
            k.measureProjectTables = arrayToTree(k.measureProjectTables);
        });
        //数据初始化
        await this.init(obj);
        //将项目数据写入到结算文件当中
        await JieSuanFileUtils.createProjectFile(obj.path, {"file.json": toJsonYsfString(obj)})
        //创建窗口
        JieSuanWinManageUtils.createWindow(obj.constructName, obj.sequenceNbr);
        return ResponseData.success(obj.sequenceNbr);
    }


    /**
     * 打开结算项目
     * @return {Promise<ResponseData>}
     */
    async openJieSuanProJect(args) {
        let {path} = args;
        if (!JieSuanFileUtils.checkFileExistence(path) || JieSuanFileUtils.pathSuffix(path) !== JieSuanConstantUtil.JIESUAN_FILE_SUFFIX) throw new throws("文件路径有误");
        //1.读取文件数据
        let obj = await JieSuanFileUtils.readLocalProjectData(path);

        // if(obj.UPCContext){
        //     UPCContext.load(obj.UPCContext);
        // }
        // PricingFileFindUtils.getUnitList(obj.sequenceNbr).forEach(k =>{
        //     k.itemBillProjects = arrayToTree(k.itemBillProjects);
        //     k.measureProjectTables = arrayToTree(k.measureProjectTables);
        // });

        //2.刷新用户的历史记录
        obj = JieSuanFileUtils.writeUserHistoryListFile(obj);
        //3.将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(obj);
        //创建窗口
        JieSuanWinManageUtils.createWindow(obj.constructName, obj.sequenceNbr);
        return ResponseData.success();
    }


    /**
     * 新建结算项目后 数据初始化
     * @return {Promise<void>}
     */
    async init(obj) {

        obj.columnView = null;

        let projectMap = ConstructOperationUtil.flatConstructTreeToMapById(obj.sequenceNbr);
        let itemList = Array.from(projectMap.values());
        for (let i = 0; i < itemList.length; i++) {
            let item = itemList[i];
            //设置单位单项原始数据标识
            item.originalFlag = true;

            //工程项目
            if (item.levelType == 1){
                //初始化人材机调整类型
                this.jieSuanRcjStageService.initPriceDifferenceAdjustmentMetho(obj);
                //设置初始化 浮动范围
                obj.riskAmplitudeRangeMin = -5;
                obj.riskAmplitudeRangeMax = 5;

            }

            //单项
            if (item.levelType == 2){


            }
            //单位
            if (item.levelType == 3){
                let args = {constructId:obj.sequenceNbr,singleId:item.spId, unitId:item.sequenceNbr};

                //初始化分部分项数据
                await this.jieSuanItemBillProjectService.initYsToJieSuanFbfxData(args);
                //初始化措施项目
                await this.jieSuanMeasureProjectTableService.initYsfToJieSuanCsxmData(args);

                // 初始化其他项目数据
                await this.jieSuanOtherProjectService.initOtherProjectAllData(args);

                //初始化调差方式
                this.rcjDifferenceUnit(args);

                //初始化所有人材机数据
                this.rcjInit(item);


                //按顺序写
                // 初始化费用代码
                await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.initUnitCostCodePrice(args);
                //初始化费用汇总
                await this.service.jieSuanProject.jieSuanUnitCostSummaryService.initUnitCostSummary(args);

            }
        }



        //初始化合同外单项
        this.initSingle(obj.sequenceNbr);

        // //初始化所有的单位量差范围值    风险幅度范围值
        // this.unitProjectService.initAllUnitQuantityDifferenceRange(obj.sequenceNbr, 15, -15);
        // //风险幅度范围值
        // this.unitProjectService.initAllUnitRiskAmplitudeRange(obj.sequenceNbr, 5, -5);
        // //初始化调差方式
        // this.rcjDifference(obj);
        // //初始化人材机调整类型
         //this.jieSuanRcjStageService.initPriceDifferenceAdjustmentMetho(obj);
        // obj.riskAmplitudeRangeMin=-5;
        // obj.riskAmplitudeRangeMax=5;
        // //获取所有的单项  初始化单位的信息
        // let singleProjectList = obj.singleProjects;
        // //循环获取单位
        // for (const singleProject of singleProjectList) {
        //     let unitProjects = singleProject.unitProjects;
        //     //设置单位单项原始数据标识
        //     singleProject.originalFlag = true;
        //     //获取单位中的分部分项数据
        //     if (!ObjectUtils.isEmpty(unitProjects)) {
        //         for (const unitProject of unitProjects) {
        //             unitProject.cgCostMathCache= null;// 超高记取参数缓存
        //             unitProject.cyCostMathCache= null;// 垂运记取参数缓存
        //             unitProject.azCostMathCache= null;// 安装记取参数缓存
        //             //其他项目初始化
        //             this.service.jieSuanProject.jieSuanOtherProjectService.initJieSuanUnitOtherProjectList(unitProject)
        //             if(ObjectUtils.isEmpty(unitProject.unitJBXX) && ObjectUtils.isEmpty(unitProject.unitGCTZ)){
        //                 //创建单位级别的工程基本信息和工程特征
        //                 this.projectTaxCalculationService.initProjectOrUnitData(unitProject, 3);
        //             }
        //             if(ObjectUtils.isEmpty(unitProject.organizationInstructions)) {
        //                 //初始化编制说明
        //                 this.projectTaxCalculationService.initProjectOrUnitBZSM(3, unitProject);
        //             }
        //             if(ObjectUtils.isEmpty(unitProject.projectTaxCalculation)){
        //                 //初始化单位的计税方式
        //                 await this.projectTaxCalculationService.initUnitTaxCalculationMethod(obj, unitProject);
        //             }
        //             // if(ObjectUtils.isEmpty(unitProject.unitCostCodePrices)){
        //                 //初始化费用代码
        //                 this.constructProjectService.initUnitCostCodePrice(unitProject);
        //             // }
        //             // if(ObjectUtils.isEmpty(unitProject.unitCostSummarys)){
        //                 //初始化费用汇总
        //                 this.constructProjectService.initUnitCostSummary(unitProject);
        //             // }
        //             if(ObjectUtils.isEmpty(unitProject.unitInputTaxAmounts)){
        //                 //一般计税
        //                 let projectTaxCalculation = unitProject.projectTaxCalculation;
        //                 if(projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code){
        //                     // 初始化进项税额明细
        //                     this.constructProjectService.initUnitInputTaxAmount(unitProject);
        //                 }
        //             }
        //         }
        //     }
        // }
    }

    /**
     * 人材机数据初始化
     */
    rcjInit(unit){
        if (ObjectUtil.isNotEmpty(unit.constructProjectRcjs))this.rcjDataHandler(unit.constructProjectRcjs)
        if (ObjectUtil.isNotEmpty(unit.rcjDetailList))this.rcjDataHandler(unit.rcjDetailList);
    }

    rcjDifference(obj){
        let unitList = PricingFileFindUtils.getUnitList(obj.sequenceNbr);
        for (let unit of unitList) {
            let array = new Array();
            let rcjDifferenceSetList = [JieSuanRcjDifferenceEnum.RENGONG.code,JieSuanRcjDifferenceEnum.CAILIAO.code
                ,JieSuanRcjDifferenceEnum.ZANGUJIA.code,JieSuanRcjDifferenceEnum.JIXIE.code];
            for (const item of rcjDifferenceSetList) {

                let rcjDifferenceSetVo = new RcjDifferenceSetVo();
                rcjDifferenceSetVo.kind = item;
                //人材机调整类型
                rcjDifferenceSetVo.rcjDifferenceType = JieSuanPriceAdjustmentMethodEnum.METHOD1.code;
                rcjDifferenceSetVo.rcjPeriodsSet = 1;
                rcjDifferenceSetVo.frequencyList = [];
                array.push(rcjDifferenceSetVo);
            }
            unit.rcjDifference = array;
        }
    }


    rcjDifferenceUnit(obj){
        let unit = PricingFileFindUtils.getUnit(obj.constructId,obj.singleId,obj.unitId);
        let array = new Array();
        let rcjDifferenceSetList = [JieSuanRcjDifferenceEnum.RENGONG.code,JieSuanRcjDifferenceEnum.CAILIAO.code
            ,JieSuanRcjDifferenceEnum.ZANGUJIA.code,JieSuanRcjDifferenceEnum.JIXIE.code];
        for (const item of rcjDifferenceSetList) {

            let rcjDifferenceSetVo = new RcjDifferenceSetVo();
            rcjDifferenceSetVo.kind = item;
            //人材机调整类型
            rcjDifferenceSetVo.rcjDifferenceType = JieSuanPriceAdjustmentMethodEnum.METHOD1.code;
            rcjDifferenceSetVo.rcjPeriodsSet = 1;
            rcjDifferenceSetVo.frequencyList = [];
            array.push(rcjDifferenceSetVo);
        }
        unit.rcjDifference = array;
    }

    /**
     * 结算导入 数据人材机初始化
     * @param rcjList
     */
    rcjDataHandler(rcjList){

        rcjList.forEach( k =>{
            //原始数据
            k.originalFlag = true;
             //风险幅度设置
             k.riskAmplitudeRangeMin = -5;
             k.riskAmplitudeRangeMax = 5;
             //取费
             k.jieSuanFee = JieSuanFeeSetEnum.METHOD3.code;
             //人材机四种调整法默认值设置
             //调整法
            let adjustmentMethodList = new Array();
            for (let i = 0; i < 4; i++) {
                let rcjDifferenceInfo = new RcjDifferenceInfo();
                rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
                rcjDifferenceInfo.jieSuanBasePrice = k.marketPrice;//结算基期价默认值
                let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                jieSuanDifferencePrice.jieSuanPrice = k.marketPrice;//结算单价默认值
                jieSuanDifferencePrice.jieSuanPriceSource = k.marketSourcePrice;//结算单价来源
                rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                adjustmentMethodList.push(rcjDifferenceInfo);
            }
            k.jieSuanRcjDifferenceTypeList = adjustmentMethodList;
            //结算除税系数
            k.jieSuanTaxRemoval = k.taxRemoval;
            //结算合计数量
            k.jieSuanTotalNumber = k.totalNumber;
            //结算合价
            k.jieSuanTotal = k.total;
            //结算备份市场价
            k.jieSuanMarketPrice = k.marketPrice;

            //第n期除税系数
            k.jieSuanStagetaxRemovalList = [];
            //结算第n期单位价差
            k.jieSuanUnitPriceDifferencList = [];
            //变值权重B分期数
            k.jieSuanStageValuetWeightB = [];


            //结算单位价差
            k.jieSuanPriceDifferenc = 0;
            //过滤出人材机中的人工，并且编码为10000001、10000002、10000003，即综合用工一类、综合用工二类、综合用工三类设置调差默认值
            let tempMaterialCode = k.materialCode.includes('#')?k.materialCode.split('#')[0]:k.materialCode;
            if (["10000001", "10000002", "10000003"].includes(tempMaterialCode) && k.kind === 1){
                k.isDifference = true;
            }
        });


    }



    //结算中新增人材机初始化
    jszNewRcjDataHandler(rcjList){

        rcjList.forEach( k =>{
            //结算打开后 新增数据 为 false原始数据
            k.originalFlag = false;
            //风险幅度设置
            k.riskAmplitudeRangeMin = -5;
            k.riskAmplitudeRangeMax = 5;
            //取费
            k.jieSuanFee = JieSuanFeeSetEnum.METHOD3.code;
            //人材机四种调整法默认值设置
            //调整法
            let adjustmentMethodList = new Array();
            for (let i = 0; i < 4; i++) {
                let rcjDifferenceInfo = new RcjDifferenceInfo();
                rcjDifferenceInfo.rcjDifferenceType = i+1;//人材机调整类型
                rcjDifferenceInfo.jieSuanBasePrice = k.marketPrice;//结算基期价默认值
                let jieSuanDifferencePrice = new JieSuanDifferencePrice();
                jieSuanDifferencePrice.jieSuanPrice = k.marketPrice;//结算单价默认值
                jieSuanDifferencePrice.jieSuanPriceSource = k.marketSourcePrice;//结算单价来源
                rcjDifferenceInfo.jieSuanDifferencePriceList = [jieSuanDifferencePrice];//单价信息集合
                adjustmentMethodList.push(rcjDifferenceInfo);
            }
            k.jieSuanRcjDifferenceTypeList = adjustmentMethodList;
            //结算除税系数
            k.jieSuanTaxRemoval = k.taxRemoval;
            //结算合计数量
            k.jieSuanTotalNumber = k.totalNumber;
            //结算合价
            k.jieSuanTotal = k.total;
            //结算备份市场价
            k.jieSuanMarketPrice = k.marketPrice;

            //第n期除税系数
            k.jieSuanStagetaxRemovalList = [];
            //结算第n期单位价差
            k.jieSuanUnitPriceDifferencList = [];
            //变值权重B分期数
            k.jieSuanStageValuetWeightB = [];


            //结算单位价差
            k.jieSuanPriceDifferenc = 0;
            //过滤出人材机中的人工，并且编码为10000001、10000002、10000003，即综合用工一类、综合用工二类、综合用工三类设置调差默认值
            let tempMaterialCode = k.materialCode.includes('#')?k.materialCode.split('#')[0]:k.materialCode;
            if (["10000001", "10000002", "10000003"].includes(tempMaterialCode) && k.kind === 1){
                k.isDifference = true;
            }
        });


    }



    /**
     * 初始化单位工程
     */
    initSingle(constructId){
        // let singleProjectList = PricingFileFindUtils.getSingleProjectList(obj.sequenceNbr);
        //
        // if (ObjectUtil.isEmpty(singleProjectList)){
        //     singleProjectList = [];
        // }
        for (let singleTypeEnum in JieSuanSingleTypeEnum) {
            let code = JieSuanSingleTypeEnum[singleTypeEnum].code;
            let value = JieSuanSingleTypeEnum[singleTypeEnum].value;
            let arg = {constructId:constructId,singleName:value};
            const singleProject = this.service.singleProjectService.addSingleProject(arg,false);
            singleProject.type = code;
            singleProject.originalFlag = false;
        }
    }



    /**
     * 费用代码   费用记取统一调用接口
     */
    async countFeeCodeAndMathFee(args){
        let {constructId,singleId,unitId,unitWorkId} = args;
        if(ObjectUtils.isEmpty(unitId)){
            args["unitId"]=unitWorkId;
        }
        // 费用记取
        await this.service.autoCostMathService.autoCostMath(args);
        //费用代码记取
        await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice(args);
    }


}

JieSuanProjectService.toString = () => '[class JieSuanProjectService]';
module.exports = JieSuanProjectService;
