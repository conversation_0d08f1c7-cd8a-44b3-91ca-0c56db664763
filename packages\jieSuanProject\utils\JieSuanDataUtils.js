const os = require("os");
const fs = require("fs");
const {JieSuanFileUtils} = require("./JieSuanFileUtils");
const {throws} = require("assert");

const JieSuanCommonUtils = require("./JieSuanCommonUtils");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {NullPointerException} = require("../../PreliminaryEstimate/core/tools/ProjectFileReader");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");


/**
 * 项目数据操作
 */
class JieSuanDataUtils extends JieSuanCommonUtils{









    /**
     * 获取用户设置的默认存储路径
     * @param fileName
     * @return {string}
     */
     getSetStoragePath(fileName) {
        if (!this.checkFileExistence(JieSuanConstantUtil.USERHISTORY_PATH))throw new NullPointerException("文件路径不能为空");
        //读取数据
        const userHistoryData = this.userHistoryData();
        if (ObjectUtil.isEmpty(fileName)){
            return `${userHistoryData.DEF_SAVE_PATH}\\${this.userId()}\\${this.userIdentity()}`;
        }else {
            return `${userHistoryData.DEF_SAVE_PATH}\\${this.userId()}\\${this.userIdentity()}\\${fileName}`.concat("."+JieSuanConstantUtil.JIESUAN_FILE_SUFFIX);
        }
    }


    /**
     * 根据项目ID获取项目数据并且返回项目数据对象
     * 从内存里面取
     * @param projectId 项目ID
     */
    getProjectDataById(projectId) {
        if (ObjectUtil.isEmpty(projectId) || ObjectUtil.isEmpty(global.jieSuanProject) || ObjectUtil.isEmpty(global.jieSuanProject[projectId])) {
            throw new throws("文件路径不能为空");
        }
        return global.jieSuanProject[id].proJectData;
    }


    /**
     * 获取项目下指定的单项
     * @param constructId
     * @param singleId
     * @return {*|null}
     */
    getSingleProject(constructId,singleId) {
        let proJectData = this.getProjectDataById(constructId);
        if (ObjectUtil.isEmpty(proJectData.singleProjects)){
            return null;
        }
        //单项
        let singleProject = proJectData.singleProjects.find((item ) => item.sequenceNbr === singleId);
        return singleProject;

    }


    /**
     * 获取单位工程
     * @param fileName
     * @return {UnitProject}
     */
    getUnit(constructId,singleId, unitId) {
        let proJectData = this.getProjectDataById(constructId);
        if (2 == proJectData.biddingType){
            //单位工程
            let unitProject = proJectData.unitProject;
            return unitProject;
        }else {
            //单项
            let singleProject = this.getSingleProject(constructId,singleId);
            if (ObjectUtil.isEmpty(singleProject)){
                if (!ObjectUtil.isEmpty(proJectData.unitProjectArray)){
                    let unitProject = proJectData.unitProjectArray.find((item ) => item.sequenceNbr === unitId);
                    return unitProject;
                }
            }else {
                let unitProject = singleProject.unitProjects.find((item ) => item.sequenceNbr === unitId);
                return unitProject;
            }
        }

    }

}

module.exports = {
    JieSuanDataUtils: new JieSuanDataUtils()
};

