'use strict';

const { Service, Log } = require('../../../core');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const { ObjectUtils } = require('../../../electron/utils/ObjectUtils');
const YsshssConstant = require('../enum/YsshssConstant');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { NumberUtil } = require('../../../common/NumberUtil');
const { ProcessFluctuateUtil } = require('../utils/ProcessFluctuateUtil');
const { ResponseData } = require('../../../common/ResponseData');
const ChangeExplainEnum = require('../enum/ChangeExplainEnum');

/**
 * 审核 - 其他项目
 */
class YsshOtherProjectService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 初始化其他项目所有模块的匹配关联关系
   */
  async initOtherProjectAllMatch(args) {
    try { // 初始化其他项目总表的匹配关系
      await this.initOtherProjectMatch(args);
      // 初始化暂列金的匹配关系
      await this.initProvisionalMatch(args);
      // 初始化专业工程暂估价的匹配关系
      await this.initZygczgjMatch(args);
      // 初始化总承包服务费的匹配关系
      await this.initCostsServiceMatch(args);
      // 初始化计日工的匹配关系
      await this.initDayWorkerMatch(args);
    } catch (e) {
      console.log('初始化其他项目所有模块的匹配关联关系失败', e);
    }
  }

  /**
   * 初始化其他项目的匹配关联关系
   */
  async initOtherProjectMatch(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 审定
    const sdOtherProject = PricingFileFindUtils.getOtherProject(constructId, singleId, unitId);
    // 送审
    const ssOtherProject = PricingFileFindUtils.getOtherProject(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtils.isEmpty(sdOtherProject) || ObjectUtils.isEmpty(ssOtherProject)) {
      return;
    }
    // 克隆一份数据  避免影响送审数据
    const cloneSsOtherProject = ObjectUtils.cloneDeep(ssOtherProject);
    sdOtherProject.forEach(sdItem => {
      let ssFilter = cloneSsOtherProject.filter(ssItem => {
        if (ObjectUtils.isEmpty(sdItem.type) || ObjectUtils.isEmpty(ssItem.type)) {
          return false;
        }
        return sdItem.type == ssItem.type;
      });
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        ssFilter = ssFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
      }
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        sdItem[YsshssConstant.ysshGlId] = ssFilter.sequenceNbr;
        // 标识已经被匹配过了
        ssFilter.ysshMatch = true;
      }
    });
  }

  /**
   * 初始化暂列金的匹配关系
   */
  async initProvisionalMatch(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 审定
    const sdProvisional = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);
    // 送审
    const ssProvisional = PricingFileFindUtils.getOtherProjectProvisional(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtils.isEmpty(sdProvisional) || ObjectUtils.isEmpty(ssProvisional)) {
      return;
    }
    // 克隆一份数据  避免影响送审数据
    const cloneSsProvisional = ObjectUtils.cloneDeep(ssProvisional);
    sdProvisional.forEach(sdItem => {
      let ssFilter = cloneSsProvisional.filter(ssItem => {
        if (ObjectUtils.isEmpty(sdItem.name) && ObjectUtils.isEmpty(ssItem.name)) {
          return true;
        }
        return sdItem.name == ssItem.name;
      });
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        ssFilter = ssFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
      }
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        sdItem[YsshssConstant.ysshGlId] = ssFilter.sequenceNbr;
        // 标识已经被匹配过了
        ssFilter.ysshMatch = true;
      }
    });
  }

  /**
   * 初始化专业工程暂估价的匹配关系
   */
  async initZygczgjMatch(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 审定
    const sdZygczgjList = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    // 送审
    const ssZygczgjList = PricingFileFindUtils.getOtherProjectZygcZgjList(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtils.isEmpty(sdZygczgjList) || ObjectUtils.isEmpty(ssZygczgjList)) {
      return;
    }
    // 克隆一份数据  避免影响送审数据
    const cloneSsZygczgjList = ObjectUtils.cloneDeep(ssZygczgjList);
    sdZygczgjList.forEach(sdItem => {
      let ssFilter = cloneSsZygczgjList.filter(ssItem => {
        if (ObjectUtils.isEmpty(sdItem.name) && ObjectUtils.isEmpty(ssItem.name)) {
          return true;
        }
        return sdItem.name == ssItem.name;
      });
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        ssFilter = ssFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
      }
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        sdItem[YsshssConstant.ysshGlId] = ssFilter.sequenceNbr;
        // 标识已经被匹配过了
        ssFilter.ysshMatch = true;
      }
    });
  }

  /**
   * 初始化总承包服务费匹配关系
   */
  async initCostsServiceMatch(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 审定
    const sdServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    // 送审
    const ssServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtils.isEmpty(sdServiceCost) || ObjectUtils.isEmpty(ssServiceCost)) {
      return;
    }
    // 克隆一份数据  避免影响送审数据
    const cloneSsServiceCost = ObjectUtils.cloneDeep(ssServiceCost);
    const sdTitleMap = new Map();
    const sdTitleArr = [];
    const ssTitleMap = new Map();
    const ssTitleArr = [];
    // 先把审定的数据区分成标题行和数据行
    this.filterTitleData(sdServiceCost, sdTitleArr, sdTitleMap);
    // 再把送审的数据区分成标题行和数据行
    this.filterTitleData(cloneSsServiceCost, ssTitleArr, ssTitleMap);
    sdTitleArr.forEach(sdItem => {
      let ssFilter = ssTitleArr.filter(ssItem => {
        if (ObjectUtils.isEmpty(sdItem.fxName) && ObjectUtils.isEmpty(ssItem.fxName)) {
          return true;
        }
        return sdItem.fxName == ssItem.fxName;
      });
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        ssFilter = ssFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
      }
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        sdItem[YsshssConstant.ysshGlId] = ssFilter.sequenceNbr;
        // 标识已经被匹配过了
        ssFilter.ysshMatch = true;

        // 父级匹配上之后才能看子级是否匹配上
        const sdChildrenArray = sdTitleMap.get(sdItem.sequenceNbr);
        const ssChildrenArray = ssTitleMap.get(ssFilter.sequenceNbr);
        if (ObjectUtils.isNotEmpty(sdChildrenArray) && ObjectUtils.isNotEmpty(ssChildrenArray)) {
          sdChildrenArray.forEach(sdChildren => {
            let ssChildrenFilter = ssChildrenArray.filter(ssChildren => {
              if (ObjectUtils.isEmpty(sdChildren.fxName) && ObjectUtils.isEmpty(ssChildren.fxName)) {
                return true;
              }
              return sdChildren.fxName == ssChildren.fxName;
            });
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              ssChildrenFilter = ssChildrenFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
            }
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              sdChildren[YsshssConstant.ysshGlId] = ssChildrenFilter.sequenceNbr;
              // 标识已经被匹配过了
              ssChildrenFilter.ysshMatch = true;
            }
          });
        }
      }
    });
  }

  filterTitleData(dataArray, titleDataArr, titleMap) {
    dataArray.forEach(sdItem => {
      if (sdItem.dataType == 1) {
        // 标题行
        titleDataArr.push(sdItem);
      } else {
        const childrenArr = titleMap.get(sdItem.parentId);
        if (ObjectUtils.isEmpty(childrenArr)) {
          titleMap.set(sdItem.parentId, [sdItem]);
        } else {
          childrenArr.push(sdItem);
          titleMap.set(sdItem.parentId, childrenArr);
        }
      }
    });
  }

  /**
   * 初始化计日工的匹配关系
   */
  async initDayWorkerMatch(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 审定
    const sdDayWorkerList = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    // 送审
    const ssDayWorkerList = PricingFileFindUtils.getOtherProjectDayWork(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtils.isEmpty(sdDayWorkerList) || ObjectUtils.isEmpty(ssDayWorkerList)) {
      return;
    }
    // 克隆一份数据  避免影响送审数据
    const cloneSsDayWorkerList = ObjectUtils.cloneDeep(ssDayWorkerList);
    const sdTitleMap = new Map();
    const sdTitleArr = [];
    const ssTitleMap = new Map();
    const ssTitleArr = [];
    // 先把审定的数据区分成标题行和数据行
    this.filterTitleData(sdDayWorkerList, sdTitleArr, sdTitleMap);
    // 再把送审的数据区分成标题行和数据行
    this.filterTitleData(cloneSsDayWorkerList, ssTitleArr, ssTitleMap);
    sdTitleArr.forEach(sdItem => {
      let ssFilter = ssTitleArr.filter(ssItem => {
        if (ObjectUtils.isEmpty(sdItem.worksName) && ObjectUtils.isEmpty(ssItem.worksName)) {
          return true;
        }
        return sdItem.worksName == ssItem.worksName;
      });
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        ssFilter = ssFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
      }
      if (ObjectUtils.isNotEmpty(ssFilter)) {
        sdItem[YsshssConstant.ysshGlId] = ssFilter.sequenceNbr;
        // 标识已经被匹配过了
        ssFilter.ysshMatch = true;

        // 父级匹配上之后才能看子级是否匹配上
        const sdChildrenArray = sdTitleMap.get(sdItem.sequenceNbr);
        const ssChildrenArray = ssTitleMap.get(ssFilter.sequenceNbr);
        if (ObjectUtils.isNotEmpty(sdChildrenArray) && ObjectUtils.isNotEmpty(ssChildrenArray)) {
          sdChildrenArray.forEach(sdChildren => {
            let ssChildrenFilter = ssChildrenArray.filter(ssChildren => {
              if (ObjectUtils.isEmpty(sdChildren.worksName) && ObjectUtils.isEmpty(ssChildren.worksName)) {
                return true;
              }
              return sdChildren.worksName == ssChildren.worksName;
            });
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              ssChildrenFilter = ssChildrenFilter.find(item => ObjectUtils.isEmpty(item.ysshMatch));
            }
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              sdChildren[YsshssConstant.ysshGlId] = ssChildrenFilter.sequenceNbr;
              // 标识已经被匹配过了
              ssChildrenFilter.ysshMatch = true;
            }
          });
        }
      }
    });
  }

  /**
   * 获取其他项目总表的对比结果列表
   */
  async getOtherProjectComparisonList(args) {
    const { ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId } = args;
    // 审定的其他项目数组
    const sdOtherProjectArray = unitId ? PricingFileFindUtils.getOtherProject(constructId, singleId, unitId) : null;
    // 送审的其他项目数组
    const ssOtherProjectArray = ssUnitId ? PricingFileFindUtils.getOtherProject(ssConstructId, ssSingleId, ssUnitId) : null;
    // 返回结果的数组
    const resArray = [];
    const relevanceIds = new Set();
    if (ObjectUtil.isNotEmpty(sdOtherProjectArray)) {
      // 循环审定的数组 去匹配送审数据
      for (const sdOtherProject of sdOtherProjectArray) {
        // 以审定数据为基础
        const itemObj = { ...sdOtherProject };
        if (itemObj.ysshGlId != null && ObjectUtil.isNotEmpty(ssOtherProjectArray)) {
          // 记录匹配的id
          relevanceIds.add(itemObj.ysshGlId);
          // 从送审数组中查找到匹配的数据
          const ssOtherProject = ssOtherProjectArray.find(ssOtherProject => ssOtherProject.sequenceNbr === itemObj.ysshGlId);
          if (ObjectUtil.isNotEmpty(ssOtherProject)) {
            // 把送审的字段填写到审定数据的字段中
            itemObj[YsshssConstant.ysshSysj] = {
              ...ssOtherProject,
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeExplain]: sdOtherProject.changeExplain,
              [YsshssConstant.changeTotal]: 0,
              [YsshssConstant.changeRatio]: 0
            };
            if (!NumberUtil.isEqualNum(itemObj.total, ssOtherProject.total)) {
              if (ObjectUtil.isEmpty(itemObj.total) || NumberUtil.isEqualNum(itemObj.total, 0)) {
                // 匹配项  但是审定的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
                if (ObjectUtil.isEmpty(sdOtherProject.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
                }
              } else if (ObjectUtil.isEmpty(ssOtherProject.total) || NumberUtil.isEqualNum(ssOtherProject.total, 0)) {
                // 匹配项  但是送审的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
                if (ObjectUtil.isEmpty(sdOtherProject.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
                }
              } else {
                // 改项
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
                if (ObjectUtil.isEmpty(sdOtherProject.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
                }
              }
              // 计算差额和比例
              itemObj[YsshssConstant.ysshSysj] = { ...itemObj[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssOtherProject, itemObj, 'total') };
            }
          } else {
            // 审增项
            setAddItemData(itemObj, sdOtherProject);
          }
        } else {
          // 审增项
          setAddItemData(itemObj, sdOtherProject);
        }
        resArray.push(itemObj);
      }
    }

    function setAddItemData(itemObj, sdOtherProject) {
      itemObj[YsshssConstant.ysshSysj] = {
        sequenceNbr: null,
        dispNo: null,
        extraName: null,
        calculationBase: null,
        amount: null,
        total: null,
        [YsshssConstant.change]: YsshssConstant.insert,
        [YsshssConstant.changeExplain]: ObjectUtil.isEmpty(itemObj[YsshssConstant.changeExplain]) ? YsshssConstant.insertExplain : itemObj[YsshssConstant.changeExplain],
        [YsshssConstant.changeTotal]: sdOtherProject.total,
        [YsshssConstant.changeRatio]: 100
      };
    }

    if (ObjectUtil.isNotEmpty(ssOtherProjectArray)) {
      // 审删项数组
      const subtractArr = ssOtherProjectArray.filter(ssOtherProject => !relevanceIds.has(ssOtherProject.sequenceNbr));
      if (ObjectUtil.isNotEmpty(subtractArr)) {
        for (const ssOtherProject of subtractArr) {
          resArray.push({
            dispNo: null,
            extraName: null,
            calculationBase: null,
            amount: null,
            total: null,
            [YsshssConstant.ysshSysj]: {
              [YsshssConstant.change]: YsshssConstant.delete,
              [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
              [YsshssConstant.changeTotal]: NumberUtil.relativeValue(ssOtherProject.total),
              [YsshssConstant.changeRatio]: 100,
              ...ssOtherProject
            }
          });
        }
      }
    }
    return ResponseData.success(resArray);
  }

  checkIsZero(num1, num2) {
    return NumberUtil.isEqualNum(num1, 0) && NumberUtil.isEqualNum(num2, 0);
  }

  /**
   * 暂列金对比结果查询接口
   */
  async getOtherProjectProvisionalComparisonList(args) {
    const { ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId } = args;
    // 审定的暂列金数组
    const sdProvisionalArray = unitId ? PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId) : null;
    // 送审的暂列金数组
    const ssProvisionalArray = ssUnitId ? PricingFileFindUtils.getOtherProjectProvisional(ssConstructId, ssSingleId, ssUnitId) : null;
    // 返回结果的数组
    const resArray = [];
    const relevanceIds = new Set();

    if (ObjectUtil.isNotEmpty(sdProvisionalArray)) {
      for (const sdProvisional of sdProvisionalArray) {
        const itemObj = { ...sdProvisional };
        if (ObjectUtil.isNotEmpty(itemObj.ysshGlId) && ObjectUtil.isNotEmpty(ssProvisionalArray)) {
          // 记录匹配的id
          relevanceIds.add(itemObj.ysshGlId);
          // 从送审数组中查找到匹配的数据
          const ssOtherProjectProvisional = ssProvisionalArray.find(ssOtherProjectProvisional => ssOtherProjectProvisional.sequenceNbr === itemObj.ysshGlId);
          if (ObjectUtil.isNotEmpty(ssOtherProjectProvisional)) {
            // 把送审的字段填写到审定数据的字段中
            itemObj[YsshssConstant.ysshSysj] = {
              ...ssOtherProjectProvisional,
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeExplain]: sdProvisional.changeExplain,
              [YsshssConstant.changeTotal]: 0,
              [YsshssConstant.changeRatio]: 0
            };
            if (!NumberUtil.isEqualNum(itemObj.provisionalSum, ssOtherProjectProvisional.provisionalSum) && !this.checkIsZero(itemObj.provisionalSum, ssOtherProjectProvisional.provisionalSum)) {
              if (ObjectUtil.isEmpty(itemObj.provisionalSum) || NumberUtil.isEqualNum(itemObj.provisionalSum, 0)) {
                // 匹配项  但是审定的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
                if (ObjectUtil.isEmpty(sdProvisional.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
                }
              } else if (ObjectUtil.isEmpty(ssOtherProjectProvisional.provisionalSum) || NumberUtil.isEqualNum(ssOtherProjectProvisional.provisionalSum, 0)) {
                // 匹配项  但是送审的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
                if (ObjectUtil.isEmpty(sdProvisional.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
                }
              } else {
                // 改项
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
                if (ObjectUtil.isEmpty(sdProvisional.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
                }
              }
              // 计算差额和比例
              itemObj[YsshssConstant.ysshSysj] = { ...itemObj[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssOtherProjectProvisional, itemObj, 'provisionalSum') };
            }
          } else {
            // 审增项
            setAddItemData(itemObj, sdProvisional);
          }
        } else {
          // 审增项
          setAddItemData(itemObj, sdProvisional);
        }
        resArray.push(itemObj);
      }
    }

    function setAddItemData(itemObj, sdOtherProjectProvisional) {
      itemObj[YsshssConstant.ysshSysj] = {
        sequenceNbr: null,
        dispNo: null,
        name: null,
        unit: null,
        amount: null,
        price: null,
        provisionalSum: null,
        [YsshssConstant.change]: YsshssConstant.insert,
        [YsshssConstant.changeExplain]: ObjectUtil.isEmpty(itemObj[YsshssConstant.changeExplain]) ? YsshssConstant.insertExplain : itemObj[YsshssConstant.changeExplain],
        [YsshssConstant.changeTotal]: sdOtherProjectProvisional.provisionalSum,
        [YsshssConstant.changeRatio]: 100
      };
    }

    if (ObjectUtil.isNotEmpty(ssProvisionalArray)) {
      // 审删项数组
      const subtractArr = ssProvisionalArray.filter(ssOtherProjectProvisional => !relevanceIds.has(ssOtherProjectProvisional.sequenceNbr));
      if (ObjectUtil.isNotEmpty(subtractArr)) {
        for (const ssOtherProjectProvisional of subtractArr) {
          resArray.push({
            dispNo: null,
            name: null,
            unit: null,
            amount: null,
            price: null,
            provisionalSum: null,
            [YsshssConstant.ysshSysj]: {
              [YsshssConstant.change]: YsshssConstant.delete,
              [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
              [YsshssConstant.changeTotal]: NumberUtil.relativeValue(ssOtherProjectProvisional.provisionalSum),
              [YsshssConstant.changeRatio]: 100,
              ...ssOtherProjectProvisional
            }
          });
        }
      }
    }

    return ResponseData.success(resArray);
  }


  async getOtherProjectZygczgjComparisonList(args) {
    const { ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId } = args;
    // 审定的专业工程暂估价数组
    const sdZygczgjArray = unitId ? PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId) : null;
    // 送审的专业工程暂估价数组
    const ssZygczgjArray = ssUnitId ? PricingFileFindUtils.getOtherProjectZygcZgjList(ssConstructId, ssSingleId, ssUnitId) : null;
    // 返回结果的数组
    const resArray = [];
    const relevanceIds = new Set();
    if (ObjectUtil.isNotEmpty(sdZygczgjArray)) {
      for (const sdOtherProjectZygcZgj of sdZygczgjArray) {
        const itemObj = { ...sdOtherProjectZygcZgj };
        if (ObjectUtil.isNotEmpty(itemObj.ysshGlId) && ObjectUtil.isNotEmpty(ssZygczgjArray)) {
          // 记录匹配的id
          relevanceIds.add(itemObj.ysshGlId);
          // 从送审数组中查找到匹配的数据
          const ssOtherProjectZygcZgj = ssZygczgjArray.find(ssOtherProjectZygc => ssOtherProjectZygc.sequenceNbr === itemObj.ysshGlId);
          if (ObjectUtil.isNotEmpty(ssOtherProjectZygcZgj)) {
            // 把送审的字段填写到审定数据的字段中
            itemObj[YsshssConstant.ysshSysj] = {
              ...ssOtherProjectZygcZgj,
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeTotal]: 0,
              [YsshssConstant.changeRatio]: 0
            };
            if (!NumberUtil.isEqualNum(itemObj.total, ssOtherProjectZygcZgj.total) && !this.checkIsZero(itemObj.total, ssOtherProjectZygcZgj.total)) {
              if (ObjectUtil.isEmpty(itemObj.total) || NumberUtil.isEqualNum(itemObj.total, 0)) {
                // 匹配项  但是审定的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
                if (ObjectUtil.isEmpty(itemObj.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
                }
              } else if (ObjectUtil.isEmpty(ssOtherProjectZygcZgj.total) || NumberUtil.isEqualNum(ssOtherProjectZygcZgj.total, 0)) {
                // 匹配项  但是送审的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
                if (ObjectUtil.isEmpty(itemObj.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
                }
              } else {
                // 改项
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
                if (ObjectUtil.isEmpty(itemObj.changeExplain)) {
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
                }
              }
              // 计算差额和比例
              itemObj[YsshssConstant.ysshSysj] = { ...itemObj[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssOtherProjectZygcZgj, itemObj, 'total') };
            }
          } else {
            // 审增项
            setAddItemData(itemObj, sdOtherProjectZygcZgj);
          }
        } else {
          // 审增项
          setAddItemData(itemObj, sdOtherProjectZygcZgj);
        }
        resArray.push(itemObj);
      }
    }

    function setAddItemData(itemObj, sdOtherProjectZygcZgj) {
      itemObj[YsshssConstant.ysshSysj] = {
        sequenceNbr: null,
        dispNo: null,
        name: null,
        content: null,
        total: null,
        [YsshssConstant.change]: YsshssConstant.insert,
        [YsshssConstant.changeExplain]: ObjectUtil.isEmpty(itemObj[YsshssConstant.changeExplain]) ? YsshssConstant.insertExplain : itemObj[YsshssConstant.changeExplain],
        [YsshssConstant.changeTotal]: sdOtherProjectZygcZgj.total,
        [YsshssConstant.changeRatio]: 100
      };
    }

    if (ObjectUtil.isNotEmpty(ssZygczgjArray)) {
      // 审删项数组
      const subtractArr = ssZygczgjArray.filter(ssOtherProjectZygcZgj => !relevanceIds.has(ssOtherProjectZygcZgj.sequenceNbr));
      if (ObjectUtil.isNotEmpty(subtractArr)) {
        for (const ssOtherProjectZygcZgj of subtractArr) {
          resArray.push({
            dispNo: null,
            name: null,
            content: null,
            total: null,
            [YsshssConstant.ysshSysj]: {
              [YsshssConstant.change]: YsshssConstant.delete,
              [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
              [YsshssConstant.changeTotal]: NumberUtil.relativeValue(ssOtherProjectZygcZgj.total),
              [YsshssConstant.changeRatio]: 100,
              ...ssOtherProjectZygcZgj
            }
          });
        }
      }
    }
    return ResponseData.success(resArray);
  }


  async getOtherProjectServiceCostComparisonList(args) {
    const { ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId } = args;
    // 审定的专业工程暂估价数组
    const sdServiceCostArray = unitId ? PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId) : null;
    // 送审的专业工程暂估价数组
    const ssServiceCostArray = ssUnitId ? PricingFileFindUtils.getOtherProjectServiceCost(ssConstructId, ssSingleId, ssUnitId) : null;
    // 返回结果的数组
    const resArray = [];
    // 审定的Map  key是审定的总承包服务费的标题行的sequenceNbr  value是标题行下的子级数据集合
    const sdMap = new Map();
    // 送审的Map  key是送审的总承包服务费的标题行的sequenceNbr  value是标题行下的子级数据集合
    const ssMap = new Map();
    const relevanceIds = new Set();

    // 把数据填充到对应的Map中
    if (ObjectUtil.isNotEmpty(sdServiceCostArray)) {
      this.groupTitleForMap(sdServiceCostArray, sdMap);
    }
    if (ObjectUtil.isNotEmpty(ssServiceCostArray)) {
      this.groupTitleForMap(ssServiceCostArray, ssMap);
    }

    if (ObjectUtil.isNotEmpty(sdServiceCostArray)) {
      for (const sdOtherProjectServiceCost of sdServiceCostArray) {
        if (sdOtherProjectServiceCost.dataType != 1) {
          // 不是标题行
          continue;
        }
        const itemObj = { ...sdOtherProjectServiceCost };
        if (itemObj.ysshGlId != null && ObjectUtil.isNotEmpty(ssServiceCostArray)) {
          relevanceIds.add(itemObj.ysshGlId);
          // 审定的标题行对应匹配到的送审的标题行数据
          const ssOtherProjectServiceCost = ssServiceCostArray.find(ssOtherProjectServiceCost => ssOtherProjectServiceCost.sequenceNbr === itemObj.ysshGlId);
          if (ObjectUtil.isNotEmpty(ssOtherProjectServiceCost)) {
            // 对应匹配的送审标题行数据
            itemObj[YsshssConstant.ysshSysj] = {
              ...ssOtherProjectServiceCost,
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeExplain]: sdOtherProjectServiceCost.changeExplain,
              [YsshssConstant.changeTotal]: 0,
              [YsshssConstant.changeRatio]: 0
            };
            if (!NumberUtil.isEqualNum(itemObj.xmje, ssOtherProjectServiceCost.xmje) || !NumberUtil.isEqualNum(itemObj.rate, ssOtherProjectServiceCost.rate) || !NumberUtil.isEqualNum(itemObj.fwje, ssOtherProjectServiceCost.fwje)) {
              if (!this.checkIsZero(itemObj.fwje, ssOtherProjectServiceCost.fwje)) {
                if (ObjectUtil.isEmpty(itemObj.fwje) || NumberUtil.isEqualNum(itemObj.fwje, 0)) {
                  // 匹配项  但是审定的金额为空或为零
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
                  if (ObjectUtil.isEmpty(sdOtherProjectServiceCost.changeExplain)) {
                    itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
                  }
                } else if (ObjectUtil.isEmpty(ssOtherProjectServiceCost.fwje) || NumberUtil.isEqualNum(ssOtherProjectServiceCost.fwje, 0)) {
                  // 匹配项  但是送审的金额为空或为零
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
                  if (ObjectUtil.isEmpty(sdOtherProjectServiceCost.changeExplain)) {
                    itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
                  }
                } else {
                  // 改项
                  itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
                  if (ObjectUtil.isEmpty(sdOtherProjectServiceCost.changeExplain)) {
                    itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
                  }
                }
                // 计算差额和比例
                itemObj[YsshssConstant.ysshSysj] = { ...itemObj[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssOtherProjectServiceCost, itemObj, 'fwje') };
              }
            }
            resArray.push(itemObj);

            // 审定标题行处理完开始处理标题行下的数据行
            const sdChildrenArray = sdMap.get(sdOtherProjectServiceCost.sequenceNbr);
            if (ObjectUtil.isNotEmpty(sdChildrenArray)) {
              // 定义自己的关联id  用于获取未关联的送审数据行
              const childrenRelevanceIds = new Set();
              // 循环审定的标题行的子级
              for (const childrenSdServiceCost of sdChildrenArray) {
                childrenRelevanceIds.add(childrenSdServiceCost.ysshGlId);
                // 审定的数据为基础
                const sdChildrenItem = { ...childrenSdServiceCost };
                // 获取送审的标题行下的数据行
                const ssChildrenArray = ssMap.get(ssOtherProjectServiceCost.sequenceNbr);
                if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
                  // 根据审定的数据行的ysshGlId关联找对应的送审数据行
                  const ssChildren = ssChildrenArray.find(ssChildren => sdChildrenItem.ysshGlId === ssChildren.sequenceNbr);
                  if (sdChildrenItem.ysshGlId != null && ObjectUtil.isNotEmpty(ssChildren)) {
                    sdChildrenItem[YsshssConstant.ysshSysj] = {
                      ...ssChildren,
                      [YsshssConstant.change]: YsshssConstant.noChange,
                      [YsshssConstant.changeExplain]: childrenSdServiceCost.changeExplain,
                      [YsshssConstant.changeTotal]: 0,
                      [YsshssConstant.changeRatio]: 0
                    };
                    if (!NumberUtil.isEqualNum(sdChildrenItem.xmje, ssChildren.xmje) || !NumberUtil.isEqualNum(sdChildrenItem.rate, ssChildren.rate) || !NumberUtil.isEqualNum(sdChildrenItem.fwje, ssChildren.fwje)) {
                      if (!this.checkIsZero(sdChildrenItem.fwje, ssChildren.fwje)) {
                        if (ObjectUtil.isEmpty(sdChildrenItem.fwje) || NumberUtil.isEqualNum(sdChildrenItem.fwje, 0)) {
                          // 匹配项  但是审定的金额为空或为零
                          sdChildrenItem[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
                          if (ObjectUtil.isEmpty(childrenSdServiceCost.changeExplain)) {
                            sdChildrenItem[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
                          }
                        } else if (ObjectUtil.isEmpty(ssChildren.fwje) || NumberUtil.isEqualNum(ssChildren.fwje, 0)) {
                          // 匹配项  但是送审的金额为空或为零
                          sdChildrenItem[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
                          if (ObjectUtil.isEmpty(childrenSdServiceCost.changeExplain)) {
                            sdChildrenItem[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
                          }
                        } else {
                          // 改项
                          sdChildrenItem[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
                          if (ObjectUtil.isEmpty(childrenSdServiceCost.changeExplain)) {
                            sdChildrenItem[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
                          }
                        }
                        // 计算差额和比例
                        sdChildrenItem[YsshssConstant.ysshSysj] = { ...sdChildrenItem[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssChildren, sdChildrenItem, 'fwje') };
                      }
                    }
                  } else {
                    // 没找到对应的送审数据行 或者 审定没有关联的送审数据  说明是审增项
                    setAddItemData(sdChildrenItem);
                  }
                } else {
                  // 送审的标题行下没有数据行，那么与之匹配的审定下的子级全都是审增项
                  setAddItemData(sdChildrenItem);
                }
                resArray.push(sdChildrenItem);
              }
              // 循环完成检查审定的标题行的子级之后   应该把与之匹配的送审标题下的子级进行检查  没有匹配到的送审子级算该子级下的审删项
              // 获取送审的标题行下的数据行
              const ssChildrenArray = ssMap.get(ssOtherProjectServiceCost.sequenceNbr);
              if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
                // 送审下的数据行数组不为空   检查审删项
                const subtractArr = ssChildrenArray.filter(ssChildren => !childrenRelevanceIds.has(ssChildren.sequenceNbr));
                if (ObjectUtil.isNotEmpty(subtractArr)) {
                  for (const subtractItem of subtractArr) {
                    subItemPush(subtractItem);
                  }
                }
              }
            } else {
              // 审定的标题行匹配到了送审的标题行  但是审定的标题行没有子级  那么送审如果有子级 则全部都是审删行
              // 获取送审的标题行下的数据行
              const ssChildrenArray = ssMap.get(ssOtherProjectServiceCost.sequenceNbr);
              if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
                for (const ssChildren of ssChildrenArray) {
                  subItemPush(ssChildren);
                }
              }
            }
          } else {
            // 这个审定的标题行没有对应的送审标题行，说明是审增项  包括这个审定标题行下的子级
            addAndChildrenArray(itemObj);
          }
        } else {
          // 审增项
          addAndChildrenArray(itemObj);
        }
      }
    }

    function setAddItemData(sdChildrenItem) {
      sdChildrenItem[YsshssConstant.ysshSysj] = {
        dispNo: null,
        fxName: null,
        xmje: null,
        serviceContent: null,
        amount: null,
        rate: null,
        fwje: null,
        dataType: sdChildrenItem.dataType,
        [YsshssConstant.change]: YsshssConstant.insert,
        [YsshssConstant.changeExplain]: ObjectUtil.isEmpty(sdChildrenItem[YsshssConstant.changeExplain]) ? YsshssConstant.insertExplain : sdChildrenItem[YsshssConstant.changeExplain],
        [YsshssConstant.changeTotal]: sdChildrenItem.fwje,
        [YsshssConstant.changeRatio]: 100
      };
    }

    function addAndChildrenArray(itemObj) {
      setAddItemData(itemObj);
      resArray.push(itemObj);
      // 添加完父级添加子级
      const sdChildrenArray = sdMap.get(itemObj.sequenceNbr);
      if (ObjectUtil.isNotEmpty(sdChildrenArray)) {
        for (const sdChildren of sdChildrenArray) {
          const sdChildrenItem = { ...sdChildren };
          setAddItemData(sdChildrenItem);
          resArray.push(sdChildrenItem);
        }
      }
    }

    function subItemPush(subtractItem) {
      const ssChildrenItem = {
        dispNo: null,
        fxName: null,
        xmje: null,
        serviceContent: null,
        amount: null,
        rate: null,
        fwje: null,
        dataType: subtractItem.dataType,
        [YsshssConstant.ysshSysj]: {
          ...subtractItem,
          [YsshssConstant.change]: YsshssConstant.delete,
          [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
          [YsshssConstant.changeTotal]: NumberUtil.relativeValue(subtractItem.fwje),
          [YsshssConstant.changeRatio]: 100
        }
      };
      resArray.push(ssChildrenItem);
    }

    function addSsChildrenData(ssOtherProjectServiceCost) {
      resArray.push({
        dispNo: null,
        fxName: null,
        xmje: null,
        serviceContent: null,
        amount: null,
        rate: null,
        fwje: null,
        dataType: ssOtherProjectServiceCost.dataType,
        [YsshssConstant.ysshSysj]: {
          ...ssOtherProjectServiceCost,
          [YsshssConstant.change]: YsshssConstant.delete,
          [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
          [YsshssConstant.changeTotal]: NumberUtil.relativeValue(ssOtherProjectServiceCost.fwje),
          [YsshssConstant.changeRatio]: 100
        }
      });
    }

    if (ObjectUtil.isNotEmpty(ssServiceCostArray)) {
      // 送审数组不为空   获取送审数据中的标题行没有被关联到的标题行
      const subtractTitleArr = ssServiceCostArray.filter(ssOtherProjectServiceCost => ssOtherProjectServiceCost.dataType === 1 && !relevanceIds.has(ssOtherProjectServiceCost.sequenceNbr));
      if (ObjectUtil.isNotEmpty(subtractTitleArr)) {
        // 发现没有被关联到的送审数据标题行  则该标题行和下级全部都是审删项
        for (const ssOtherProjectServiceCost of subtractTitleArr) {
          // 先把标题行添加进去
          addSsChildrenData(ssOtherProjectServiceCost);
          // 再添加标题下的子级
          const ssChildrenArray = ssMap.get(ssOtherProjectServiceCost.sequenceNbr);
          if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
            for (const ssChildren of ssChildrenArray) {
              addSsChildrenData(ssChildren);
            }
          }
        }
      }
    }

    return ResponseData.success(resArray);
  }

  /**
   * 把数组根据父级的sequenceNbr进行分组
   */
  groupTitleForMap(dataArray, map) {
    for (const item of dataArray) {
      const { dataType, sequenceNbr, parentId } = item;
      if (dataType === 1) {
        // 标题行
        map.set(sequenceNbr, []);
      } else {
        // 数据行
        const childrenArray = map.get(parentId) || [];
        childrenArray.push(item);
        map.set(parentId, childrenArray);
      }
    }
  }

  async getOtherProjectDayWorkComparisonList(args) {
    const { ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId } = args;
    // 审定的专业工程暂估价数组
    const sdDayWorkArray = unitId ? PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId) : null;
    // 送审的专业工程暂估价数组
    const ssDayWorkArray = ssUnitId ? PricingFileFindUtils.getOtherProjectDayWork(ssConstructId, ssSingleId, ssUnitId) : null;
    // 审定的Map  key是审定的计日工的标题行的sequenceNbr  value是标题行下的子级数据集合
    const sdMap = new Map();
    // 送审的Map  key是送审的计日工的标题行的sequenceNbr  value是标题行下的子级数据集合
    const ssMap = new Map();
    const resArray = [];
    const relevanceIds = new Set();
    // 获取this的作用域
    let self = this;
    // 把数据填充到对应的Map中
    if (ObjectUtil.isNotEmpty(sdDayWorkArray)) {
      this.groupTitleForMap(sdDayWorkArray, sdMap);
    }
    if (ObjectUtil.isNotEmpty(ssDayWorkArray)) {
      this.groupTitleForMap(ssDayWorkArray, ssMap);
    }

    if (ObjectUtil.isNotEmpty(sdDayWorkArray)) {
      for (const sdOtherProjectDayWork of sdDayWorkArray) {
        if (sdOtherProjectDayWork.dataType != 1) {
          // 不是标题行
          continue;
        }
        const itemObj = { ...sdOtherProjectDayWork };
        if (ObjectUtil.isNotEmpty(itemObj.ysshGlId) && ObjectUtil.isNotEmpty(ssDayWorkArray)) {
          relevanceIds.add(itemObj.ysshGlId);
          // 审定的标题行对应匹配到的送审的标题行数据
          const ssOtherProjectDayWork = ssDayWorkArray.find(ssOtherProjectDayWork => ssOtherProjectDayWork.sequenceNbr === itemObj.ysshGlId);
          if (ObjectUtil.isNotEmpty(ssOtherProjectDayWork)) {
            // 对应匹配的送审标题行数据
            setUpdateItemData(itemObj, ssOtherProjectDayWork);
            resArray.push(itemObj);

            // 审定标题行处理完开始处理标题行下的数据行
            const sdChildrenArray = sdMap.get(sdOtherProjectDayWork.sequenceNbr);
            if (ObjectUtil.isNotEmpty(sdChildrenArray)) {
              // 定义子级的关联id  用于获取未关联的送审数据行
              const childrenRelevanceIds = new Set();
              // 循环审定的标题行的子级
              for (const childrenSdDayWork of sdChildrenArray) {
                childrenRelevanceIds.add(childrenSdDayWork.ysshGlId);
                // 审定的数据为基础
                const sdChildrenItem = { ...childrenSdDayWork };
                // 获取送审的标题行下的数据行
                const ssChildrenArray = ssMap.get(ssOtherProjectDayWork.sequenceNbr);
                if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
                  // 根据审定的数据行的ysshGlId关联找对应的送审数据行
                  const ssChildren = ssChildrenArray.find(ssChildren => sdChildrenItem.ysshGlId === ssChildren.sequenceNbr);
                  if (sdChildrenItem.ysshGlId != null && ObjectUtil.isNotEmpty(ssChildren)) {
                    setUpdateItemData(sdChildrenItem, ssChildren);
                  } else {
                    // 没找到对应的送审数据行 或者 审定没有关联的送审数据  说明是审增项
                    setAddItemData(sdChildrenItem);
                  }
                } else {
                  // 送审的标题行下没有数据行，那么与之匹配的审定下的子级全都是审增项
                  setAddItemData(sdChildrenItem);
                }
                resArray.push(sdChildrenItem);
              }
              // 循环完成检查审定的标题行的子级之后   应该把与之匹配的送审标题下的子级进行检查  没有匹配到的送审子级算该子级下的审删项
              // 获取送审的标题行下的数据行
              const ssChildrenArray = ssMap.get(ssOtherProjectDayWork.sequenceNbr);
              if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
                // 送审下的数据行数组不为空   检查审删项
                const subtractArr = ssChildrenArray.filter(ssChildren => !childrenRelevanceIds.has(ssChildren.sequenceNbr));
                if (ObjectUtil.isNotEmpty(subtractArr)) {
                  for (const subtractItem of subtractArr) {
                    subItemPush(subtractItem);
                  }
                }
              }
            } else {
              // 审定的标题行匹配到了送审的标题行  但是审定的标题行没有子级  那么送审如果有子级 则全部都是审删行
              // 获取送审的标题行下的数据行
              const ssChildrenArray = ssMap.get(ssOtherProjectDayWork.sequenceNbr);
              if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
                for (const ssChildren of ssChildrenArray) {
                  subItemPush(ssChildren);
                }
              }
            }
          } else {
            // 这个审定的标题行没有对应的送审标题行，说明是审增项  包括这个审定标题行下的子级
            addAndChildrenArray(itemObj);
          }
        } else {
          // 审增项
          addAndChildrenArray(itemObj);
        }
      }
    }

    function setAddItemData(sdChildrenItem) {
      sdChildrenItem[YsshssConstant.ysshSysj] = {
        dispNo: null,
        worksName: null,
        unit: null,
        specification: null,
        taxRemoval: null,
        jxTotal: null,
        quantitativeExpression: null,
        tentativeQuantity: null,
        price: null,
        total: null,
        dataType: sdChildrenItem.dataType,
        [YsshssConstant.change]: YsshssConstant.insert,
        [YsshssConstant.changeExplain]: ObjectUtil.isEmpty(sdChildrenItem[YsshssConstant.changeExplain]) ? YsshssConstant.insertExplain : sdChildrenItem[YsshssConstant.changeExplain],
        [YsshssConstant.changeTotal]: sdChildrenItem.total,
        [YsshssConstant.changeRatio]: 100
      };
    }

    function addAndChildrenArray(itemObj) {
      setAddItemData(itemObj);
      resArray.push(itemObj);
      // 添加完父级添加子级
      const sdChildrenArray = sdMap.get(itemObj.sequenceNbr);
      if (ObjectUtil.isNotEmpty(sdChildrenArray)) {
        for (const sdChildren of sdChildrenArray) {
          const sdChildrenItem = { ...sdChildren };
          setAddItemData(sdChildrenItem);
          resArray.push(sdChildrenItem);
        }
      }
    }

    function setUpdateItemData(item, ssData) {
      item[YsshssConstant.ysshSysj] = {
        sequenceNbr: ssData.sequenceNbr,
        dispNo: ssData.dispNo,
        worksName: ssData.worksName,
        unit: ssData.unit,
        tentativeQuantity: ssData.tentativeQuantity,
        quantitativeExpression: ssData.quantitativeExpression,
        specification: ssData.specification,
        taxRemoval: ssData.taxRemoval,
        jxTotal: ssData.jxTotal,
        price: ssData.price,
        csPrice: ssData.csPrice,
        total: ssData.total,
        dataType: ssData.dataType,
        [YsshssConstant.change]: YsshssConstant.noChange,
        [YsshssConstant.changeExplain]: item.changeExplain,
        [YsshssConstant.changeTotal]: 0,
        [YsshssConstant.changeRatio]: 0
      };
      if (!NumberUtil.isEqualNum(item.tentativeQuantity, ssData.tentativeQuantity) || !NumberUtil.isEqualNum(item.price, ssData.price) || !NumberUtil.isEqualNum(item.total, ssData.total)) {
        // 因为是三个字段判断是不是改项  并且是tentativeQuantity*price=total
        // 所以要先看total是不是都是0  如果都是0   但是三个字段还有不同的 那么这个就是改项
        if (!self.checkIsZero(item.total, ssData.total)) {
          if (NumberUtil.isEqualNum(item.total, 0) && NumberUtil.isEqualNum(ssData.total, 0)) {
            item[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
          } else {
            if (ObjectUtil.isEmpty(item.total) || NumberUtil.isEqualNum(item.total, 0)) {
              // 匹配项  但是审定的金额为空或为零
              item[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
              if (ObjectUtil.isEmpty(item.changeExplain)) {
                item[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
              }
            } else if (ObjectUtil.isEmpty(ssData.total) || NumberUtil.isEqualNum(ssData.total, 0)) {
              // 匹配项  但是送审的金额为空或为零
              item[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
              if (ObjectUtil.isEmpty(item.changeExplain)) {
                item[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
              }
            } else {
              // 改项
              item[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
              if (ObjectUtil.isEmpty(item.changeExplain)) {
                item[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
              }
            }
          }
          // 计算差额和比例
          item[YsshssConstant.ysshSysj] = { ...item[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssData, item, 'total') };
        }
      }
    }

    function subItemPush(subtractItem) {
      const ssChildrenItem = {
        dispNo: null,
        worksName: null,
        unit: null,
        specification: null,
        taxRemoval: null,
        jxTotal: null,
        quantitativeExpression: null,
        tentativeQuantity: null,
        price: null,
        total: null,
        dataType: subtractItem.dataType,
        [YsshssConstant.ysshSysj]: {
          ...subtractItem,
          [YsshssConstant.change]: YsshssConstant.delete,
          [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
          [YsshssConstant.changeTotal]: NumberUtil.relativeValue(subtractItem.total),
          [YsshssConstant.changeRatio]: 100
        }
      };
      resArray.push(ssChildrenItem);
    }

    if (ObjectUtil.isNotEmpty(ssDayWorkArray)) {
      // 送审数组不为空   获取送审数据中的标题行没有被关联到的标题行
      const subtractTitleArr = ssDayWorkArray.filter(ssOtherProjectDayWork => ssOtherProjectDayWork.dataType === 1 && !relevanceIds.has(ssOtherProjectDayWork.sequenceNbr));
      if (ObjectUtil.isNotEmpty(subtractTitleArr)) {
        // 发现没有被关联到的送审数据标题行  则该标题行和下级全部都是审删项
        for (const ssOtherProjectDayWork of subtractTitleArr) {
          addSsChildrenData(ssOtherProjectDayWork);
          const ssChildrenArray = ssMap.get(ssOtherProjectDayWork.sequenceNbr);
          if (ObjectUtil.isNotEmpty(ssChildrenArray)) {
            for (const ssChildren of ssChildrenArray) {
              addSsChildrenData(ssChildren);
            }
          }
        }
      }
    }

    function addSsChildrenData(ssOtherProjectDayWork) {
      resArray.push({
        dispNo: null,
        worksName: null,
        unit: null,
        specification: null,
        taxRemoval: null,
        jxTotal: null,
        quantitativeExpression: null,
        tentativeQuantity: null,
        price: null,
        total: null,
        dataType: ssOtherProjectDayWork.dataType,
        [YsshssConstant.ysshSysj]: {
          ...ssOtherProjectDayWork,
          [YsshssConstant.change]: YsshssConstant.delete,
          [YsshssConstant.changeExplain]: YsshssConstant.deleteExplain,
          [YsshssConstant.changeTotal]: NumberUtil.relativeValue(ssOtherProjectDayWork.total),
          [YsshssConstant.changeRatio]: 100
        }
      });
    }
    return ResponseData.success(resArray);
  }


  async updateMatch(args) {
    const { bizType } = args;
    switch (bizType) {
      case ChangeExplainEnum.QTXM.code:
        await this.updateOtherProjectMatch(args);
        break;
      case ChangeExplainEnum.QTXM_ZLJ.code:
        await this.updateProvisionalMatch(args);
        break;
      case ChangeExplainEnum.QTXM_ZGJ.code:
        await this.updateZygczgjMatch(args);
        break;
      case ChangeExplainEnum.QTXM_ZCBFWF.code:
        await this.updateCostsServiceMatch(args);
        break;
      case ChangeExplainEnum.QTXM_JRG.code:
        await this.updateDayWorkerMatch(args);
        break;
      default:
        throw new Error('参数错误');
    }
    return ResponseData.success(true);
  }

  async updateOtherProjectMatch(args) {
    const { ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, constructId, singleId, unitId, sequenceNbr } = args;
    throw new Error('其他项目总表不支持修改匹配关联');
  }

  async updateProvisionalMatch(args) {
    const { ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, constructId, singleId, unitId, sequenceNbr } = args;
    const sdProvisional = PricingFileFindUtils.getOtherProjectProvisional(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(sdProvisional)) {
      throw new Error('未找到审定数据');
    }
    const sdItem = sdProvisional.find(otherProject => otherProject.sequenceNbr === sequenceNbr);
    if (ObjectUtil.isEmpty(sdItem)) {
      throw new Error('未找到审定数据');
    }
    const ssProvisional = PricingFileFindUtils.getOtherProjectProvisional(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtil.isEmpty(ssProvisional)) {
      throw new Error('未找到送审数据');
    }
    const ssItem = ssProvisional.find(otherProject => otherProject.sequenceNbr === ssSequenceNbr);
    if (ObjectUtil.isEmpty(ssItem)) {
      throw new Error('未找到送审数据');
    }
    // 需要先查找这个送审的数据之前有没有已经被关联  如果有需要把已关联的关系删除
    const oldMatch = sdProvisional.filter(provisional => provisional[YsshssConstant.ysshGlId] === ssSequenceNbr);
    if (ObjectUtil.isNotEmpty(oldMatch)) {
      oldMatch.forEach(oldItem => oldItem[YsshssConstant.ysshGlId] = null);
    }
    // 给本次选择的审定和送审关联
    sdItem[YsshssConstant.ysshGlId] = ssSequenceNbr;
  }

  async updateZygczgjMatch(args) {
    const { ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, constructId, singleId, unitId, sequenceNbr } = args;
    const sdZygczgjList = PricingFileFindUtils.getOtherProjectZygcZgjList(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(sdZygczgjList)) {
      throw new Error('未找到审定数据');
    }
    const sdItem = sdZygczgjList.find(otherProject => otherProject.sequenceNbr === sequenceNbr);
    if (ObjectUtil.isEmpty(sdItem)) {
      throw new Error('未找到审定数据');
    }
    const ssZygczgjList = PricingFileFindUtils.getOtherProjectZygcZgjList(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtil.isEmpty(ssZygczgjList)) {
      throw new Error('未找到送审数据');
    }
    const ssItem = ssZygczgjList.find(otherProject => otherProject.sequenceNbr === ssSequenceNbr);
    if (ObjectUtil.isEmpty(ssItem)) {
      throw new Error('未找到送审数据');
    }
    // 需要先查找这个送审的数据之前有没有已经被关联  如果有需要把已关联的关系删除
    const oldMatch = sdZygczgjList.filter(zgjItem => zgjItem[YsshssConstant.ysshGlId] === ssSequenceNbr);
    if (ObjectUtil.isNotEmpty(oldMatch)) {
      oldMatch.forEach(oldItem => oldItem[YsshssConstant.ysshGlId] = null);
    }
    // 给本次选择的审定和送审关联
    sdItem[YsshssConstant.ysshGlId] = ssSequenceNbr;
  }

  async updateCostsServiceMatch(args) {
    const { ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, constructId, singleId, unitId, sequenceNbr } = args;
    const sdServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(sdServiceCost)) {
      throw new Error('未找到审定数据');
    }
    const sdItem = sdServiceCost.find(otherProject => otherProject.sequenceNbr === sequenceNbr);
    if (ObjectUtil.isEmpty(sdItem)) {
      throw new Error('未找到审定数据');
    }
    const ssServiceCost = PricingFileFindUtils.getOtherProjectServiceCost(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtil.isEmpty(ssServiceCost)) {
      throw new Error('未找到送审数据');
    }
    const ssItem = ssServiceCost.find(otherProject => otherProject.sequenceNbr === ssSequenceNbr);
    if (ObjectUtil.isEmpty(ssItem)) {
      throw new Error('未找到送审数据');
    }
    if (sdItem.dataType != ssItem.dataType) {
      // 不同类型的数据行不能相互匹配
      throw new Error('标题行不能与数据行匹配');
    }
    if (sdItem.dataType == 1) {
      // 修改的标题行
      // 修改标题行时，被匹配的送审数据也必须是标题行，并且标题行相互匹配之后，送审标题行下的数据行也会跟随标题行重新与审定标题行下的数据行进行匹配

      // 先找出送审标题行下的数据行
      const ssChildren = ssServiceCost.filter(serviceCost => serviceCost.parentId === ssSequenceNbr);
      // 再找出审定标题行下的数据行
      const sdChildren = sdServiceCost.filter(serviceCost => serviceCost.parentId === ssSequenceNbr);

      // 找出送审以前匹配的审定标题行
      const oldSdTitle = sdServiceCost.filter(serviceCost => serviceCost[YsshssConstant.ysshGlId] === ssSequenceNbr);
      if (ObjectUtil.isNotEmpty(oldSdTitle)) {
        // 如果本次的送审之前有匹配审定数据 把之前的审定标题行的关联关系清除
        oldSdTitle[YsshssConstant.ysshGlId] = null;
        const oldSdChildren = sdServiceCost.filter(serviceCost => serviceCost.parentId === oldSdTitle[0].sequenceNbr);
        if (ObjectUtil.isNotEmpty(oldSdChildren)) {
          // 再把之前的审定标题行下的数据行的关联关系清除
          oldSdChildren.forEach(oldSdChild => oldSdChild[YsshssConstant.ysshGlId] = null);
        }
      }

      // 给审定标题行关联送审的标题行
      sdItem[YsshssConstant.ysshGlId] = ssSequenceNbr;
      if (ObjectUtil.isNotEmpty(sdChildren)) {
        // 给审定标题行下的数据行关联送审的标题行
        if (ObjectUtil.isNotEmpty(ssChildren)) {
          const ssMatchedIds = [];
          sdChildren.forEach(sdItem => {
            let ssChildrenFilter = ssChildren.filter(ssItem => {
              if (ObjectUtils.isEmpty(sdItem.fxName) && ObjectUtils.isEmpty(ssItem.fxName)) {
                return true;
              }
              return sdItem.fxName == ssItem.fxName;
            });
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              ssChildrenFilter = ssChildrenFilter.find(item => !ssMatchedIds.includes(item.sequenceNbr));
            }
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              sdItem[YsshssConstant.ysshGlId] = ssChildrenFilter.sequenceNbr;
              // 标识已经被匹配过了
              ssMatchedIds.push(ssChildrenFilter.sequenceNbr);
            }
          });
        } else {
          // 如果审定关联的送审标题行下没有数据行，则把审定标题行下的数据行的关联关系清除  相当于审定标题行下的数据行全是审增
          sdChildren.forEach(sdItem => sdItem[YsshssConstant.ysshGlId] = null);
        }
      }
    } else {
      // 修改的数据行

      // 需要先查找这个送审的数据之前有没有已经被关联  如果有需要把已关联的关系删除
      const oldMatch = sdServiceCost.filter(serviceCost => serviceCost[YsshssConstant.ysshGlId] === ssSequenceNbr);
      if (ObjectUtil.isNotEmpty(oldMatch)) {
        oldMatch.forEach(oldItem => oldItem[YsshssConstant.ysshGlId] = null);
      }
      // 给本次选择的审定和送审关联
      sdItem[YsshssConstant.ysshGlId] = ssSequenceNbr;
    }
  }

  async updateDayWorkerMatch(args) {
    const { ssConstructId, ssSingleId, ssUnitId, ssSequenceNbr, constructId, singleId, unitId, sequenceNbr } = args;
    const sdDayWorker = PricingFileFindUtils.getOtherProjectDayWork(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(sdDayWorker)) {
      throw new Error('未找到审定数据');
    }
    const sdItem = sdDayWorker.find(otherProject => otherProject.sequenceNbr === sequenceNbr);
    if (ObjectUtil.isEmpty(sdItem)) {
      throw new Error('未找到审定数据');
    }
    const ssDayWorker = PricingFileFindUtils.getOtherProjectDayWork(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtil.isEmpty(ssDayWorker)) {
      throw new Error('未找到送审数据');
    }
    const ssItem = ssDayWorker.find(otherProject => otherProject.sequenceNbr === ssSequenceNbr);
    if (ObjectUtil.isEmpty(ssItem)) {
      throw new Error('未找到送审数据');
    }
    if (sdItem.dataType != ssItem.dataType) {
      // 不同类型的数据行不能相互匹配
      throw new Error('标题行不能与数据行匹配');
    }
    if (sdItem.dataType == 1) {
      // 修改的标题行
      // 修改标题行时，被匹配的送审数据也必须是标题行，并且标题行相互匹配之后，送审标题行下的数据行也会跟随标题行重新与审定标题行下的数据行进行匹配

      // 先找出送审标题行下的数据行
      const ssChildren = ssDayWorker.filter(darWorker => darWorker.parentId === ssSequenceNbr);
      // 再找出审定标题行下的数据行
      const sdChildren = sdDayWorker.filter(darWorker => darWorker.parentId === ssSequenceNbr);

      // 找出送审以前匹配的审定标题行
      const oldSdTitle = sdDayWorker.filter(darWorker => darWorker[YsshssConstant.ysshGlId] === ssSequenceNbr);
      if (ObjectUtil.isNotEmpty(oldSdTitle)) {
        // 如果本次的送审之前有匹配审定数据 把之前的审定标题行的关联关系清除
        oldSdTitle[YsshssConstant.ysshGlId] = null;
        const oldSdChildren = sdDayWorker.filter(serviceCost => serviceCost.parentId === oldSdTitle[0].sequenceNbr);
        if (ObjectUtil.isNotEmpty(oldSdChildren)) {
          // 再把之前的审定标题行下的数据行的关联关系清除
          oldSdChildren.forEach(oldSdChild => oldSdChild[YsshssConstant.ysshGlId] = null);
        }
      }

      // 给审定标题行关联送审的标题行
      sdItem[YsshssConstant.ysshGlId] = ssSequenceNbr;
      if (ObjectUtil.isNotEmpty(sdChildren)) {
        // 给审定标题行下的数据行关联送审的标题行
        if (ObjectUtil.isNotEmpty(ssChildren)) {
          const ssMatchedIds = [];
          sdChildren.forEach(sdItem => {
            let ssChildrenFilter = ssChildren.filter(ssItem => {
              if (ObjectUtils.isEmpty(sdItem.worksName) && ObjectUtils.isEmpty(ssItem.worksName)) {
                return true;
              }
              return sdItem.worksName == ssItem.worksName;
            });
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              ssChildrenFilter = ssChildrenFilter.find(item => !ssMatchedIds.includes(item.sequenceNbr));
            }
            if (ObjectUtils.isNotEmpty(ssChildrenFilter)) {
              sdItem[YsshssConstant.ysshGlId] = ssChildrenFilter.sequenceNbr;
              // 标识已经被匹配过了
              ssMatchedIds.push(ssChildrenFilter.sequenceNbr);
            }
          });
        } else {
          // 如果审定关联的送审标题行下没有数据行，则把审定标题行下的数据行的关联关系清除  相当于审定标题行下的数据行全是审增
          sdChildren.forEach(sdItem => sdItem[YsshssConstant.ysshGlId] = null);
        }
      }
    } else {
      // 修改的数据行

      // 需要先查找这个送审的数据之前有没有已经被关联  如果有需要把已关联的关系删除
      const oldMatch = sdDayWorker.filter(dayWorker => dayWorker[YsshssConstant.ysshGlId] === ssSequenceNbr);
      if (ObjectUtil.isNotEmpty(oldMatch)) {
        oldMatch.forEach(oldItem => oldItem[YsshssConstant.ysshGlId] = null);
      }
      // 给本次选择的审定和送审关联
      sdItem[YsshssConstant.ysshGlId] = ssSequenceNbr;
    }
  }
}

YsshOtherProjectService.toString = () => '[class YsshOtherProjectService]';
module.exports = YsshOtherProjectService;
