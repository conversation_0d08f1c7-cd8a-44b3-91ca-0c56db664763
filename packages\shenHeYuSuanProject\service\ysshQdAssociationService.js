const { Service } = require('../../../core');
const { PricingFileFindUtils } = require('../../../electron/utils/PricingFileFindUtils');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const { ResponseData } = require('../../../common/ResponseData');
const { QdAssociation } = require('../model/QdAssociation');
const BranchProjectLevelConstant = require('../../../electron/enum/BranchProjectLevelConstant');
const YsshssConstant = require('../enum/YsshssConstant');

/**
 * 分部分项清单关联
 * 整个分部分项的清单关联整体数据存储在单位工程对象中，字段名字是：ysshQdAssociation
 * ysshQdAssociation是一个对象，对应QdAssociation.ts
 */
class YsshQdAssociationService extends Service {

  constructor(ctx) {
    super(ctx);
  }

  /**
   * 新建清单关联
   */
  async createQdAssociation(args) {
    // bizType 为1表示新建清单关联 为0表示删除关联
    // 这里的constructId, singleId, unitId, qdId  肯定是送审的  因为只有
    const { constructId, singleId, unitId, bizType, ssConstructId, ssSingleId, ssUnitId, ssQdId } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(unit)) {
      throw new Error('参数错误');
    }
    const ssUnit = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
    const qdItem = ssUnit.itemBillProjects.getNodeById(ssQdId);
    if (ObjectUtil.isEmpty(qdItem)) {
      throw new Error('清单数据不存在');
    }
    let name = '';
    if (bizType == 1) {
      // 新增关联
      if (ObjectUtil.isEmpty(unit.ysshQdAssociation)) {
        const map = new Map();
        name = '关联1';
        map.set(ssQdId, '关联1');
        unit.ysshQdAssociation = new QdAssociation(1, map);
      } else {
        const ysshQdAssociation = unit.ysshQdAssociation;
        if (ysshQdAssociation.associationMap.has(ssQdId)) {
          throw new Error('当前清单已有关联');
        }
        ysshQdAssociation.num = ysshQdAssociation.num + 1;
        name = '关联' + ysshQdAssociation.num;
        ysshQdAssociation.associationMap.set(ssQdId, name);
      }
    } else {
      // 删除关联
      if (ObjectUtil.isNotEmpty(unit.ysshQdAssociation)) {
        unit.ysshQdAssociation.associationMap.delete(ssQdId);
        // 检查已经关联了这个被删除的送审清单的审定清单  如果关联了就删除关联关系
        const sdUnit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        // 删除审定单位中与送审清单关联项有关联的清单关联关系
        this.delSdUnitQdAssociation(sdUnit, ssQdId);
      }
    }
    return ResponseData.success(name);
  }

  delSdUnitQdAssociation(sdUnit, ssQdId) {
    if (ObjectUtil.isNotEmpty(sdUnit) && ObjectUtil.isNotEmpty(sdUnit.itemBillProjects)) {
      let qdList = sdUnit.itemBillProjects.filter(item => item.kind == BranchProjectLevelConstant.qd);
      // let qdList = sdUnit.itemBillProjects.getNodesByKind(BranchProjectLevelConstant.qd);
      if (ObjectUtil.isNotEmpty(qdList)) {
        for (const v of qdList) {
          if (ObjectUtil.isNotEmpty(v.ysshAssociation) && v.ysshAssociation.qdId == ssQdId) {
            v.ysshAssociation = null;
          }
        }
      }
    }
  }

  /**
   * 绑定清单关联关系
   */
  async bindQdAssociation(args) {
    // 这里的constructId, singleId, unitId, qdId   一定是审定的  因为只有审增的数据可以绑定已有的关联项
    const { constructId, singleId, unitId, qdId, name } = args;
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(unit)) {
      throw new Error('参数错误');
    }
    const qdItem = unit.itemBillProjects.getNodeById(qdId);
    if (ObjectUtil.isEmpty(qdItem)) {
      throw new Error('清单数据不存在');
    }
    if (ObjectUtil.isEmpty(unit.ysshQdAssociation)) {
      throw new Error('操作失败');
    }
    if (ObjectUtil.isEmpty(name)) {
      // 说明是清除关联
      qdItem.ysshAssociation = null;
    } else {
      let associationQdId = null;
      for (const [k, v] of unit.ysshQdAssociation.associationMap) {
        if (v == name) {
          associationQdId = k;
          break;
        }
      }
      if (ObjectUtil.isEmpty(associationQdId)) {
        throw new Error('所选清单关联项不存在，请重试');
      }
      qdItem.ysshAssociation = {
        name: name,
        qdId: associationQdId
      };
    }
    return ResponseData.success(true);
  }

  /**
   * 查询已有的所有清单关联关系
   */
  async queryAllQdAssociationList(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    const resArr = [];
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(unit.ysshQdAssociation)) {
      return ResponseData.success(resArr);
    }
    const ssUnit = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtil.isEmpty(unit) || ObjectUtil.isEmpty(ssUnit)) {
      throw new Error('参数错误');
    }
    let qdList = unit.itemBillProjects.filter(item => item.kind == BranchProjectLevelConstant.qd && ObjectUtil.isNotEmpty(item.ysshAssociation));
    // let qdList = unit.itemBillProjects.getNodesByKind(BranchProjectLevelConstant.qd);
    // if (ObjectUtil.isEmpty(qdList)) {
    //   return ResponseData.success(resArr);
    // }
    // qdList = [...qdList.values()].filter(item => ObjectUtil.isNotEmpty(item.ysshAssociation));
    if (ObjectUtil.isEmpty(qdList)) {
      return ResponseData.success(resArr);
    }
    let glQdIds = new Set();
    for (let item of qdList) {
      if (glQdIds.has(item.ysshAssociation.qdId)) {
        continue;
      }
      glQdIds.add(item.ysshAssociation.qdId);
      const nodeById = ssUnit.itemBillProjects.getNodeById(item.ysshAssociation.qdId);
      if (ObjectUtil.isNotEmpty(nodeById)) {
        const qdNode = ObjectUtil.cloneDeep(nodeById);
        qdNode.ysshAssociation = item.ysshAssociation;
        qdList.push(qdNode);
      }

    }
    qdList.sort((a, b) => {
      const name1 = a.ysshAssociation.name;
      const name2 = b.ysshAssociation.name;
      const num1 = name1.replace('关联', '');
      const num2 = name2.replace('关联', '');
      return num1.localeCompare(num2);
    });
    for (const qd of qdList) {
      const item = { ...qd };
      const qdNode = ssUnit.itemBillProjects.getNodeById(qd.ysshAssociation.qdId);
      if (ObjectUtil.isNotEmpty(qdNode)) {
        item[YsshssConstant.ysshSysj] = { ...qdNode };
      }
      resArr.push(item);
    }
    return ResponseData.success(resArr);
  }


  /**
   * 报表查询已有的所有清单 一对多、审减、调价等关系
   */
  async queryAllQdAssociationListForSheet(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    const resArr = [];
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    const ssUnit = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
    if (ObjectUtil.isEmpty(unit)) {
      throw new Error('参数错误');
    }
    let qdList = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxListComparison(args);
    qdList = qdList.filter(item => item.kind == BranchProjectLevelConstant.qd);
    if (ObjectUtil.isEmpty(qdList)) {
      return ResponseData.success(resArr);
    }
    qdList.sort((a, b) => {
      if (ObjectUtil.isNotEmpty(a.ysshAssociation) && ObjectUtil.isNotEmpty(b.ysshAssociation)) {
        const name1 = a.ysshAssociation.name;
        const name2 = b.ysshAssociation.name;
        const num1 = name1.replace('关联', '');
        const num2 = name2.replace('关联', '');
        return num1.localeCompare(num2);
      }
    });
    for (const qd of qdList) {
      const item = { ...qd };
      if (ObjectUtil.isNotEmpty(qd.ysshAssociation)) {
        const qdNode = ssUnit.itemBillProjects.getNodeById(qd.ysshAssociation.qdId);//查找关联的送审数据
        if (ObjectUtil.isNotEmpty(qdNode)) {
          item[YsshssConstant.ysshSysj] = { ...qdNode };
        }
      }
      resArr.push(item);
    }
    return ResponseData.success(resArr);
  }

  /**
   * 查询某个清单可选择的清单关联项
   */
  async queryQdAssociation(args) {
    // change 表示是当前这个清单是： 正常项(0)、增项(1)、删项(2)、改项(3)
    const resArr = [];
    const { constructId, singleId, unitId, qdId, ssConstructId, ssSingleId, ssUnitId, ssQdId, change } = args;
    if (change == YsshssConstant.delete) {
      return ResponseData.success(resArr);
    }
    if (change == YsshssConstant.noChange) {
      // 如果是正常项  但是送审的清单是不存在的(说明审定的数据是0)   那么这种是特殊的正常项   不能新建清单
      if (ObjectUtil.isEmpty(ssQdId)) {
        return ResponseData.success(resArr);
      }
      const ssUnit = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
      if (ObjectUtil.isEmpty(ssUnit)) {
        return ResponseData.success(resArr);
      }
      const ssQdNode = ssUnit.itemBillProjects.getNodeById(ssQdId);
      if (ObjectUtil.isEmpty(ssQdNode)) {
        return ResponseData.success(resArr);
      }
    }
    const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
    if (ObjectUtil.isEmpty(unit)) {
      return ResponseData.success(resArr);
    }
    if (change == YsshssConstant.noChange || change == YsshssConstant.update) {
      //  匹配项或者改项
      if (ObjectUtil.isNotEmpty(unit.ysshQdAssociation)) {
        const ssAssociation = unit.ysshQdAssociation.associationMap.get(ssQdId);
        if (ObjectUtil.isNotEmpty(ssAssociation)) {
          resArr.push(ssAssociation);
        } else {
          resArr.push('新建关联' + (unit.ysshQdAssociation.num + 1));
        }
      } else {
        resArr.push('新建关联1');
      }
    } else if (change == YsshssConstant.insert) {
      // 增项   下拉显示的应该是所有已新建的关联项列表
      if (ObjectUtil.isNotEmpty(unit.ysshQdAssociation)) {
        for (const [k, v] of unit.ysshQdAssociation.associationMap) {
          resArr.push(v);
        }
        resArr.sort((a, b) => {
          const num1 = a.replace('关联', '');
          const num2 = b.replace('关联', '');
          return num1.localeCompare(num2);
        });
      }
    }
    return ResponseData.success(resArr);
  }


  /**
   * 检查每个分部分项清单的清单关联状态是否正确   并维护一份正确的清单关联数据
   * 这个方法逻辑很绕  如果可以改预算的代码就完全不用这么麻烦 TvT
   */
  async checkAndUpdateQdAssociation(args) {
    // change 表示是当前这个清单是： 正常项(0)、增项(1)、删项(2)、改项(3)
    const { constructId, singleId, unitId, qdId, ssConstructId, ssSingleId, ssUnitId, ssQdId, change } = args;
    try {
      if (change == YsshssConstant.noChange || change == YsshssConstant.update) {
        // 正常项  或者  改项
        const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        const qdNode = unit.itemBillProjects.getNodeById(qdId);
        // 【正常项、改项】的清单上只能新建关联，新建的关联是存储在单位级别的unit.ysshQdAssociation中的   审定清单本身不能有qdNode.ysshAssociation
        if (ObjectUtil.isNotEmpty(qdNode)) {
          qdNode.ysshAssociation = null;
        }
      } else if (change == YsshssConstant.insert) {
        // 增项
        const unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        const qdNode = unit.itemBillProjects.getNodeById(qdId);
        if (ObjectUtil.isNotEmpty(ssQdId)) {
          // 增项  但是有送审数据参数
          const ssUnit = PricingFileFindUtils.getUnit(ssConstructId, ssSingleId, ssUnitId);
          const ssQdNode = ssUnit.itemBillProjects.getNodeById(ssQdId);
          if (ObjectUtil.isNotEmpty(ssQdNode)) {
            if (ObjectUtil.isNotEmpty(unit.ysshQdAssociation)) {
              // 删除一下这个送审的清单关联项   不管有没有  直接删
              const delRes = unit.ysshQdAssociation.associationMap.delete(ssQdId);
              if (delRes) {
                // 删除成功   那么就需要再维护一下之前关联过这个的清单关联项的审定清单
                // 删除审定单位中与送审清单关联项有关联的清单关联关系
                this.delSdUnitQdAssociation(unit, ssQdId);
              }
            }
          }
        } else {
          // 增项  没有送审数据 只有审定数据
          if (ObjectUtil.isEmpty(unit.ysshQdAssociation)) {
            // 如果单位级别都没有清单关联数据   那么清单上肯定也没有
            qdNode.ysshAssociation = null;
          } else {
            if (ObjectUtil.isNotEmpty(qdNode.ysshAssociation)) {
              // 如果审定的清单有关联关系，那么检查这个关联的清单关联项是不是还存在  不存在的话直接清楚审的清单的关联关系
              if (!unit.ysshQdAssociation.associationMap.has(qdNode.ysshAssociation.qdId)) {
                qdNode.ysshAssociation = null;
              }
            }
          }
        }
      } else if (change == YsshssConstant.delete) {
        // 删项
        let sdUnit = null;
        if (ObjectUtil.isNotEmpty(qdId)) {
          // 删项  但是有审定数据参数
          sdUnit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
          const qdNode = sdUnit.itemBillProjects.getNodeById(qdId);
          if (ObjectUtil.isNotEmpty(qdNode)) {
            // 有审定数据的删项   那么不管有没有关联，直接清除这个审定清单的关联关系
            qdNode.ysshAssociation = null;
          }
        }
        // 查看单位级别有没有创建过的清单关联数据  如果有就需要看这个删项之前是不是有关联项  如果有就删除这个关联项并删除这个关联项的所有关联关系
        // 但是需要注意审定的单位不是空的  因为又可能这就是一个审删的单位
        if (ObjectUtil.isNotEmpty(sdUnit) && ObjectUtil.isNotEmpty(sdUnit.ysshQdAssociation)) {
          const delRes = sdUnit.ysshQdAssociation.associationMap.delete(ssQdId);
          if (delRes) {
            this.delSdUnitQdAssociation(sdUnit, ssQdId);
          }
        }
      }
    } catch (e) {
      console.error('分部分项关联数据维护失败 ', e);
    }
  }

  /**
   * 获取这个清单上应该显示的清单关联内容
   */
  async getQdAssociationValue(args) {
    const { constructId, singleId, unitId, qdId, ssConstructId, ssSingleId, ssUnitId, ssQdId, change } = args;
    try {
      if (change == YsshssConstant.noChange || change == YsshssConstant.update) {
        // 正常项 或者 改项

        // 获取审定的单位
        const sdUnit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        if (ObjectUtil.isEmpty(sdUnit) || ObjectUtil.isEmpty(sdUnit.ysshQdAssociation)) {
          return '';
        }
        return sdUnit.ysshQdAssociation.associationMap.get(ssQdId);
      } else if (change == YsshssConstant.insert) {
        // 增项
        const sdUnit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        const sdQdNode = sdUnit.itemBillProjects.getNodeById(qdId);
        if (ObjectUtil.isNotEmpty(sdQdNode)) {
          if (ObjectUtil.isNotEmpty(sdUnit.ysshQdAssociation)) {
            if (ObjectUtil.isEmpty(sdQdNode.ysshAssociation)) {
              return '';
            } else {
              return sdUnit.ysshQdAssociation.associationMap.get(sdQdNode.ysshAssociation.qdId);
            }
          } else {
            return '';
          }
        } else {
          return '';
        }
      } else if (change == YsshssConstant.delete) {
        // 删项
        return '';
      }
    } catch (e) {
      console.error('获取分部分项关联数据失败 ', e);
    }
    return '';
  }


}


YsshQdAssociationService.toString = () => '[class YsshQdAssociationService]';
module.exports = YsshQdAssociationService;
