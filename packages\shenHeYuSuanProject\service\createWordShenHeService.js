const {Service} = require("../../../core");
const {PricingFileFindUtils} = require('../../../electron/utils/PricingFileFindUtils')
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const officegen = require('officegen')
const fs = require('fs');
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const os = require("os");
const {dialog} = require("electron");
const path = require("path");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const ProjectLevelConstant = require("../../../electron/enum/ProjectLevelConstant");
const ConstructBiddingTypeConstant = require("../../../electron/enum/ConstructBiddingTypeConstant");
const YsshssConstant = require("../enum/YsshssConstant");
const {toArrayTree} = require("xe-utils");
const {arrayToTree, treeToArray} = require("../../../electron/main_editor/tree");
const _ = require("lodash");

class createWordShenHeService extends Service{

    constructor(ctx) {
        super(ctx);
    }


    async createWordData(arg) {
        let  sdProject = PricingFileFindUtils.getProjectObjById(arg.constructId);
        let  ssProject = PricingFileFindUtils.getProjectObjById(arg.ssConstructId);
        // 创建一个可写流，用于将文档写入到文件中
        let wordName = sdProject.sequenceNbr+ssProject.sequenceNbr;
        const baseDataDir = `${os.homedir()}\\.xilidata\\${wordName}.docx`;//默认存储路径
        const writeStream = await  fs.createWriteStream(baseDataDir);
        // 创建一个新的Word文档对象
        const docx = officegen({
            type: 'docx',
            orientation: 'portrait',
            pageMargins: {top: 720, right: 720, bottom: 720, left: 720}
        });
        //设置标题
        let constructName = sdProject.constructName
        // 添加内容到文档
        var pObj = docx.createP({align: 'center'});// 创建行 设置居中 大标题
        pObj.addText(constructName, {bold: true, font_face: 'Arial', font_size: 18});
        const pObj1 = docx.createP();
        pObj1.addLineBreak();
        pObj1.addText('审核结果：', {bold: true, size: 24});
        pObj1.addLineBreak();
        let sdXmGczj = 0;
        let ssXmGczj = 0;
        let str = "";
        //审定的单位工程
        let sdunitProjects  = PricingFileFindUtils.getUnitList(arg.constructId);
        if(ObjectUtils.isNotEmpty(sdunitProjects)){
            for(const sdUnit of sdunitProjects){
                sdXmGczj = NumberUtil.add(sdXmGczj,sdUnit.gczj);
            }
        }
        //送审的单位工程
        let ssunitProjects  = PricingFileFindUtils.getUnitList(arg.ssConstructId);
        if(ObjectUtils.isNotEmpty(ssunitProjects)){
            for(const ssUnit of ssunitProjects){
                ssXmGczj = NumberUtil.add(ssXmGczj,ssUnit.gczj);
            }
        }
        let zgczj = NumberUtil.subtract(sdXmGczj,ssXmGczj);
        str =  await this.jgjs(zgczj);
        //工程项目分析
        let blNumber = NumberUtil.divide(zgczj,ssXmGczj);
        let bl = NumberUtil.numberScale2(blNumber * 100);
        let blStr = await  this.bljs(bl);
        pObj1.addText(sdProject.constructName + "，" + "送审金额" +ssXmGczj+"元"+
            "（大写："+NumberUtil.numToCny(ssXmGczj)+ "）审定金额" + sdXmGczj+"元"+"（大写：" +NumberUtil.numToCny(sdXmGczj)+ "），"
            +str+zgczj+ "，"+blStr+bl+"%");
        pObj1.addLineBreak();
        pObj1.addText('主要审减原因说明如下：', {bold: true, size: 24});
        pObj1.addLineBreak();
        //组装匹配数据
        const args ={
            sequenceNbr:arg.constructId
        }
        let result = await this.service.shenHeYuSuanProject.shenHeProjectService.generateLevelTreeNodeStructure(args);
        let arrayTree = toArrayTree(result, {
            key: 'id',
            parentKey: 'parentId',
        });

        await this.traverseConstruct(arrayTree,pObj1,arg,{"dispNo":"0"});
        // 生成文档并写入到可写流中
        docx.generate(writeStream);

        writeStream.on('finish', function () {
            console.log('Word文档已成功写入到分析报告中');
        });

        writeStream.on('error', function (err) {
            console.error('写入Word文档时发生错误:', err);
        });
        // 等待文件写入完成
        await new Promise((resolve, reject) => {
            writeStream.on('finish', resolve);
            writeStream.on('error', reject);
        });
        return baseDataDir;
    }


    async traverseConstruct(treeList,pObj1,arg,dispNo) {
        let bakDispNo = _.cloneDeep(dispNo);
        for (let i = 0; i < treeList.length; i++) {
            let element = treeList[i];

            if (element.levelType == 2 && element.children.length>0 && element.children[0].levelType==2) {
                //如果是不含单位的子单项 对子单项数据进行统计
                await this.createSingWordData(element,pObj1,arg,dispNo);
            }
            //如果是含有单位的子单项  对子单项进行统计同时遍历单位数据
            if (element.levelType == 2 && element.children.length>0 && element.children[0].levelType==3) {
                await this.createUnitWordData(element,pObj1,arg,dispNo);
            }
            if (element.children.length > 0) {
                if (dispNo.dispNo == 0) {
                    dispNo.dispNo = "0";
                }else {
                    dispNo.dispNo = dispNo.dispNo+"."+0;
                }
                dispNo = await this.traverseConstruct(element.children,pObj1,arg,dispNo);
            }
        }
        if (bakDispNo.dispNo.includes(".")) {
            let lastIndex = bakDispNo.dispNo.lastIndexOf(".");
            bakDispNo.dispNo = bakDispNo.dispNo.slice(0,lastIndex);
        }
        return bakDispNo;

    }


    /**
     * 创建word单项工程数据
     * @returns {Promise<void>}
     */
    async createSingWordData(singleProject,pObj1,arg,dispNo){
        let str = "";
        let bl = 0;
        let blStr = "";

        let argParam = {};
        argParam.levelType = 2;
        argParam.constructId = arg.constructId;
        argParam.ssConstructId = arg.ssConstructId;
        argParam.singleId = singleProject.id;
        argParam.ssSingleId = singleProject.ysshSingleId;
        let singleProjectData = await this.service.shenHeYuSuanProject.ysshCostAnalysisService.getCostAnalysisData(argParam);
        let sdData = singleProjectData.result.costAnalysisSingleVOList;
        let ysshSysj = singleProjectData.result.costAnalysisSingleVOList.ysshSysj;

        let sdGczj = sdData.gczj;
        let ssGczj = ysshSysj.gczj;

        let gczj = NumberUtil.subtract(sdGczj,ssGczj);
        str =  await this.jgjs(gczj);
        //单项工程项目分析
        bl = NumberUtil.numberScale2(NumberUtil.divide(gczj,ssGczj) * 100);
        if (ssGczj == 0) {
            bl = 100;
        }
        blStr = await  this.ljs(bl);
        if (gczj != 0) {
            //处理序号的问题
            let arrayData = dispNo.dispNo.split(".");
            if (arrayData.length ==1) {
                dispNo.dispNo = Number.parseInt(dispNo.dispNo)+1;
            }else {
                let lastIndex = dispNo.dispNo.lastIndexOf(".");
                let dispNoStr = dispNo.dispNo.slice(0,lastIndex);
                let num = Number.parseInt(arrayData[arrayData.length-1]);
                dispNo.dispNo = dispNoStr+"."+(num+1);
            }
            pObj1.addText(dispNo.dispNo+"、"+singleProject.name+"，送审金额"+ssGczj+"元"+"，"+"审定金额"+sdGczj+"，"+str+gczj+"，"+blStr+bl+"%");
            pObj1.addLineBreak();
        }
    }




    /**
     * 单位工程word数据
     * @param unitProjects
     * @param pObj1
     * @param i   单项工程循环的下标 用来word展示 目录层级
     * @returns {Promise<void>}
     */
    async createUnitWordData(singleProject,pObj1,arg,dispNum){
        //对当前单项进行统计
        await this.createSingWordData(singleProject,pObj1,arg,dispNum);

        let argParam = {};
        argParam.levelType = 2;
        argParam.constructId = arg.constructId;
        argParam.ssConstructId = arg.ssConstructId;
        argParam.singleId = singleProject.id;
        argParam.ssSingleId = singleProject.ysshSingleId;
        let singleProjectData = await this.service.shenHeYuSuanProject.ysshCostAnalysisService.getCostAnalysisData(argParam);
        let unitDataList = singleProjectData.result.costAnalysisSingleVOList.childrenList;
        let str = "";
        let je = 0;
        let blStr = "";
        let bl = 0;
        for (let j = 0; j < unitDataList.length; j++) {
            let dispNo = dispNum.dispNo+"."+(j+1);
            let unitProjectData = unitDataList[j];
            let unitDirectoryData = singleProject.children.filter(item => item.id ==unitProjectData.sequenceNbr)[0];

            let ssUnitGczj = ObjectUtils.isEmpty(unitProjectData.ysshSysj.gczj)?0:unitProjectData.ysshSysj.gczj;


            //计算审增审减
            je = NumberUtil.subtract(unitProjectData.gczj,ssUnitGczj);
            str =  await this.jgjs(je);
            //单位工程项目分析
            bl = NumberUtil.numberScale2(NumberUtil.divide(je,ssUnitGczj) * 100);
            blStr = await  this.ljs(bl);
            if (ssUnitGczj == 0) {
                bl = 100;
            }
            if (je != 0) {
                pObj1.addText(dispNo+"、"+unitProjectData.projectName+"，送审金额"+ssUnitGczj+"元"+"，"+"审定金额"+unitProjectData.gczj+"，"+str+je+"，"+blStr+bl+"%");
                pObj1.addLineBreak();
            }

            let ssconstructId = arg.ssConstructId;//送审id
            let sdConstructId = arg.constructId;
            let sdSingleId = singleProject.id;
            let ssSingleId = singleProject.ysshSingleId;
            let sdUnitId = unitDirectoryData.id;
            let ssUnitId = unitDirectoryData.ysshUnitId;

            const  args=
                {
                    ssConstructId : ssconstructId,//送审id
                    ssSingleId : ssSingleId,
                    ssUnitId : ssUnitId,
                    constructId : sdConstructId,//审定id
                    singleId: sdSingleId,
                    unitId : sdUnitId,
                    //分部分项冗余字段
                    sequenceNbr: "",
                    pageNum:1,
                    pageSize:300000,
                    isAllFlag:false
                }
            //清单跟定额
            let arrayKind = ["03"];
            //筛选出增删改项
            let arrayChange = [1,2,3];
            let indexNumber = 0;
            //分布分项数据
            let fbfxListComparison = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxListComparison(args);
            //筛选出清单跟定额得数据
            if(ObjectUtils.isNotEmpty(fbfxListComparison)){
                fbfxListComparison =  fbfxListComparison.filter(obj => arrayKind.includes(obj.kind));
                //筛选出增删改项
                if(ObjectUtils.isNotEmpty(fbfxListComparison)){
                    fbfxListComparison = fbfxListComparison.filter(obj => arrayChange.includes(obj.ysshSysj.change));
                    indexNumber = await this.fbfxList(fbfxListComparison,pObj1,indexNumber);
                }
            }

            //措施项目数据
            let resultList = await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(args);
            if(ObjectUtils.isNotEmpty(resultList)){
                //筛选出清单跟定额得数据
                resultList = resultList.filter(obj => arrayKind.includes(obj.kind)).filter(item =>!ObjectUtils.isEmpty(item.name) && !item.name.includes("安全生产"));
                //筛选出增删改项
                //resultList = resultList.filter(obj => arrayChange.includes(obj.ysshSysj.change));
                if(ObjectUtils.isNotEmpty(resultList)){
                    indexNumber = await this.csxmList(resultList,pObj1,indexNumber);
                }
            }

            //其他项目
            let qtxmList  = await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectComparisonList(args);
            if(ObjectUtils.isNotEmpty(qtxmList)){
                indexNumber = await this.qtxm(qtxmList,pObj1,indexNumber);
            }
            //规费,税金,安全生产、文明施工费
            let ssUnit = PricingFileFindUtils.getUnit(args.ssConstructId,args.ssSingleId,args.ssUnitId);
            let sdUnit = PricingFileFindUtils.getUnit(args.constructId,args.singleId,args.unitId);
            let sdgfee = 0;
            let ssgfee = 0;
            if(ObjectUtils.isNotEmpty(sdUnit) && ObjectUtils.isNotEmpty(sdUnit.gfee)){
                sdgfee =   sdUnit.gfee;
            }
            if(ObjectUtils.isNotEmpty(ssUnit) && ObjectUtils.isNotEmpty(ssUnit.gfee)){
                ssgfee =   ssUnit.gfee;
            }
            console.log('word文档规费打印='+sdgfee+":"+ssgfee+"ssUnit:"+ssUnit+"sdgfee:"+sdgfee);
            let gfee = NumberUtil.numberScale2(NumberUtil.subtract(sdgfee,ssgfee));
            str =  await this.jgjs(gfee);

            if (gfee != 0) {
                indexNumber++;
                pObj1.addText(indexNumber+"）规费，送审基数"+ssgfee+"，"+"审定基数"+sdgfee+"，"+str+gfee+"元");
                pObj1.addLineBreak();
            }
            let sdsj = 0;
            let sssj = 0;
            if(ObjectUtils.isNotEmpty(sdUnit) && ObjectUtils.isNotEmpty(sdUnit.sj)){
                sdsj = sdUnit.sj;
            }
            if(ObjectUtils.isNotEmpty(ssUnit) && ObjectUtils.isNotEmpty(ssUnit.sj)){
                sssj = ssUnit.sj;
            }
            let sj = NumberUtil.numberScale2(NumberUtil.subtract(sdsj,sssj));
            if (sj != 0) {
                str =  await this.jgjs(sj);
                indexNumber++;
                pObj1.addText(indexNumber+"）税金，送审基数"+sssj+"，"+"审定基数"+sdsj+"，"+str+sj+"元");
                pObj1.addLineBreak();
            }
            let sdsafeFee = 0;
            let sssafeFee = 0;
            if(ObjectUtils.isNotEmpty(sdUnit) && ObjectUtils.isNotEmpty(sdUnit.safeFee)){
                sdsafeFee = sdUnit.safeFee;
            }
            if(ObjectUtils.isNotEmpty(ssUnit) && ObjectUtils.isNotEmpty(ssUnit.safeFee)){
                sssafeFee = ssUnit.safeFee;
            }
            let safeFee = NumberUtil.numberScale2(NumberUtil.subtract(sdsafeFee,sssafeFee));
            str =  await this.jgjs(safeFee);
            if (safeFee != 0) {
                indexNumber++;
                pObj1.addText(indexNumber+"）安全生产、文明施工费，送审基数"+sssafeFee+"，"+"审定基数"+sdsafeFee+"，"+str+safeFee+"元");
                pObj1.addLineBreak();
            }
        }
    }

    /**
     * 分布分项
     * @param fbfxListComparison
     * @param pObj1
     * @param indexNumber
     * @returns {Promise<void>}
     */
    async fbfxList(fbfxListComparison,pObj1,indexNumber){
        for(let l = 0; l < fbfxListComparison.length; l++){
            let ysshSysjSD = fbfxListComparison[l];
            let ysshSysjSS = ysshSysjSD.ysshSysj;
            if(ObjectUtils.isNotEmpty(ysshSysjSS)){
                let changeTotal = 0;
                if(ObjectUtils.isNotEmpty(ysshSysjSS.changeTotal)){//没变动的没有  changeTotal
                    changeTotal = ysshSysjSS.changeTotal;
                }
                let bdName = "";
                if(ObjectUtils.isNotEmpty(ysshSysjSD.bdName)){
                    bdName =  ysshSysjSD.bdName;
                }else if(ObjectUtils.isNotEmpty(ysshSysjSS.bdName)){
                    bdName =  ysshSysjSS.bdName;
                }
                let str =  await this.jgjs(changeTotal);
                if(ysshSysjSS.change === YsshssConstant.insert){
                    pObj1.addText(NumberUtil.add(l,1)+"）增加"+bdName+"审增金额"+ysshSysjSS.changeTotal+"元");
                    pObj1.addLineBreak();
                    indexNumber++;
                }
                if(ysshSysjSS.change === YsshssConstant.delete){
                    pObj1.addText(NumberUtil.add(l,1)+"）删除"+bdName+"审删金额"+ysshSysjSS.changeTotal+"元");
                    pObj1.addLineBreak();
                    indexNumber++;
                }
                if(ysshSysjSS.change === YsshssConstant.update){
                    let sdprice = 0
                    if(ObjectUtils.isNotEmpty(ysshSysjSD.price)){
                        sdprice =  ysshSysjSD.price;
                    }
                    let ssprice = 0
                    if(ObjectUtils.isNotEmpty(ysshSysjSS.price)){
                        ssprice =  ysshSysjSS.price;
                    }
                    pObj1.addText(NumberUtil.add(l,1)+"）"+bdName+"送审综合单价"+ssprice+"元"+"，"+"审定综合单价"+sdprice+"元"+"，"+str+changeTotal+"元");
                    pObj1.addLineBreak();
                    indexNumber++;
                }
            }
        }
        return indexNumber;
    }

    /**
     * 措施项目
     * @param resultList
     * @param pObj1
     * @param indexNumber
     * @param fbfxIndexNumber
     * @returns {Promise<void>}
     */
    async csxmList(resultList,pObj1,fbfxIndexNumber){
        for(let l = 0; l < resultList.length; l++){
            let ysshSysjSD = resultList[l];
            let ysshSysjSS = ysshSysjSD.ysshSysj;
            if(ObjectUtils.isNotEmpty(ysshSysjSS)){
                let changeTotal = 0;
                if(ObjectUtils.isNotEmpty(ysshSysjSS.changeTotal)){//没变动的没有  changeTotal
                    changeTotal = ysshSysjSS.changeTotal;
                }
                let name = " ";
                if(ObjectUtils.isNotEmpty(ysshSysjSD.name)){
                    name =  ysshSysjSD.name;
                }else if(ObjectUtils.isNotEmpty(ysshSysjSS.name)){
                    name =  ysshSysjSS.name;
                }
                let str =  await this.jgjs(changeTotal);
                if(ysshSysjSS.change === YsshssConstant.insert){
                    pObj1.addText(NumberUtil.add(l,fbfxIndexNumber)+"）增加"+name+"审增金额"+ysshSysjSS.changeTotal+"元");
                    pObj1.addLineBreak();
                    fbfxIndexNumber++;
                }
                if(ysshSysjSS.change === YsshssConstant.delete){
                    pObj1.addText(NumberUtil.add(l,fbfxIndexNumber)+"）删除"+name+"审删金额"+ysshSysjSS.changeTotal+"元");
                    pObj1.addLineBreak();
                    fbfxIndexNumber++;
                }
                if(ysshSysjSS.change === YsshssConstant.update){
                    let sdprice = 0
                    if(ObjectUtils.isNotEmpty(ysshSysjSD.price)){
                        sdprice =  ysshSysjSD.price
                    }
                    let ssprice = 0
                    if(ObjectUtils.isNotEmpty(ysshSysjSS.price)){
                        ssprice =  ysshSysjSS.price
                    }
                    pObj1.addText(NumberUtil.add(l,fbfxIndexNumber)+"）"+name+"送审综合单价"+ssprice+"元"+"，"+"审定综合单价"+sdprice+"元"+"，"+str+changeTotal+"元");
                    pObj1.addLineBreak();
                    fbfxIndexNumber++;
                }
            }
        }
        return fbfxIndexNumber;
    }

    /**
     * 其他项目
     * @param resultList
     * @param pObj1
     * @param indexNumber
     * @param fbfxIndexNumber
     * @param csxmIndexNumber
     * @returns {Promise<void>}
     */
    async qtxm(resultList,pObj1,indexNumber){
        let amount = 0;
        for(let l = 0; l < resultList.length; l++){
            let result = resultList[l].ysshSysj;
            let changeTotal = result.changeTotal;
            amount = NumberUtil.add(amount,changeTotal);
        }
        amount = NumberUtil.numberScale2(amount);
        let str =  await this.jgjs(amount);
        if (amount != 0) {
            pObj1.addText(indexNumber+"）其他项目，"+str+amount+"元");
            pObj1.addLineBreak();
            indexNumber++;
        }
        return indexNumber;
    }

    /**
     * 审增审减金额判断
     * @param je
     * @returns {Promise<string>}
     */
    async jgjs(je){
        let str = "";
        if (je>= 0) {
            str = "审增金额";
        } else if (je < 0) {
            str = '审减金额';
        }
        return str;
    }

    /**
     * 审增审减比例判断
     * @param bl
     * @returns {Promise<string>}
     */
    async bljs(bl){
        let str = "";
        if (bl>= 0) {
            str = "审增比例";
        } else if (bl < 0) {
            str = '审减比例';
        }
        return str;
    }

    /**
     * 审增率审减率判断
     * @param bl
     * @returns {Promise<string>}
     */
    async ljs(bl){
        let str = "";
        if (bl>= 0) {
            str = "审增率";
        } else if (bl < 0) {
            str = '审减率';
        }
        return str;
    }


    async readCreateWordData(arg) {
        let data =   fs.readFileSync(arg.baseDataDir);
        return data;
    }


    async downloadWordFile(arg) {
        let  sdProject = PricingFileFindUtils.getProjectObjById(arg.constructId);
        let projectName = sdProject.constructName;
        const dialogOptions = {
            title: '保存文件',
            defaultPath: projectName,
            filters: [{ name: 'docx', extensions: ['docx'] }]
        };
        //下载弹框
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        if (result && !result.canceled) {
            // 完整的目标文件路径
            const targetFilePath = path.join(result);
            // 复制文件
            fs.copyFile(arg.baseDataDir, targetFilePath, (err) => {
                if (err) {
                    console.error('复制word分析报告文件时出错:', err);
                    return ResponseData.fail("下载word分析报告文件时出错");
                }
                console.log('分析报告文件复制成功！');
            });
        }
        return ResponseData.success("下载成功");
    }


}
createWordShenHeService.toString = () => '[class createWordShenHeService]';
module.exports = createWordShenHeService;
