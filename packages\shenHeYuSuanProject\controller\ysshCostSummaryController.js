const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ResponseData} = require("../../../common/ResponseData");
const {Controller} = require("../../../core");


/**
 * 预算审核 - 费用汇总Controller
 */
class YsshCostSummaryController extends Controller {


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 获取费用汇总的数据对比结果集合
     * @return
     */
    async getCostSummaryComparisonList(args) {
        return await this.service.shenHeYuSuanProject.ysshCostSummaryService.getCostSummaryComparisonList(args);
    }

    /**
     * 对比匹配页面 分部分项数据重新挂关联关系
     *
     matchingId：送审明细数据的id
     detailId:  审定明细数据的id
     constructId: 审定项目id
     spId：  审定单项id
     unitId：审定单位id
     ssConstructId:  审定项目id
     ssSingleId：  审定单项id
     ssUnitId：审定单位id
     */
    async changeCostSummaryRelation (arg) {
        await this.service.shenHeYuSuanProject.ysshCostSummaryService.changeCostSummaryRelation(arg);
        return ResponseData.success(true);
    }

    /**
     * 获取规费明细的数据对比结果集合
     * @param args
     * @returns {Promise<*>}
     */
    async getGfeeFeeComparisonData(args) {
        const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
        const sdGfeeResult = await this.service.gfeeService.getGfeeFee({ constructId: constructId, singleId: singleId, unitId: unitId });

        if(ObjectUtils.isEmpty(ssUnitId)){
            const ssGfeeResult = null
            return await this.service.shenHeYuSuanProject.ysshCostSummaryService.getGfeeFeeComparisonData({ ssGfeeResult, sdGfeeResult });
        }else {
            const ssGfeeResult = await this.service.gfeeService.getGfeeFee({ constructId: ssConstructId, singleId: ssSingleId, unitId: ssUnitId });
            return await this.service.shenHeYuSuanProject.ysshCostSummaryService.getGfeeFeeComparisonData({ ssGfeeResult, sdGfeeResult });
        }

    }

    /**
     * 获取安文费的数据对比结果集合
     * @param args
     * @returns {Promise<*>}
     */
    async getSafeFeeComparisonData(args) {
        const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
        const sdSafeFeeResult = await this.service.safeFeeService.getSafeFee({ constructId: constructId, singleId: singleId, unitId: unitId });
        if(ObjectUtils.isEmpty(ssUnitId)){
            let ssSafeFeeResult =null;
            return await this.service.shenHeYuSuanProject.ysshCostSummaryService.getSafeFeeComparisonData({ ssSafeFeeResult, sdSafeFeeResult });
        }else {
            const ssSafeFeeResult = await this.service.safeFeeService.getSafeFee({ constructId: ssConstructId, singleId: ssSingleId, unitId: ssUnitId });
            return await this.service.shenHeYuSuanProject.ysshCostSummaryService.getSafeFeeComparisonData({ ssSafeFeeResult, sdSafeFeeResult });
        }


    }


}

YsshCostSummaryController.toString = () => '[class YsshCostSummaryController]';
module.exports = YsshCostSummaryController;

