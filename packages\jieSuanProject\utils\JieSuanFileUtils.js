const {dialog} = require("electron");
const JSZip = require("jszip");
const fs = require("fs");
const JieSuanConstantUtil = require("../enum/JieSuanConstantUtil");
const JieSuanCommonUtils = require("./JieSuanCommonUtils");
const {throws} = require("assert");
const path = require('path');
const { exec } = require('child_process');
const {DateUtils} = require("../../../electron/utils/DateUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");
const {CryptoUtils} = require("../../../electron/utils/CrypUtils");


/**
 * 项目文件操作
 */
class JieSuanFileUtils extends JieSuanCommonUtils{


    writeUserHistoryListFile(obj) {
        return ProjectFileUtils.writeUserHistoryListFile(obj);
    }


    /**
     * 打开系统级保存文件位置选择框
     */
    async openSaveFileWin(defaultPath){
        if (ObjectUtil.isEmpty(defaultPath)){
            throw new throws("文件路径不能为空");
        }
        const dialogOptions = {
            title: '保存文件',
            defaultPath: "C:\\Users\\<USER>\\Desktop\\hahhah.ysf",
            filters: [{ name: '云算房文件', extensions: ['ysf'] }],
        };
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        return filePath;
    }


    /**
     * 创建项目的本地文件
     * @param path 文件路径包含文件名以及后缀
     * @param fileData 文件内的数据
     */
    async createProjectFile(path,fileData){
        if (ObjectUtil.isEmpty(path) || ObjectUtil.isEmpty(fileData)){
            throw new throws("数据不能为空")
        }
        let keys = Object.keys(fileData);
        // 创建一个新的压缩包实例
        const zip = new JSZip();
        keys.forEach(k =>{
            // 添加数据到压缩包中
            zip.file(k, fileData[k]);
        });
        // 生成压缩包
        await zip.generateAsync({ type: 'nodebuffer' }).then(function (content) {
            fs.writeFileSync(path, content);
        }).catch(function (error) {
            console.error('创建压缩包时发生错误:', error);
        });
    }


    /**
     * 根据路径读取本地文件数据
     * @param path
     */
    async readProjectFile(path) {
        try {
            // // 读取压缩包文件
            const zipFileContent = fs.readFileSync(path);
            const zipObject = await JSZip.loadAsync(zipFileContent);
            // 获取所有文件名称
            const files = Object.keys(zipObject.files);
            let result ={};
            // 遍历每个文件并输出内容或进行其他操作
            for (let i = 0; i < files.length; i++) {
                const fileName = files[i];
                if (!zipObject.files[fileName].dir) {
                    // 读取指定文件的内容
                    const fileContent = await zipObject.file(fileName).async("string");
                    result[fileName] = fileContent;
                }
            }
            return result;
        } catch (error) {
            throw error;
        }
    }

    /**
     * 从本地读取项目数据
     * @param path
     * @return {Promise<{}>}
     */
    async readLocalProjectData(path) {
        let data = await this.readProjectFile(path);
        //解密
        let s = CryptoUtils.decryptAESData(data[JieSuanConstantUtil.JIESUAN_FILENAME]);
        return JSON.parse(s);
    }



    /**
     * 根据路径修改本地文件数据
     * @param path 文件路径
     * @param fileData 修改或添加文件内容
     */
    async updateProjectFile(path,fileData) {
        try {
            // 读取压缩包文件
            const zipFileContent = fs.readFileSync(path);
            const zipObject = await JSZip.loadAsync(zipFileContent);
            let keys = Object.keys(fileData);
            keys.forEach(k =>{
                // 添加数据到压缩包中
                zipObject.file(k, fileData[k]);
            });

            // 生成压缩包
            await zipObject.generateAsync({ type: 'nodebuffer' }).then(function (content) {
                fs.writeFileSync(path, content);
            }).catch(function (error) {
                console.error('创建压缩包时发生错误:', error);
            });
        } catch (error) {
            throw error;
        }
    }

    /**
     * 根据文件路径获取文件后缀
     * @param filePath
     */
    pathSuffix(filePath){
        if (ObjectUtil.isEmpty(filePath))throw new throws("文件路径不能为空");
        return  filePath.substring(filePath.lastIndexOf('.') + 1);
    }


    /**
     * 从本地读取动态列设置文件数据
     * @param path
     * @return {Promise<{}>}
     */
    async readLocalColumnData(path) {
        let data = await this.readProjectFile(path);
        return JSON.parse(data[JieSuanConstantUtil.JIESUAN_COLUMNNAME]);
    }

    /**
     * 文件复制
     * @param filePath1
     * @param filePath2
     * @returns {Promise<void>}
     */
    async copyFile(filePath1, filePath2) {
        if (!fs.existsSync(filePath2)) {
            fs.mkdirSync(filePath2, { recursive: true });
        }
        let fileName = path.basename(filePath1);
        let targetFilePath = path.join(filePath2, fileName);
        let readStream = fs.createReadStream(filePath1);
        let writeStream = fs.createWriteStream(targetFilePath);
        readStream.pipe(writeStream);
        writeStream.on('finish', () => {
        });
        writeStream.on('error', (err) => {
            console.error('复制文件时出错:', err);
        });
    }

    /**
     * 文件复制到YJS
     * @param filePath
     * @param yjsPath
     * @param yjsFilePath
     * @returns {Promise<void>}
     */
    async copyFile2YJS(filePath, yjsPath, yjsFilePath) {
        try {
            // 读取压缩包文件
            const zipFileContent = fs.readFileSync(yjsPath);
            const zipObject = await JSZip.loadAsync(zipFileContent);

            // 将文件内容写入压缩包中的指定目录
            const fileContent = fs.readFileSync(filePath);
            const fileDirectory = path.join(yjsFilePath, path.basename(filePath));
            zipObject.file(fileDirectory, fileContent);

            // 生成压缩包
            await zipObject.generateAsync({ type: 'nodebuffer' }).then(function (content) {
                fs.writeFileSync(yjsPath, content);
            }).catch(function (error) {
                console.error('创建压缩包时发生错误:', error);
            });
        } catch (error) {
            throw error;
        }
    }

    /**
     * 删除YJS文件
     * @param yjsPath
     * @param yjsFilePath
     * @returns {Promise<void>}
     */
    async deleteYJSFile(yjsPath, yjsFilePath) {
        try {
            // 读取压缩包文件
            const zipFileContent = fs.readFileSync(yjsPath);
            const zipObject = await JSZip.loadAsync(zipFileContent);

            // 删除指定文件
            delete zipObject.files[yjsFilePath];

            // 重新生成压缩包
            const content = await zipObject.generateAsync({ type: 'nodebuffer' });
            fs.writeFileSync(yjsPath, content);
        } catch (error) {
            throw error;
        }
    }

    /**
     * 打开文件
     * @param tempDir
     * @param yjsFilePath
     * @returns {Promise<void>}
     */
    async openAccordingFile(tempDir, yjsPath, yjsFilePath) {
        try {
            // 创建临时目录（如果不存在）
            if (!fs.existsSync(tempDir)) {
                fs.mkdirSync(tempDir, { recursive: true });
            }
            // 读取压缩包文件
            const zipFileContent = fs.readFileSync(yjsPath);
            const zipObject = await JSZip.loadAsync(zipFileContent);
            // 检查文件是否存在
            if (!zipObject.files[yjsFilePath]) {
                throw new Error(`${yjsFilePath} 文件不存在`);
            }
            // 提取文件内容
            const fileContent = await zipObject.file(yjsFilePath).async('nodebuffer');
            // 创建一个唯一的临时文件名
            const tempFileName = path.basename(yjsFilePath);
            const tempFilePath = path.join(tempDir, tempFileName);
            // 将文件内容写入临时文件
            fs.writeFileSync(tempFilePath, fileContent);
            // 打开文件
            exec(tempFilePath, (error, stdout, stderr) => {
                if (error) {
                    throw error;
                }
            });
        } catch (error) {
            throw error;
        }
    }

    /**
     * 获取文件信息
     * @param filePath
     * @returns {Promise<*>}
     */
    async getFileInfo(filePath) {
        return new Promise((resolve, reject) => {
            fs.stat(filePath, (err, stats) => {
                if (err) {
                    console.error('获取文件大小时出错:', err);
                    reject(err);
                    return;
                }
                let fileSize;
                if (stats.size >= 1000000) {
                    fileSize = `${(stats.size / 1000000).toFixed(2)}M`;
                } else {
                    fileSize = `${(stats.size / 1000).toFixed(2)}K`;
                }
                const fileInfo = {
                    fileName: path.basename(filePath),
                    fileSize: fileSize
                };
                resolve(fileInfo);
            });
        });
    }

    /**
     * 删除文件
     * @param filePath
     * @returns {Promise<*>}
     */
    async deleteFile(filePath) {
        fs.unlink(filePath, (err) => {
            if (err) {
                console.error('删除文件失败:', err);
                return;
            }
        });
    }

}

module.exports = {
    JieSuanFileUtils: new JieSuanFileUtils()
};

