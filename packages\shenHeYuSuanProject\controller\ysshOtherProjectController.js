const { Controller } = require('../../../core');


class YsshOtherProjectController extends Controller {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取 其他项目 的数据对比结果集合
   */
  async getOtherProjectComparisonList(args) {
    return await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectComparisonList(args);
  }

  /**
   * 获取 暂列金 的数据对比结果集合
   */
  async getOtherProjectProvisionalComparisonList(args) {
    return await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectProvisionalComparisonList(args);
  }

  /**
   * 获取 专业工程暂估价 的数据对比结果集合
   */
  async getOtherProjectZygczgjComparisonList(args) {
    return await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectZygczgjComparisonList(args);
  }

  /**
   * 获取 总承包服务费 的数据对比结果集合
   */
  async getOtherProjectServiceCostComparisonList(args) {
    return await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectServiceCostComparisonList(args);
  }

  /**
   * 获取 计日工 的数据对比结果集合
   */
  async getOtherProjectDayWorkComparisonList(args) {
    return await this.service.shenHeYuSuanProject.ysshOtherProjectService.getOtherProjectDayWorkComparisonList(args);
  }

  /**
   * 修改其他项目中的匹配关联关系
   */
  async updateMatch(args) {
    return await this.service.shenHeYuSuanProject.ysshOtherProjectService.updateMatch(args);
  }

}

YsshOtherProjectController.toString = () => '[class YsshOtherProjectController]';
module.exports = YsshOtherProjectController;