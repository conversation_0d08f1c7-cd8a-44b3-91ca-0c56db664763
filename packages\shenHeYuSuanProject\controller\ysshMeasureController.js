const {ResponseData} = require("../../../common/ResponseData");
const {Controller} = require("../../../core");


/**
 * 【预算审核】措施项目Controller
 */
class YsshMeasureController extends Controller {

    constructor(ctx) {
        super(ctx);


    }

    async changeMeasureRelation(args){
        return await this.service.shenHeYuSuanProject.ysshMeasureService.changeMeasureRelation(args);
    }



    /**
     * 对比结果列表
     * @param args
     */
    async listSearch(args) {
        //  const {ssConstructId, ssSingleId, ssUnitId, sdConstructId, sdSingleId, sdUnitId, sequenceNbr, pageNum, pageSize, isAllFlag, type} = args;
        let resultList = await this.service.shenHeYuSuanProject.ysshMeasureService.listSearch(args);
        let result = {};
        result.data = resultList;
        return ResponseData.success(result);
    }

    /**
     * 展开
     */
    async open(args) {
        // let {ssConstructId, ssSingleId, ssUnitId, sdConstructId, sdSingleId, sdUnitId, pointLine} = args;
        // const result = this.service.shenHeYuSuanProject.stepItemCostService.open(constructId, singleId, unitId, pointLine);
        let resultList = await this.service.shenHeYuSuanProject.ysshMeasureService.open(args);
        return ResponseData.success(true);
    }

    /**
     * 折叠
     */
    async close(args) {
        // let {ssConstructId, ssSingleId, ssUnitId, sdConstructId, sdSingleId, sdUnitId, pointLine} = args;
        // const result = this.service.shenHeYuSuanProject.stepItemCostService.close(constructId, singleId, unitId, pointLine);
        let resultList = await this.service.shenHeYuSuanProject.ysshMeasureService.close(args);
        return ResponseData.success(true);
    }


    // /**
    //  * 【数据转换】批量数据审定转送审
    //  * @param args:审核数据列表
    //  */
    // async sdToSsBatch(args) {
    //     await this.service.shenHeYuSuanProject.ysshMeasureService.sdToSsBatch(args);
    //     return ResponseData.success();
    // }
    //
    // /**
    //  * 【数据转换】批量数据送审转审定
    //  * @param args:审核数据列表
    //  */
    // async ssToSdBatch(args) {
    //     await this.service.shenHeYuSuanProject.ysshMeasureService.ssToSdBatch(args);
    //     return ResponseData.success();
    // }

}
YsshMeasureController.toString = () => '[class YsshMeasureController]';
module.exports = YsshMeasureController;
