
const {Service} = require("../../../core");
const {CostAnalysisVO} = require( "../../../electron/model/CostAnalysisVO");
const {PricingFileFindUtils} = require( "../../../electron/utils/PricingFileFindUtils");
const ConstructBiddingTypeConstant = require( "../../../electron/enum/ConstructBiddingTypeConstant");
const ProjectLevelConstant = require( "../../../electron/enum/ProjectLevelConstant");
const {CostAnalysisUnitVO} = require( "../../../electron/model/CostAnalysisUnitVO");
const {ConvertUtil} = require( "../../../electron/utils/ConvertUtils");
const {ObjectUtils} = require( "../../../electron/utils/ObjectUtils");


const jiesuan_zjfx_ybjs_htw = require("../jsonData/jiesuan_zjfx_ybjs_htw.json");
const jiesuan_zjfx_ybjs_htn = require("../jsonData/jiesuan_zjfx_ybjs_htn.json");
const jiesuan_zjfx_jyjs_htw = require("../jsonData/jiesuan_zjfx_jyjs_htw.json");
const jiesuan_zjfx_jyjs_htn = require("../jsonData/jiesuan_zjfx_jyjs_htn.json");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {CostAnalysisSingleVO} = require("../../../electron/model/CostAnalysisSingleVO");

class jieSuanUnitProjectService extends Service{
    /**
     * 获取造价分析
     * @param args
     * @returns {Promise<CostAnalysisVO>}
     */
    async getCostAnalysisData(args) {

        let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
        //let taxMode = projectObj.projectTaxCalculation.taxCalculationMethod;
        //返回对象
        let costAnalysisVO = new CostAnalysisVO();

        if (ConstructBiddingTypeConstant.unitProject === projectObj.biddingType) {
            //单位工程项目
            let costAnalysisUnitVOList = await this.generateUnitCostAnalysisData(args.constructId, args.singleId, args.unitId)
            costAnalysisVO.costAnalysisUnitVOList = costAnalysisUnitVOList;
        } else {
            //招标和投标项目
            if(ProjectLevelConstant.construct === args.levelType ){
                // 工程项目层级
                let costAnalysisVOList = await this.generateConstructCostAnalysisData(projectObj);
                costAnalysisVO.costAnalysisConstructVOList = costAnalysisVOList;
            }else if(ProjectLevelConstant.single === args.levelType){
                // 单项层级
                // let singleProject = projectObj.singleProjects.find((item ) => item.sequenceNbr === args.singleId);

                let singleProject = await PricingFileFindUtils.getOneFromSingleProjects(projectObj.singleProjects,args.singleId);
                costAnalysisVO.costAnalysisSingleVOList = await this.getCurrentSingleCostAnalysisData(singleProject,"1");
            }else if(ProjectLevelConstant.unit === args.levelType){
                // 单位层级
                let costAnalysisUnitVOList = await this.generateUnitCostAnalysisData(args.constructId,args.singleId, args.unitId)
                costAnalysisVO.costAnalysisUnitVOList = costAnalysisUnitVOList;
            }
        }
        //增加固定安文费
        if(ObjectUtils.isEmpty(args.unitId) && ObjectUtils.isNotEmpty(costAnalysisVO.costAnalysisConstructVOList)){
            let projectObj = await PricingFileFindUtils.getProjectObjById(args.constructId);
            let costAnalysisSingleVO = new CostAnalysisSingleVO();
            costAnalysisSingleVO.projectName = "安全生产、文明施工费（含税）";
            costAnalysisSingleVO.contractPrice = projectObj.securityFee;
            costAnalysisSingleVO.jieSuanPrice = projectObj.jieSuanSecurityFee;
            costAnalysisSingleVO.dispNo = costAnalysisVO.costAnalysisConstructVOList.length+1;
            costAnalysisVO.costAnalysisConstructVOList.push(costAnalysisSingleVO);
        }
        return costAnalysisVO;
    }


    /**
     * 工程项目造价分析
     */
    async generateConstructCostAnalysisData(projectObj){
        let singleProjects = projectObj.singleProjects;
        let costAnalysisVOList = new Array();
        // 获取工程项目下所有的单项造价分析数据
        if(singleProjects){
            for (let i =0 ;i<singleProjects.length ;i++) {
                //获取单项工程对应的造价分析
                let singleCostAnalysis =await this.getCurrentSingleCostAnalysisData(singleProjects[i],i+1+"");
                costAnalysisVOList.push(singleCostAnalysis);
            }
        }
        return costAnalysisVOList;
    }




    /**
     * 得到当前子单项的造价分析数据
     * @param singleObj
     * @param dispNo  当前单项的序号
     * @returns {Promise<CostAnalysisSingleVO|*[]>}
     */
    async getCurrentSingleCostAnalysisData(singleObj,dispNo){

        //遍历到最底层有单位的子单项  返回该子单项
        if (singleObj.unitProjects != null && singleObj.unitProjects.length > 0) {
            return await this.getCostAnalysisDataContainUnits(singleObj,dispNo);
        }
        let singleProjects = singleObj.subSingleProjects;
        //如果单项既没有单位也没有子单项  说明为空
        if (singleProjects == null || singleProjects.length == 0) {
            return await this.getCostAnalysisDataContainUnits(singleObj,dispNo);
        }
        //对于中间的子单项  进行累计
        let dataArray = [];
        let subDispNo = 0;
        for (let i = 0; i < singleProjects.length; i++) {
            subDispNo = dispNo+"."+(i+1);
            //拿到子单项以后 进行该一层级的累计  最后返回该一层级的单项数据
            let subSingleData = await this.getCurrentSingleCostAnalysisData(singleProjects[i],subDispNo);
            dataArray.push(subSingleData);
        }
        return await this.getSubSingleCostAnalysisDataTotal(dataArray,dispNo,singleObj);
    }



    /**
     * 对同一层级的子单项造价分析数据进行汇总
     * @param subSingleProjects
     * @param dispNo
     * @returns {Promise<any[]>} 返回该单项及子单项的造价分析数据
     */
    async getSubSingleCostAnalysisDataTotal(subSingleProjects,dispNo,singleObj){
        //存放单项工程数据
        let singleCostAnalysisVO= new CostAnalysisSingleVO();
        singleCostAnalysisVO.dispNo = dispNo;
        singleCostAnalysisVO.projectName = singleObj.projectName;
        singleCostAnalysisVO.sequenceNbr = singleObj.sequenceNbr;
        let gczj_ys         = 0;
        let gczj         = 0;
        let gczjsbsj         = 0;
        let fbfxhj          = 0;
        let csxhj           = 0;
        let qtxmhj          = 0;
        let gfee            = 0;
        let safeFee         = 0;
        let jxse            = 0;
        let xxse            = 0;
        let zzsynse         = 0;
        let fjse            = 0;
        let sj              = 0;
        let sbfsj           = 0;
        let jsjc            = 0;
        let jsjcrgf         = 0;
        let jsjcclf         = 0;
        let jsjcjxf         = 0;
        let jcgfhj          = 0;
        let jcaqwmsgfhj     = 0;
        let jcjxse          = 0;
        let jcg             = 0;
        let jch             = 0;
        let jci             = 0;
        let jcj             = 0;
        let jsjcsbf         = 0;
        let jck             = 0;
        let k               = 0;
        let jsjczgj         = 0;
        let jcs             = 0;
        let jieSuanPrice    = 0;
        let contractPrice   = 0;
        //存放所有单位对应的造价分析
        if(!ObjectUtils.isEmpty(subSingleProjects)){
            for (let j =0 ;j<subSingleProjects.length ;j++) {
                let subSingleProject = subSingleProjects[j];
                //单项合计计算
                gczj   =NumberUtil.add(gczj      ,subSingleProject.gczj)
                gczj_ys   =NumberUtil.add(gczj_ys      ,subSingleProject.gczj_ys)
                gczjsbsj   =NumberUtil.add(gczjsbsj      ,subSingleProject.gczjsbsj)
                fbfxhj=NumberUtil.add(fbfxhj       ,subSingleProject.fbfxhj)
                csxhj=NumberUtil.add(csxhj        ,subSingleProject.csxhj)
                qtxmhj=NumberUtil.add(qtxmhj       ,subSingleProject.qtxmhj)
                gfee=NumberUtil.add(gfee         ,subSingleProject.gfee)
                safeFee=NumberUtil.add(safeFee      ,subSingleProject.safeFee)
                jxse=NumberUtil.add(jxse         ,subSingleProject.jxse)
                xxse=NumberUtil.add(xxse         ,subSingleProject.xxse)
                zzsynse=NumberUtil.add(zzsynse      ,subSingleProject.zzsynse)
                fjse=NumberUtil.add(fjse         ,subSingleProject.fjse)
                sj=NumberUtil.add(sj           ,subSingleProject.sj)
                sbfsj=NumberUtil.add(sbfsj        ,subSingleProject.sbfsj)
                jsjc=NumberUtil.add(jsjc         ,subSingleProject.jsjc)
                jsjcrgf=NumberUtil.add(jsjcrgf      ,subSingleProject.jsjcrgf)
                jsjcclf=NumberUtil.add(jsjcclf      ,subSingleProject.jsjcclf)
                jsjcjxf=NumberUtil.add(jsjcjxf      ,subSingleProject.jsjcjxf)
                jcgfhj=NumberUtil.add(jcgfhj       ,subSingleProject.jcgfhj)
                jcaqwmsgfhj=NumberUtil.add(jcaqwmsgfhj  ,subSingleProject.jcaqwmsgfhj)
                jcjxse=NumberUtil.add(jcjxse       ,subSingleProject.jcjxse)
                jcg=NumberUtil.add(jcg          ,subSingleProject.jcg)
                jch=NumberUtil.add(jch          ,subSingleProject.jch)
                jci=NumberUtil.add(jci          ,subSingleProject.jci)
                jcj=NumberUtil.add(jcj          ,subSingleProject.jcj)
                jsjcsbf=NumberUtil.add(jsjcsbf      ,subSingleProject.jsjcsbf )
                jck=NumberUtil.add(jck          ,subSingleProject.jck)
                k=NumberUtil.add(k            ,subSingleProject.k)
                jsjczgj=NumberUtil.add(jsjczgj      ,subSingleProject.jsjczgj)
                jcs=NumberUtil.add(jcs          ,subSingleProject.jcj)
                jieSuanPrice=NumberUtil.add(jieSuanPrice ,subSingleProject.jck)
                contractPrice=NumberUtil.add(contractPrice,subSingleProject.k)
            }
        }
        singleCostAnalysisVO.gczj_ys         =gczj_ys
        singleCostAnalysisVO.gczj         =gczj
        singleCostAnalysisVO.gczjsbsj         =gczjsbsj
        singleCostAnalysisVO.fbfxhj          =fbfxhj
        singleCostAnalysisVO.csxhj           =csxhj
        singleCostAnalysisVO.qtxmhj          =qtxmhj
        singleCostAnalysisVO.gfee            =gfee
        singleCostAnalysisVO.safeFee         =safeFee
        singleCostAnalysisVO.jxse            =jxse
        singleCostAnalysisVO.xxse            =xxse
        singleCostAnalysisVO.zzsynse         =zzsynse
        singleCostAnalysisVO.fjse            =fjse
        singleCostAnalysisVO.sj              =sj
        singleCostAnalysisVO.sbfsj           =sbfsj
        singleCostAnalysisVO.jsjc            =jsjc
        singleCostAnalysisVO.jsjcrgf         =jsjcrgf
        singleCostAnalysisVO.jsjcclf         =jsjcclf
        singleCostAnalysisVO.jsjcjxf         =jsjcjxf
        singleCostAnalysisVO.jcgfhj          =jcgfhj
        singleCostAnalysisVO.jcaqwmsgfhj     =jcaqwmsgfhj
        singleCostAnalysisVO.jcjxse          =jcjxse
        singleCostAnalysisVO.jcg             =jcg
        singleCostAnalysisVO.jch             =jch
        singleCostAnalysisVO.jci             =jci
        singleCostAnalysisVO.jcj             =jcj
        singleCostAnalysisVO.jsjcsbf         =jsjcsbf
        singleCostAnalysisVO.jck             =jck
        singleCostAnalysisVO.k               =k
        singleCostAnalysisVO.jsjczgj         =jsjczgj
        singleCostAnalysisVO.jcs             =jcs
        singleCostAnalysisVO.jieSuanPrice    =jieSuanPrice
        singleCostAnalysisVO.contractPrice   =contractPrice
        singleCostAnalysisVO.childrenList= subSingleProjects;
        singleCostAnalysisVO.levelType = ProjectLevelConstant.single;

        return singleCostAnalysisVO;

    }

    async getCostAnalysisDataContainUnits(singleObj,dispNo) {
        //存放单项工程数据
        let singleCostAnalysisVO= new CostAnalysisSingleVO();
        singleCostAnalysisVO.dispNo = dispNo;
        singleCostAnalysisVO.projectName = singleObj.projectName;
        singleCostAnalysisVO.sequenceNbr = singleObj.sequenceNbr;
        let unitProjects = singleObj.unitProjects;
        let gczj         = 0;
        let gczj_ys         = 0; //合同金额
        let gczjsbsj         = 0;
        let fbfxhj          = 0;
        let csxhj           = 0;
        let qtxmhj          = 0;
        let gfee            = 0;
        let safeFee         = 0;
        let jxse            = 0;
        let xxse            = 0;
        let zzsynse         = 0;
        let fjse            = 0;
        let sj              = 0;
        let sbfsj           = 0;
        let jsjc            = 0;
        let jsjcrgf         = 0;
        let jsjcclf         = 0;
        let jsjcjxf         = 0;
        let jcgfhj          = 0;
        let jcaqwmsgfhj     = 0;
        let jcjxse          = 0;
        let jcg             = 0;
        let jch             = 0;
        let jci             = 0;
        let jcj             = 0;
        let jsjcsbf         = 0;
        let jck             = 0;
        let k               = 0;
        let jsjczgj         = 0;
        let jcs             = 0;
        let jieSuanPrice    = 0;
        let contractPrice   = 0;

        //存放所有单位对应的造价分析
        let unitCostAnalysis =await this.generateSingleCostAnalysisData(unitProjects,singleCostAnalysisVO.dispNo);
        if(!ObjectUtils.isEmpty(unitProjects)) {
            for (let j =0 ;j<unitProjects.length ;j++){
                let unitProject =unitProjects[j];
                let unitCostSummarys = unitProject.unitCostSummarys;
                let JCG = unitCostSummarys.find(summary => summary.type === '价差销项税额');
                let JCH = unitCostSummarys.find(summary => summary.type === '价差增值税应纳税额')
                let JCI = unitCostSummarys.find(summary => summary.type === '价差附加税费')
                let JCJ = unitCostSummarys.find(summary => summary.type === '价差税金')
                let JCK = unitCostSummarys.find(summary => summary.type === '调差后工程造价')
                gczj_ys   =NumberUtil.add(gczj_ys      ,unitProject.gczj_ys)
                gczj   =NumberUtil.add(gczj      ,unitProject.gczj)
                gczjsbsj   =NumberUtil.add(gczjsbsj      ,unitProject.gczjsbsj)
                fbfxhj=NumberUtil.add(fbfxhj       ,unitProject.fbfxhj)
                csxhj=NumberUtil.add(csxhj        ,unitProject.csxhj)
                qtxmhj=NumberUtil.add(qtxmhj       ,unitProject.qtxmhj)
                gfee=NumberUtil.add(gfee         ,unitProject.gfee)
                safeFee=NumberUtil.add(safeFee      ,unitProject.safeFee)
                jxse=NumberUtil.add(jxse         ,unitProject.jxse)
                xxse=NumberUtil.add(xxse         ,unitProject.xxse)
                zzsynse=NumberUtil.add(zzsynse      ,unitProject.zzsynse)
                fjse=NumberUtil.add(fjse         ,unitProject.fjse)
                sj=NumberUtil.add(sj           ,unitProject.sj)
                sbfsj=NumberUtil.add(sbfsj        ,unitProject.sbfsj)
                jsjc=NumberUtil.add(jsjc         ,unitProject.jsjc)
                jsjcrgf=NumberUtil.add(jsjcrgf      ,unitProject.jsjcrgf)
                jsjcclf=NumberUtil.add(jsjcclf      ,unitProject.jsjcclf)
                jsjcjxf=NumberUtil.add(jsjcjxf      ,unitProject.jsjcjxf)
                jcgfhj=NumberUtil.add(jcgfhj       ,unitProject.jcgfhj)
                jcaqwmsgfhj=NumberUtil.add(jcaqwmsgfhj  ,unitProject.jcaqwmsgfhj)
                jcjxse=NumberUtil.add(jcjxse       ,unitProject.jcjxse)
                jcg=NumberUtil.add(jcg          ,this._getUnitCostSummaryPrice(JCG))
                jch=NumberUtil.add(jch          ,this._getUnitCostSummaryPrice(JCH))
                jci=NumberUtil.add(jci          ,this._getUnitCostSummaryPrice(JCI))
                jcj=NumberUtil.add(jcj          ,this._getUnitCostSummaryPrice(JCJ))
                jsjcsbf=NumberUtil.add(jsjcsbf      ,unitProject.jsjcsbf )
                jck=NumberUtil.add(jck          ,this._getUnitCostSummaryPrice(JCK))
                jsjczgj=NumberUtil.add(jsjczgj      ,unitProject.jsjczgj)
                jcs=NumberUtil.add(jcs          ,this._getUnitCostSummaryPrice(JCJ))
                jieSuanPrice=NumberUtil.add(jieSuanPrice ,this._getUnitCostSummaryPrice(JCK))
            }
        }
        singleCostAnalysisVO.gczj         =gczj
        singleCostAnalysisVO.gczj_ys         =gczj_ys
        singleCostAnalysisVO.gczjsbsj         =gczjsbsj
        singleCostAnalysisVO.fbfxhj          =fbfxhj
        singleCostAnalysisVO.csxhj           =csxhj
        singleCostAnalysisVO.qtxmhj          =qtxmhj
        singleCostAnalysisVO.gfee            =gfee
        singleCostAnalysisVO.safeFee         =safeFee
        singleCostAnalysisVO.jxse            =jxse
        singleCostAnalysisVO.xxse            =xxse
        singleCostAnalysisVO.zzsynse         =zzsynse
        singleCostAnalysisVO.fjse            =fjse
        singleCostAnalysisVO.sj              =sj
        singleCostAnalysisVO.sbfsj           =sbfsj
        singleCostAnalysisVO.jsjc            =jsjc
        singleCostAnalysisVO.jsjcrgf         =jsjcrgf
        singleCostAnalysisVO.jsjcclf         =jsjcclf
        singleCostAnalysisVO.jsjcjxf         =jsjcjxf
        singleCostAnalysisVO.jcgfhj          =jcgfhj
        singleCostAnalysisVO.jcaqwmsgfhj     =jcaqwmsgfhj
        singleCostAnalysisVO.jcjxse          =jcjxse
        singleCostAnalysisVO.jcg             =jcg
        singleCostAnalysisVO.jch             =jch
        singleCostAnalysisVO.jci             =jci
        singleCostAnalysisVO.jcj             =jcj
        singleCostAnalysisVO.jsjcsbf         =jsjcsbf
        singleCostAnalysisVO.jck             =jck
        singleCostAnalysisVO.k               =k
        singleCostAnalysisVO.jsjczgj         =jsjczgj
        singleCostAnalysisVO.jcs             =jcs
        singleCostAnalysisVO.jieSuanPrice    =jieSuanPrice
        singleCostAnalysisVO.contractPrice   =contractPrice
        singleCostAnalysisVO.childrenList= unitCostAnalysis;
        singleCostAnalysisVO.levelType = ProjectLevelConstant.single;




        return singleCostAnalysisVO;
    }



    /**
     * 获取单项下的工程项目
     * @param unitProjects
     * @returns {Promise<CostAnalysisSingleVO[]>}
     */
    async generateSingleCostAnalysisData(unitProjects,singDispNo){
        //存放所有单位对应的造价分析
        let unitCostAnalysis = new Array();
        if(!ObjectUtils.isEmpty(unitProjects)){
            for (let j =0 ;j<unitProjects.length ;j++) {
                let costAnalysisSingleVO = new CostAnalysisSingleVO();
                let unitProject = unitProjects[j];
                let unitCostSummarys = unitProject.unitCostSummarys;
                let jcg = unitCostSummarys.find(summary => summary.type === '价差销项税额');
                let jch = unitCostSummarys.find(summary => summary.type === '价差增值税应纳税额')
                let jci = unitCostSummarys.find(summary => summary.type === '价差附加税费')
                let jcj = unitCostSummarys.find(summary => summary.type === '价差税金')
                let jck = unitCostSummarys.find(summary => summary.type === '调差后工程造价')
                costAnalysisSingleVO.gczj_ys                = unitProject.gczj_ys
                costAnalysisSingleVO.gczj                = unitProject.gczj
                costAnalysisSingleVO.gczjsbsj              = unitProject.gczjsbsj
                costAnalysisSingleVO.fbfxhj               = unitProject.fbfxhj
                costAnalysisSingleVO.csxhj                = unitProject.csxhj
                costAnalysisSingleVO.qtxmhj               = unitProject.qtxmhj
                costAnalysisSingleVO.gfee                 = unitProject.gfee
                costAnalysisSingleVO.safeFee              = unitProject.safeFee
                costAnalysisSingleVO.jxse                 = unitProject.jxse
                costAnalysisSingleVO.xxse                 = unitProject.xxse
                costAnalysisSingleVO.zzsynse              = unitProject.zzsynse
                costAnalysisSingleVO.fjse                 = unitProject.fjse
                costAnalysisSingleVO.sj                   = unitProject.sj
                costAnalysisSingleVO.sbfsj                = unitProject.sbfsj
                costAnalysisSingleVO.jsjc                 = unitProject.jsjc
                costAnalysisSingleVO.jsjcrgf              = unitProject.jsjcrgf
                costAnalysisSingleVO.jsjcclf              = unitProject.jsjcclf
                costAnalysisSingleVO.jsjcjxf              = unitProject.jsjcjxf
                costAnalysisSingleVO.jcgfhj               = unitProject.jcgfhj
                costAnalysisSingleVO.jcaqwmsgfhj          = unitProject.jcaqwmsgfhj
                costAnalysisSingleVO.jcjxse               = unitProject.jcjxse
                costAnalysisSingleVO.jcg                  = this._getUnitCostSummaryPrice(jcg) // 人材机调离\价差销项税额
                costAnalysisSingleVO.jch                  = this._getUnitCostSummaryPrice(jch) // 人材机调离\价差增值税应纳税额
                costAnalysisSingleVO.jci                  = this._getUnitCostSummaryPrice(jci) // 人材机调整\价差附加税费
                costAnalysisSingleVO.jcj                  = this._getUnitCostSummaryPrice(jcj) // 人材机调\价差税金
                costAnalysisSingleVO.jsjcsbf              = unitProject.jsjcsbf
                costAnalysisSingleVO.jck                  = this._getUnitCostSummaryPrice(jck)
                costAnalysisSingleVO.jsjczgj              = unitProject.jsjczgj
                costAnalysisSingleVO.jcs                  = this._getUnitCostSummaryPrice(jcj)
                costAnalysisSingleVO.jieSuanPrice         = this._getUnitCostSummaryPrice(jck)
                costAnalysisSingleVO.levelType      = ProjectLevelConstant.unit;
                costAnalysisSingleVO.projectName    = unitProject.upName;
                costAnalysisSingleVO.sequenceNbr    = unitProject.sequenceNbr;
                if(singDispNo!== null){
                    costAnalysisSingleVO.dispNo = singDispNo+'.'+(j+1)
                }else {
                    costAnalysisSingleVO.dispNo = (j+1)
                }
                unitCostAnalysis.push(costAnalysisSingleVO);
            }
        }
        return unitCostAnalysis;
    }

    /**
     * 获取结算单位工程造价分析
     * @param taxMode
     * @param constructId
     * @param singleId
     * @param unitId
     * @returns {Promise<CostAnalysisUnitVO[]>}
     */
    async generateUnitCostAnalysisData(constructId, singleId, unitId) {
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        let unitCostSummarys = unit.unitCostSummarys;
        let taxMode = unit.projectTaxCalculation.taxCalculationMethod;
        let array = new Array();
        //一般计税
        if (taxMode == TaxCalculationMethodEnum.GENERAL.code) {
            // 合同内
            if (unit.originalFlag) {
                for (let i in jiesuan_zjfx_ybjs_htn) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(jiesuan_zjfx_ybjs_htn[i], obj);
                    array.push(obj);
                }
            } else { // 合同外
                for (let i in jiesuan_zjfx_ybjs_htw) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(jiesuan_zjfx_ybjs_htw[i], obj);
                    array.push(obj);
                }
            }
        } else {
            // 合同内
            if (unit.originalFlag) {
                for (let i in jiesuan_zjfx_jyjs_htn) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(jiesuan_zjfx_jyjs_htn[i], obj)
                    array.push(obj);
                }
            } else { // 合同外
                for (let i in jiesuan_zjfx_jyjs_htw) {
                    let obj = new CostAnalysisUnitVO();
                    ConvertUtil.setDstBySrc(jiesuan_zjfx_jyjs_htw[i], obj)
                    array.push(obj);
                }
            }
        }
        let contexts = new Map();
        let jcg = unitCostSummarys.find(summary => summary.type === '价差销项税额');
        let jch = unitCostSummarys.find(summary => summary.type === '价差增值税应纳税额')
        let jci = unitCostSummarys.find(summary => summary.type === '价差附加税费')
        let jcj = unitCostSummarys.find(summary => summary.type === '价差税金')
        let jck = unitCostSummarys.find(summary => summary.type === '调差后工程造价')
        let k = unitCostSummarys.find(summary => summary.type === 'K')

        contexts.set("gczj", unit.gczj) // 结算金额(不含人材机调整\结算合计(不含设备费及其税金)
        contexts.set("fbfxhj", unit.fbfxhj) // 结算金额(不含人材机调整)\分部分项
        contexts.set("csxhj", unit.csxhj) // 结算金额(不含人材机调整)\措施项目
        contexts.set("qtxmhj", unit.qtxmhj) // 结算金额(不含人材机调整)\其他项目
        contexts.set("gfee", unit.gfee) // 结算金额(不含人材机调整)\规费
        contexts.set("safeFee", unit.safeFee) // 结算金额(不含人材机调整)\安全、生产文明施工费
        contexts.set("jxse", unit.jxse) // 结算金额(不含人材机调整)\进项税额
        contexts.set("xxse", unit.xxse) // 结算金额(不含人材机调整)\销项税额
        contexts.set("zzsynse", unit.zzsynse) // 结算金额(不含人材机调整)\增值税应纳税额
        contexts.set("fjse", unit.fjse) // 结算金额(不含人材机调整)\附加税费
        contexts.set("sj", unit.sj) // 结算金额(不含人材机调整)\税金
        contexts.set("sbfsj", unit.sbfsj) // 结算金额(不含人材机调整)\设备费及其税金
        contexts.set("jsjc", unit.jsjc) // 人材机调整\合计(不含设备费及其税金)
        contexts.set("jsjcrgf", unit.jsjcrgf) // 人材机调整\人工价差
        contexts.set("jsjcclf", unit.jsjcclf) // 人材机调整\材料价差
        contexts.set("jsjcjxf", unit.jsjcjxf) // 人材机调整\机械价差
        contexts.set("jcgfhj", unit.jcgfhj) // 人材机调整\价差规费
        contexts.set("jcaqwmsgfhj", unit.jcaqwmsgfhj) // 人材机调整\价差安全、生产文明施工费
        contexts.set("jcjxse", unit.jcjxse) // 人材机调整\价差进项税额
        contexts.set("jcg", ObjectUtils.isEmpty(jcg)? null : jcg.price) // 人材机调离\价差销项税额
        contexts.set("jch", ObjectUtils.isEmpty(jch)? null : jch.price) // 人材机调离\价差增值税应纳税额
        contexts.set("jci", ObjectUtils.isEmpty(jci)? null : jci.price) // 人材机调整\价差附加税费
        contexts.set("jcj", ObjectUtils.isEmpty(jcj)? null : jcj.price) // 人材机调\价差税金
        contexts.set("jsjcsbf", unit.jsjcsbf) // 人材机调整\设备费及其税金
        contexts.set("jck", ObjectUtils.isEmpty(jck)? null : jck.price) // 结算金额(含人材机调整、不含设备费及其税金）  //工程造价调差后
        contexts.set("k", ObjectUtils.isEmpty(k)? null : k.price) // 合同金额 (不含设备费及其税金)    //工程造价
        contexts.set("jsjczgj", unit.jsjczgj) // 人材机调整\暂估材料价差

        // TODO zxj 字段名优化
        contexts.set("jcs", ObjectUtils.isEmpty(jcj)? null : jcj.price) // 人材机调\价差税金
        contexts.set("jieSuanPrice", ObjectUtils.isEmpty(jck)? null : jck.price) // 结算金额(含人材机调整、不含设备费及其税金）  //工程造价调差后
        contexts.set("contractPrice", ObjectUtils.isEmpty(k)? null : k.price) // 合同金额 (不含设备费及其税金)    //工程造价

        for (const item of array) {
            let value = contexts.get(item.field);
            item.context = ObjectUtils.isEmpty(value) ? 0 : value;
        }

        let result = new Array();
        let parentItem = new CostAnalysisUnitVO();
        for (const item of array) {
            if (item.dispNo.toString().indexOf(".") === -1) {
                //说明是父级
                parentItem = item;
                parentItem.childrenList = new Array();
                result.push(parentItem)
            } else {
                //子集
                parentItem.childrenList.push(item)
            }
        }
        return result;
    }

    /**
     * 造价分析校验
     * @param unitCostSummary
     * @returns {number|*}
     * @private
     */
    _getUnitCostSummaryPrice(unitCostSummary) {
        if (ObjectUtils.isEmpty(unitCostSummary) || ObjectUtils.isEmpty(unitCostSummary.price) || Number.isNaN(unitCostSummary.price)) {
            return 0;
        } else {
            return unitCostSummary.price;
        }
    }


    async updateAllProjectSecurity(constructId, securityFee, unitList) {

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(projectObjById)) {
            return ResponseData.fail("获取单位工程失败");
        }
        unitList = await this.service.securityFeeProjectService.queryAllProjectSecurity(constructId);
        for (let unit of unitList.datas) {
            if ("dw" === unit.type) {
                let qdByDjcs = PricingFileFindUtils.getUnit(constructId, unit.parentId, unit.sequenceNbr);
                let measureProjectTables = qdByDjcs.measureProjectTables;

                // 安文费总价措施参数缓存修改
                if (ObjectUtils.isNotEmpty(qdByDjcs.zjcsCostMathCache) && ObjectUtils.isNotEmpty(qdByDjcs.zjcsCostMathCache.data)) {
                    let data = qdByDjcs.zjcsCostMathCache.data;
                    if (!ObjectUtils.isEmpty(data)) {
                        for (let datum of data) {
                            if (datum.zjcsClassCode === "0") {
                                datum.isCheck = 0;
                                break;
                            }
                        }
                    }
                }

                // 安文费有删除  需要获取对应的清单做汇总
                let changeDeIds = new Set();
                // 获取到安文费清单列
                let find = measureProjectTables.find(a => a.zjcsClassCode == "0");
                let filter = measureProjectTables.filter(p => p.parentId === find.sequenceNbr);
                if (ObjectUtils.isNotEmpty(filter)) {
                    changeDeIds.add(find.sequenceNbr);
                    for (let item of filter) {
                        qdByDjcs.measureProjectTables.removeNode(item.sequenceNbr);
                    }
                }

                // let measureProjectTablesArray = treeToArray(measureProjectTables);
                // for (let i = 0; i < measureProjectTablesArray.length; i++) {
                //     let measureProjectTable = measureProjectTablesArray[i];
                //     if (find.sequenceNbr === measureProjectTable.parentId) {
                //         measureProjectTablesArray.splice(i, 1);
                //         i--;
                //     }
                // }
                // qdByDjcs.measureProjectTables = arrayToTree(measureProjectTablesArray);

                //触发自动记取
                await this.service.autoCostMathService.autoCostMath({
                    constructId: constructId,
                    singleId: unit.parentId,
                    unitId: unit.sequenceNbr,
                    changeDeIdArr: changeDeIds
                });
                // 触发费用代码计算以及费用汇总计算
                await this.service.jieSuanProject.jieSuanUnitCostCodePriceService.countCostCodePrice({
                    constructId: constructId,
                    singleId: unit.parentId,
                    unitId: unit.sequenceNbr,
                });

                qdByDjcs.fixationSecurityFee = "1";
            }
        }

        projectObjById.jieSuanSecurityFee = securityFee;
        return ResponseData.success("设置成功");

    }

    selectSecurityFee(args){
        let projectObjById = PricingFileFindUtils.getProjectObjById(args.constructId);
        if(ObjectUtils.isEmpty(projectObjById.securityFee)){
            return false
        }else {
            return true
        }

    }
}

jieSuanUnitProjectService.toString = () => '[class jieSuanUnitProjectService]';
module.exports = jieSuanUnitProjectService;