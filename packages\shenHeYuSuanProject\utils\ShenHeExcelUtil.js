// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../../../electron/vo/CellVo");
const SheetStyle = require("../../../electron/vo/SheetStyle");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const ExcelEnum = require("../enum/ExportExcelEnum");
const {DateUtils} = require("../../../electron/utils/DateUtils");
const XLSX = require('xlsx');
const ExportExcelEnum = require("../enum/ExportExcelEnum");

class ShenHeExcelUtil {

    constructor() {
        this.VER_2007 = 2007;
        this.VER_2003 = 2003;
    }

    /**
     * 判断cell是否在某个合并单元格中，如果存在返回合并单元格名字
     * @param merges
     * @param cell
     * @returns fan
     */
    getMergeName(merges, cell) {
        let result = null;
        let {row, col} = cell;

        for (let r in merges) {
            let model = merges[r].model;
            if (row >= model.top && row <= model.bottom && col >= model.left && col <= model.right) {
                result = r;
                break;
            }
        }

        return result;
    }

    /**
     * 找到sheet中对应值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    findValueCell(worksheet, value) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => k.value == value);

        return filter[0];
    }

    /**
     * 找到sheet中包含该值的cell定位
     * @param merges
     * @param cell
     * @returns fan
     */
    findContainValueCell(worksheet, containValue) {
        let results = [];
        let merges = worksheet.merges;

        //定义一个存放数据值和cell的对象
        function CellValue(cell, value) {
            this.cell = cell;
            this.value = value;
        }

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                let mergeName = null;
                if (isMerged) {
                    //获取合并单元格的名称
                    mergeName = this.getMergeName(merges, cell);
                    //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                let cellValue = new CellValue(cell, value);
                results.push(cellValue);
            })
        });
        let filter = results.filter(k => {
            if (k.value == null || typeof k.value != "string") return false;
            try {
                return k.value.includes(containValue);
            } catch (e) {
                console.log(e.stackTrace);
            }
        });
        return filter;
    }


    getMerges(workbook, sheetName) {
        const worksheet = workbook.getWorksheet(sheetName);
        return worksheet._merges;
    }

    getSheet(workbook, sheetName) {
        return workbook.getWorksheet(sheetName);
    }

    async read(excelPath, sheetName) {
        // read from a file
        let workbook = new ExcelJS.Workbook();
        await workbook.xlsx.readFile(excelPath)

        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(sheetName);
        return worksheet;
    }

    async readToWorkBook(excelPath, options = {}) {
        // read from a file
        if (options.version === this.VER_2003) {
            let workbook = XLSX.readFile(excelPath);
            return workbook;
        } else {
            let workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(excelPath)
            return workbook;
        }
    }

    async readSheetContent(excelPath, sheetName, options = {}) {
        // read from a file
        return this.readSheetContentByWorkBook(await this.readToWorkBook(excelPath, options), sheetName, options)
    }

    readSheetContentByWorkBook(workbook, sheetName, options = {}) {
        if (options.version === this.VER_2003) {
            return this.readSheetContentByWorkBook2003(workbook, sheetName);
        } else {
            return this.readSheetContentByWorkBook2007(workbook, sheetName);
        }
    }

    readSheetContentByWorkBook2003(workbook, sheetName) {
        //console.log(workbook);
        let sheetContent = workbook.Sheets[sheetName];
        if (ObjectUtils.isEmpty(sheetContent)) {
            return [];
        }
        let results = [];

        for (let k in sheetContent) {
            if (k.startsWith("!") || !sheetContent.hasOwnProperty(k)) {
                continue;
            }

            let matches = k.match(/[a-zA-Z]+/);
            if (ObjectUtils.isEmpty(matches)) {
                continue;
            }
            let colName = matches[0];
            let row = k.substring(colName.length);
            if (ObjectUtils.isEmpty(colName) || ObjectUtils.isEmpty(row)) {
                continue;
            }

            colName = colName.toLowerCase();
            let col = colName.charCodeAt(0) - 97 + 1;

            results[row] = results[row] || new Map();
            results[row].set(col, sheetContent[k].w);
        }

        return results.filter(m => ObjectUtils.isNotEmpty(m));
    }

    readSheetContentByWorkBook2007(workbook, sheetName) {
        let results = [];
        // 获取第一个工作表
        const worksheet = workbook.getWorksheet(sheetName);
        if (ObjectUtils.isEmpty(worksheet)) {
            return results;
        }

        const merges = worksheet._merges;

        worksheet.eachRow(row => {
            let rowContentMap = new Map();
            row.eachCell(cell => {
                let {isMerged, address, value, col, row} = cell;
                if (value instanceof Date) {
                    value = DateUtils.format(value);
                }
                let mergeName = null;
                if (isMerged) {
                    mergeName = this.getMergeName(merges, cell);
                    if (mergeName != null && mergeName != address) {
                        value = null;
                    }
                }
                //console.log(cell.isMerged, "  ", cell.address, ": ", cell.value)
                //console.log(isMerged, "  ", address, ": ", value, ", row: ", row, ", col: ", col)
                rowContentMap.set(col, value);
            })
            results.push(rowContentMap);
        });
        // console.log(results)
        return results;
    }

    async writeDataToSheet(data, worksheet) {
        // read from a file
        let results = [];
        // findValueCell(worksheet,"")
        let countRow = -1;
        await this.findCellStyleList(worksheet);
        worksheet.eachRow(row => {
            if (row.number >= 5) {
                countRow++
                row.eachCell(cell => {
                    // let {isMerged, address, value, col, row} = cell;
                    if (cell.col == 2) cell.value = data[countRow].bdCode;
                    if (cell.col == 3) cell.value = data[countRow].bdName;
                    if (cell.col == 4) cell.value = data[countRow].description;
                    // if (cell.col==5) cell.value = data[countRow].;
                    let mergeName = null;
                    // if(isMerged){
                    //     //获取合并单元格的名称
                    //     mergeName = getMergeName(merges, cell);
                    //     //如果合并单元格的当前具体位置与合并位置不一致 则值置为null
                    //     if(mergeName != null && mergeName != address){
                    //         value = null;
                    //     }
                    // }
                    // //每一行有值的内容 都放进去  上面是过滤了没有值的合并单元格
                    // let cellValue = new CellValue(cell,value);
                    // results.push(cellValue);
                })
            }
        });
        //console.log(results)
        await workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\csClient\\模板\\招标项目\\单位工程层级.xlsx");
        return results;
    }

    isExcel(fileName) {
        let lower = fileName ? fileName.toLowerCase() : "";
        return lower.endsWith(".xlsx") || lower.endsWith(".xls")
    }

    /**
     * 输出sheet中每一个cell的格式列表
     */
    async findCellStyleList(workSheet) {
        let sheetStyle = new SheetStyle();

        let cellList = [];
        const merges = workSheet._merges;
        for (let i = 0; i < workSheet._rows.length; i++) {
            let rowAA = workSheet._rows[i];
            for (let i = 0; i < rowAA._cells.length; i++) {
                let cellAA = rowAA._cells[i];
                let {isMerged, address, value, col, row, style, type, formula, model} = cellAA;
                let cellVo = new CellVo();
                //alignment 水平方向 值为2 为居中  值为1 为居左
                try {
                    cellVo.alignment = this.getHorizontalAlignment(style);
                } catch (error) {
                    console.log(error.stackTrace);
                }

                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.bottom)) {
                    // cellVo.borderBottom = this.getBorderNumer(style.border.bottom.style);
                    cellVo.borderBottom = 1;
                } else {
                    cellVo.borderBottom = 0;
                }
                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.left)) {
                    cellVo.borderLeft = 1;
                } else {
                    cellVo.borderLeft = 0;
                }
                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.right)) {
                    cellVo.borderRight = 1;
                } else {
                    cellVo.borderRight = 0;
                }
                if (!ObjectUtils.isEmpty(style.border) && !ObjectUtils.isEmpty(style.border.top)) {
                    cellVo.borderTop = 1;
                } else {
                    cellVo.borderTop = 0;
                }
                cellVo.cellType = type;
                cellVo.columnIndex = col - 1;
                if (value == null) {
                    cellVo.content = "";
                } else if (typeof value === 'object') {
                    cellVo.content = {};
                    cellVo.content['richText'] = value.richText[0].text;
                } else {
                    cellVo.content = value;
                }
                // cellVo.fontBold =
                if (style.font != null) {
                    cellVo.fontName = style.font.name;
                    cellVo.fontSize = style.font.size;
                }
                cellVo.rowIndex = row - 1;
                cellVo.verticalAlignment = this.getVerticalAlignment(style);
                cellList.push(cellVo);
            }
        }
        sheetStyle.cells = cellList;

        //----------merge-------------------
        function CellMerge(key, firstRow, lastRow, firstCol, lastCol) {
            this.key = key;
            this.firstRow = firstRow;
            this.lastRow = lastRow;
            this.firstCol = firstCol;
            this.lastCol = lastCol;
        }

        let mergesList = [];
        let mergeMap = new Map(Object.entries(merges))
        for (let [key, value] of mergeMap) {
            let cellMerge = new CellMerge(key, value.top - 1, value.bottom - 1, value.left - 1, value.right - 1);
            mergesList.push(cellMerge);
        }
        sheetStyle.merges = mergesList;

        //----------------------------------
        function PrintProperty(headerMerge, footerMerge, leftMerge, rightMerge, bottomMerge, topMerge, landSpace) {
            this.headerMerge = headerMerge;
            this.footerMerge = footerMerge;
            this.leftMerge = leftMerge;
            this.rightMerge = rightMerge;
            this.bottomMerge = bottomMerge;
            this.topMerge = topMerge;
            this.landSpace = landSpace;
        }

        let printProperty = new PrintProperty(workSheet.pageSetup.margins.header,
            workSheet.pageSetup.margins.footer,
            workSheet.pageSetup.margins.left,
            workSheet.pageSetup.margins.right,
            workSheet.pageSetup.margins.bottom,
            workSheet.pageSetup.margins.top,
            false
        );
        sheetStyle.print = printProperty;

        if (workSheet.name.includes("【封面3】审核签署表")
            || workSheet.name.includes("【封面4】工程审核认证单")
            || workSheet.name.includes("【封面5】工程造价审查书")
            || workSheet.name.includes("【项1】工程审核汇总对比表")
            || workSheet.name.includes("【人材机1】人材机汇总对比表")
            || workSheet.name.includes("【费1】单位工程审核对比表")
            || workSheet.name.includes("【分部1】分部分项清单对比表")
            || workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")
            || workSheet.name.includes("【措施1】措施项目审核对比表")
            || workSheet.name.includes("【人材机2】人材机审核对比表")
            || workSheet.name.includes("【人材机3】人材机价差汇总对比表")
            || workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")
            || workSheet.name.includes("【规费1】规费明细对比表")
            || workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            printProperty.landSpace = true;//表示为横版
        }

        //-----------row--------------------------
        // fillContentLimitHei = 1043.62;
        function RowUnit(height, rowIndex) {
            this.height = height;
            this.rowIndex = rowIndex;
        }

        let rowsList = [];
        for (let i = 0; i < workSheet._rows.length; i++) {
            let sheetRow = workSheet._rows[i];
            let rowUnit = {};
            if (printProperty.landSpace) {  //如果是横版
                // fillContentLimitHei = 680.38;
                rowUnit = new RowUnit((sheetRow.height / (ExcelEnum.A4HeightHorizontal - ExcelEnum.A4TopHorizontal - ExcelEnum.A4BottomHorizontal)) * 670, sheetRow.number - 1);
            } else {
                if (workSheet.name.includes("扉页") || workSheet.name.includes("封面")) {
                    rowUnit = new RowUnit(sheetRow.height, sheetRow.number - 1);
                } else {
                    rowUnit = new RowUnit((sheetRow.height / (ExcelEnum.A4Height - ExcelEnum.A4Top - ExcelEnum.A4Bottom)) * 1020, sheetRow.number - 1);
                }
            }
            rowsList.push(rowUnit);
        }
        sheetStyle.rows = rowsList;

        //----------Column-----------------------
        function ColumnUnit(width, columnIndex) {
            this.width = width;
            this.columnIndex = columnIndex;
        }

        let columnList = [];
        for (let i = 0; i < workSheet.columns.length; i++) {
            let column = workSheet.columns[i];
            if (printProperty.landSpace) {  //如果是横版
                columnList.push(new ColumnUnit((column.width / ExcelEnum.A4WidthHorizontal) * 1020, column.number - 1));
            } else {
                columnList.push(new ColumnUnit((column.width / ExcelEnum.A4Width) * (770 - 60), column.number - 1));
            }

        }
        sheetStyle.columns = columnList;
        //--------组装pageResult----------------------------
        let pageResult = {};
        let sheetBreak = [];
        pageResult['sheetBreak'] = sheetBreak;


        for (let i = 0; i < workSheet.rowBreaks.length; i++) {
            let rowBreak = workSheet.rowBreaks[i];
            let number = rowBreak.id - 1;
            sheetBreak.push(number);
        }
        sheetStyle.pageResult = pageResult;
        // workSheet.get
        return sheetStyle;
    }

    getBorderNumer(style) {
        if (style == 'thin') {
            return 0;
        } else return 1;
    }

    getHorizontalAlignment(style) {
        if (null == style.alignment) return null;
        // 值为2 为居中  值为1 为居左
        if (style.alignment.horizontal == 'center') {
            return 2;
        } else if (style.alignment.horizontal == 'left') {
            return 1;
        } else if (style.alignment.horizontal == 'right') {
            return 3;
        }
    }

    getVerticalAlignment(style) {
        if (null == style.alignment) return null;
        //top 为3 middle 为1  bottom为2
        if (style.alignment.vertical == 'middle') {
            return 1;
        } else if (style.alignment.vertical == 'top') {
            return 3;
        } else if (style.alignment.vertical == 'bottom') {
            return 2;
        }
    }

    async removeTags(html) {
        return html.replace(/(<([^>]+)>)/gi, "").replace(/&nbsp;/g, ' ');
    }

    //行高自适应
    fitHeight(workSheet) {
        const rows = workSheet._rows;
        //console.log(rows)
        let colWidth = 200;
        let fontSize = 14; //与前端展示字体大小一致
        let rowWordNum = Math.trunc(colWidth / fontSize)
        let rowSpace = 0
        let i = 1;
        for (i = 1; i < rows.length; i++) {
            let text = rows[i].cells[2].innerText;
            if (!text) {
                continue;
            }

            let resultText = "";
            let contents = text.split("\\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
            let minHeight = 0;
            let length = 0;
            let j = 0;
            for (j = 0; j < contents.length; j++) {
                let rowText = contents[j];
                if (!rowText && rowText.length == 0) {
                    continue;
                }
                length += rowText.length;
                resultText += rowText + "<br/>" + " " + "<br/>"; //多加一个空行，为了分隔两条特征，展示更友好
            }
            let rowNum = Math.ceil(length / rowWordNum) + 1 + contents.length;
            minHeight += (fontSize + rowSpace) * rowNum;

            //console.log(minHeight)

            const divs = rows[i].cells[2].getElementsByTagName('div')
            divs[0].style.height = minHeight + "px"
        }
    }

    async copyRowsWithIndex(rowNumber, rowInsertNum, workSheet) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let headArrayValues = [];
        let row = workSheet._rows[rowNumber - 1];
        headArrayValues.push(row.values);
        workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
        this.resetMerges(workSheet, rowInsertNum);
        //对下面所有的行的合并单元格进行重新 merge 目的是重置  _merges  否则会导致前端渲染出问题
        // for (let i = rowInsertNum; i < workSheet._rows.length; i++) {
        //     let row2 = workSheet._rows[i-1];//为i是插入后的下一行  i-1表示插入的当前行
        //     for (let j = 0; j < row2._cells.length; j++) {
        //         let cell = row2._cells[j];
        //         let mergeObject = mergeMaps.get(cell._address);
        //         if (mergeObject!=null){
        //             let {top,left,bottom,right} = mergeObject;
        //             workSheet.unMergeCells([top+1,left,bottom+1,right]);//由于插入了新行  所以合并单元格也需往下进行迁移
        //             workSheet.mergeCells([top+1,left,bottom+1,right]);
        //         }
        //     }
        // }
        let row1 = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < row1._cells.length; i++) {
            // row1._cells[i].numFmt = workSheet._rows[0]._cells[i].numFmt;
            row1._cells[i].style = workSheet._rows[rowNumber - 1]._cells[i].style;
        }
        row1.height = workSheet._rows[rowNumber - 1].height;
        //遍历该行的所有合并单元格  按照该行的方式进行合并
        //当前插入行与模板行的行距
        let distanceRow = rowInsertNum - rowNumber;


        for (let m = 0; m < row1._cells.length; m++) {
            // let cell = row1._cells[m];
            //获取模板行的合并单元格
            let mergeName = this.getMergeName(workSheet._merges, row._cells[m]);
            if (mergeName != null) {
                let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                if (rowInsertNum == bottom + distanceRow) {
                    workSheet.unMergeCells([top + distanceRow, left, bottom + distanceRow, right]);
                    workSheet.mergeCells([top + distanceRow, left, bottom + distanceRow, right]);
                }
            }
        }

    }


    //插入本页小计     第18行 表头
    async insertPageXiaoJi(rowInsertNum, values, workSheet) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        workSheet.insertRows(rowInsertNum, values, 'o');
        //重置 _merges
        this.resetMerges(workSheet, rowInsertNum);
        let rowObject = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowObject._cells.length; i++) {
            rowObject._cells[i].style = workSheet._rows[rowInsertNum - 7]._cells[i].style;
            //console.log("");
        }
        rowObject.height = ExcelEnum.xiaoJi;


        //对插入行的合并单元格格式 进行处理
        for (let m = 0; m < rowObject._cells.length; m++) {
            //获取模板行的合并单元格
            let mergeName = this.getMergeName(workSheet._merges, workSheet._rows[rowInsertNum - 7]._cells[m]);
            if (mergeName != null) {
                let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                workSheet.unMergeCells([top + 1, left, bottom + 1, right]);
                workSheet.mergeCells([top + 1, left, bottom + 1, right]);
            }
            rowObject._cells[m].style = workSheet._rows[rowInsertNum - 7]._cells[m].style;
        }
    }


    async insertBlankRowWithTemplate(rowInsertNum, values, workSheet, templateStyleList, mergeStyleList) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        workSheet.insertRows(rowInsertNum, values, 'o');
        //重置 _merges
        this.resetMerges(workSheet, rowInsertNum);
        let rowObject = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowObject._cells.length; i++) {
            rowObject._cells[i].style = templateStyleList[i];
        }

        for (let i = 0; i < mergeStyleList.length; i++) {
            let {top, left, bottom, right} = mergeStyleList[i];
            workSheet.unMergeCells([rowInsertNum, left, rowInsertNum, right]);
            workSheet.mergeCells([rowInsertNum, left, rowInsertNum, right]);
        }
    }

    //处理一个worksheet的分页问题
    async dealWithPage(workSheet, workbook, headArgs) {
        let headStartNum = 0;
        let headEndNum = 0;
        if (headArgs != null) {
            headStartNum = headArgs['headStartNum'];
            headEndNum = headArgs['headEndNum'];
            if (headArgs['titlePage'] == null) {
                headArgs['titlePage'] = false;//默认为 数据页
            }
        } else {
            headArgs = {};
            headStartNum = 1;
            headEndNum = 4;
            headArgs['headStartNum'] = headStartNum;
            headArgs['headEndNum'] = headEndNum;
            headArgs['titlePage'] = false;//默认为 数据页
        }

        //1、复制表头
        //2、进行 行高自适应的处理 确定行高后  进行分页
        //10号字体
        // 在该行下方插入一个分页符
        //A4 行高 721.5   宽度
        // let marginLeft = ;//左边距
        //得到每一个cell的宽度比例 并计入map
        await this.getRatioWidthSheet(workSheet);


        let mergeMap = new Map(Object.entries(workSheet._merges));
        let fontSize = 10;
        //todo 对于表头数据也应做行高自适应处理  因为列宽不一样 ？
        if (workSheet.name.includes("暂估价表")) {  //针对这个表做特殊情况处理 而传进来的 headArgs 只作分页用
            headEndNum = 4;
        }
        //行高自适应
        for (let i = headEndNum + 1; i <= workSheet._rows.length; i++) {
            let minHeight = 0;
            let fitRight = true;//这里预设为false 就会保留初始模板的空白行高度 为true针对空白行统统高度为0

            for (let j = 0; j < workSheet.getRow(i)._cells.length; j++) {
                let cell = workSheet.getRow(i)._cells[j];
                let celltextValue = cell.model.value;
                if (!celltextValue) {
                    continue;
                }
                fitRight = true;
                if (typeof celltextValue === 'number') {
                    celltextValue = String(celltextValue);
                    // cell.value = celltextValue;
                }

                let contents;
                try {
                    contents = celltextValue.split("\n"); //内容中可能包含换行符，这里以"\n"字符串为换行标识
                } catch (e) {
                    console.log(e.stackTrace);
                }
                let mergeName = this.getMergeName(workSheet._merges, cell);
                let mergeLength = 0;//得到该cell的宽度大小
                if (mergeName != null) {
                    let value = mergeMap.get(mergeName).model;
                    for (let m = value.left; m <= value.right; m++) {
                        mergeLength += workSheet.getRow(i)._cells[m - 1]._column.width;
                    }
                } else {
                    mergeLength = cell._column.width;
                }
                // let rowWordNum = Math.trunc(mergeLengthRatio * ExcelEnum.A4Width / ((fontSize / 72) * 10)) //每一列能够存放的字数
                //rowWordNum若为0  说明该列的列宽很窄  会造成递归死循环
                let rowWordNum = Math.trunc(mergeLength / ((fontSize / 72) * 10)) //每一列能够存放的字数
                if (rowWordNum == 0) continue;
                let rowSpace = 2;//行间距
                let rowNumTotal = 0;
                for (let j = 0; j < contents.length; j++) {
                    let rowText = contents[j];
                    if (!rowText && rowText.length == 0) {
                        continue;
                    }
                    // "垂直运输费 ±0.00以下 四层以内"  类似这种问题 在excel中会展示三行 实际计算是两行  18/9 此时满除就多加一行
                    let rowNum = Math.ceil(rowText.length / rowWordNum);
                    //优化处理  如果单行字数超过五  考虑到单元格的两侧边界距离  实际每行能存放的字数进行减二
                    if (rowNum >= 2 && rowNum * rowWordNum == rowText.length) {
                        rowNum++;
                    }
                    rowNumTotal += rowNum;
                }
                let newMinHeight = ((fontSize) + rowSpace) * rowNumTotal + 8;   //计算出该Cell列 的最小适应高度  加4是上下的总共边距

                if (minHeight < newMinHeight) {
                    minHeight = newMinHeight; //得到该行的最大行高
                }
                // if (cell.style.height == undefined || cell.style.height <= 18) {
                //     cell.style.height = 18;
                // }
            }
            if (fitRight) {
                workSheet.getRow(i).height = minHeight;
            }
            // if (workSheet.getRow(i).height <= 18) {
            //     workSheet.getRow(i).height = 18;
            // }
            if (workSheet.getRow(i).height > ExportExcelEnum.A4HeightDataMax) {
                workSheet.getRow(i).height = ExportExcelEnum.A4HeightDataMax;
            }
        }
        //分页处理
        // await workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
        if (!headArgs['titlePage']) {  //如果不是扉页
            //如果是表1-6 表1-7 因为要填充本页小计  因此需预留一定的空间
            let totalPage;
            if (!workSheet.name.includes("【封面4】工程审核认证单")) {
                //所有分页每一页插入审核人、审定人信息
                totalPage = await this.pageSplitForXiaoJi(workSheet, 1, headArgs, 0);

                //对workSheet 每页增加本页小计行
                //获取分页符  得到行号 然后累加每行的合计
                // let rowBreakBeforeIndex = 4;
                // let total = 0;//最后一行的合计
                // for (let n = 0; n < workSheet.rowBreaks.length; n++) {
                //     let rowBreakIndex = workSheet.rowBreaks[n].id - 1;
                //     let rowCurrent = workSheet._rows[rowBreakIndex];
                //     let slice = workSheet._rows.slice(rowBreakBeforeIndex, rowBreakIndex);
                //     let xiaoJiPage = 0;
                //     for (let i = 0; i < slice.length; i++) {
                //         let rowCur = slice[i];
                //         let cellElement = rowCur._cells[rowCur._cells.length - 1];//合价cell
                //         let cellOrder = rowCur._cells[0];//序号cell
                //         if (cellElement.value != null && cellOrder.value != null) { //多增加条件防止将分部子分部数据的合计也添加进去
                //             xiaoJiPage += Number(cellElement.value);
                //         }
                //     }
                //     rowBreakBeforeIndex = rowBreakIndex + 5;
                //     rowCurrent._cells[rowCurrent._cells.length - 1].value = xiaoJiPage;//对本页小计进行赋值
                //     total += xiaoJiPage;
                // }
                // if (workSheet.rowBreaks.length == 0) {  //如果只有一页
                //     let xiaoJiPage = 0;
                //     let sliceRows = workSheet._rows.slice(rowBreakBeforeIndex, workSheet._rows.length);
                //     for (let i = 0; i < sliceRows.length; i++) {
                //         let rowCur = sliceRows[i];
                //         let cellElement = rowCur._cells[rowCur._cells.length - 1];
                //         if (cellElement.value != null) {
                //             xiaoJiPage += Number(cellElement.value);
                //         }
                //     }
                //     let xiaoJiRow = workSheet._rows[workSheet._rows.length - 2];
                //     xiaoJiRow._cells[xiaoJiRow._cells.length - 1].value = xiaoJiPage;//对本页小计进行赋值
                //     total += xiaoJiPage;
                // }
                // let totalRow = workSheet._rows[workSheet._rows.length - 1];
                // totalRow._cells[totalRow._cells.length - 1].value = total;//对合计进行赋值
            } else {   //序号  12
                totalPage = await this.pageSplit(workSheet, 1, headArgs, 0);
            }
            /*****************************************/
            //对页码显示进行处理
            let cellList = this.findContainValueCell(workSheet, "第 1 页  共 1 页");
            if (cellList.length == 0) {
                cellList = this.findContainValueCell(workSheet, "第 1 页 共 1 页");//横版是如此格式
            }
            const grouped = cellList.reduce((result, obj) => {
                const key = obj.cell._row._number;
                if (!result[key]) {
                    result[key] = [];
                }
                result[key].push(obj.cell);
                return result;
            }, {});
            let mergeMap = new Map(Object.entries(grouped));
            let count = 0;
            for (let [key, value] of mergeMap) {
                count++;
                let str = "第 " + (count) + " 页 共 " + totalPage + " 页";
                for (let i = 0; i < value.length; i++) {
                    let elementCell = value[i];
                    elementCell.value = str;
                }
            }
            /*****************对空白行的处理********************************/
            //要求空白行只能是在末尾页  而不是在页中  否则逻辑出错
            await this.dealWithBlankRow(workSheet, headArgs);

        }
        //如果是扉页
        if (headArgs['titlePage']) {  //如果是扉页  按照比例调整行高
            let total = workSheet._rows.reduce((totalHigh, rowUnit) => {
                return totalHigh + rowUnit.height;
            }, 0);
            for (let i = 0; i < workSheet._rows.length; i++) {
                workSheet._rows[i].height = (workSheet._rows[i].height / total) * (ExcelEnum.A4Height);
            }
        }
        // await workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
    }


    async dealWithBlankRow(workSheet, headArgs) {
        if (workSheet.name.includes("编制说明") ||
            workSheet.name.includes("报价说明")
        ) return;

        //由于暂估价表格式较为特殊  第一行为空行  这里暂时进行赋值  后进行删除 是为了适应当前的方法
        if (workSheet.name.includes("暂估价表")) {
            workSheet._rows[3]._cells[0].value = "你好";
        }
        //这里之所以放在删除空白行的前面  是为了保留删除之前的单元格记录 方便后续拿到删除行的单元格格式
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let blankRowList = await this.deleteBlankRow(workSheet);
        if (blankRowList.length == 0) {    //现在也就 材料、机械、设备增值税计算表 这张表出现了这种情况  数据填充完以后进行分页恰巧没有空白行
            //在最后一页的页尾插入空白行 作为后面进行补偿的模板
            let values = [[]];
            for (let i = 0; i < workSheet._rows[workSheet._rows.length - 1]._cells.length; i++) {
                values[0].push("");
            }
            //获取数据模板行
            let headEndNum = headArgs['headEndNum'];
            let rowTemp = await this.getTemplateDataRow(workSheet, headEndNum);
            let insertRow = workSheet.insertRows(workSheet._rows.length + 1, values, 'o');
            this.resetMerges(workSheet, workSheet._rows.length);
            for (let m = 0; m < insertRow[0]._cells.length; m++) {
                //获取模板行的合并单元格
                let mergeName = this.getMergeName(workSheet._merges, rowTemp._cells[m]);
                if (mergeName != null) {
                    let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                    workSheet.unMergeCells([insertRow[0]._number, left, insertRow[0]._number, right]);
                    workSheet.mergeCells([insertRow[0]._number, left, insertRow[0]._number, right]);
                }
                insertRow[0]._cells[m].style = rowTemp._cells[m].style;
            }
            mergeMaps = new Map(Object.entries(workSheet._merges));
            blankRowList = await this.deleteBlankRow(workSheet);
        }
        //获取要插入的行号
        let rowNumInsertIndex = 0;
        let rowNumInsert = 0;
        //得到空白行的模板
        let element = null;
        if (blankRowList.length >= 2) {
            for (let i = blankRowList.length - 1; i >= 1; i--) {
                if (blankRowList[i]._number - 1 != blankRowList[i - 1]._number) { //因为存在中间为空白行,而最后一行有值且包括进来需进行分页的情况
                    // 所以空白行的模板起始行应该是最后连续的第一条空白行
                    element = blankRowList[i];
                    break;
                }
            }
            if (element == null) { //说明删除的空白行全是连续的
                element = blankRowList[0];
            }
        } else {
            if (blankRowList.length == 1) {
                element = blankRowList[0];
            }
        }

        for (let i = workSheet._rows.length - 1; i >= 0; i--) {
            if (blankRowList[blankRowList.length - 1]._number < workSheet._rows[i]._number) {
                //定位删除的最后一行空白行在excel中的倒序索引 以便确定插入位置的行号  如果一直为初始值 说明删除的空白行后面没有数据行了
                rowNumInsertIndex++;
            }
        }
        //重置行号
        const regex = /([A-Za-z]+)(\d+)/;
        for (let i = 0; i < workSheet._rows.length; i++) {
            if (workSheet._rows[i]._number != i + 1) {
                workSheet._rows[i]._number = i + 1;
                //同时需要对cell的address进行重置  否则填充行又为0的话  可能造成后续行导出excel后不会显示
                //在填充的过程中框架mergeCells方法会对后续行的address和_merges内容不对的进行重置
                //但如果不填充 address还是原来删除前的比较大的值 就会造成excel不显示该行
                for (let j = 0; j < workSheet._rows[i]._cells.length; j++) {
                    let address = workSheet._rows[i]._cells[j]._address;
                    const matches = address.match(regex);
                    const part1 = matches[1];
                    workSheet._rows[i]._cells[j]._address = part1 + workSheet._rows[i]._number;
                    workSheet._rows[i]._cells[j].model.address = part1 + workSheet._rows[i]._number;
                    //对cell的master进行重置
                    if (workSheet._rows[i]._cells[j].model.master != null) {
                        let name = this.getMergeName(workSheet._merges, workSheet._rows[i]._cells[j]);
                        workSheet._rows[i]._cells[j].model.master = name;
                    }
                }
            }
        }
        for (let i = workSheet._rows.length - 1; i >= 0; i--) {
            rowNumInsertIndex--;
            if (rowNumInsertIndex == -1) {
                rowNumInsert = workSheet._rows[i]._number + 1;  //得到插入的行号位置
                break;
            }
            if (rowNumInsertIndex == 0) {
                rowNumInsert = workSheet._rows[i]._number;  //得到插入的行号位置
                break;
            }
        }
        //获取分页符后 对有空白行的那一页 进行补偿
        let start = 0;
        let end = 0;
        if (workSheet.rowBreaks.length != 0) {
            start = workSheet.rowBreaks[workSheet.rowBreaks.length - 1].id;
            end = workSheet._rows.length;
        } else {
            start = 1;
            end = workSheet._rows.length;
        }
        //每页总高度
        let pageHeight = 0;
        if (workSheet.name.includes("【封面3】审核签署表")
            || workSheet.name.includes("【封面4】工程审核认证单")
            || workSheet.name.includes("【封面5】工程造价审查书")
            || workSheet.name.includes("【项1】工程审核汇总对比表")
            || workSheet.name.includes("【人材机1】人材机汇总对比表")
            || workSheet.name.includes("【费1】单位工程审核对比表")
            || workSheet.name.includes("【分部1】分部分项清单对比表")
            || workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")
            || workSheet.name.includes("【措施1】措施项目审核对比表")
            || workSheet.name.includes("【人材机2】人材机审核对比表")
            || workSheet.name.includes("【人材机3】人材机价差汇总对比表")
            || workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")
            || workSheet.name.includes("【规费1】规费明细对比表")
            || workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            pageHeight = (ExcelEnum.A4HeightHorizontal - ExcelEnum.A4TopHorizontal - ExcelEnum.A4BottomHorizontal);
        } else {
            pageHeight = ExcelEnum.A4Height - ExcelEnum.A4Top - ExcelEnum.A4Bottom;
        }
        let rows = workSheet._rows.slice(start - 1, end);//当前页剔除空白行的所有行
        //得到剔除空白行页的总高度
        let totalHeight = rows.reduce((total, item) => total + item.height, 0);
        //得到平均行高
        let averageHeight = totalHeight / rows.length;
        //增加空白行
        //初始化values
        let rowValues = [[]];
        for (let i = 0; i < workSheet._rows[0]._cells.length; i++) {
            rowValues[0].push("");
        }
        let heightDiffer = pageHeight - totalHeight;
        let number = Math.trunc(heightDiffer / averageHeight);

        let elementStyleList = [];
        for (let i = 0; i < element._cells.length; i++) {
            elementStyleList.push(element._cells[i].style);
        }
        let mergeElementStyle = [];
        for (let i = 0; i < element._cells.length; i++) {
            if (mergeMaps.has(element._cells[i]._address)) {
                mergeElementStyle.push(mergeMaps.get(element._cells[i]._address));
            }
        }
        for (let i = 0; i < number; i++) {
            await this.insertBlankRowWithTemplate(rowNumInsert + i, rowValues, workSheet, elementStyleList, mergeElementStyle);
            //这里是在删除的第一个空白行的基础上进行增加
            workSheet._rows[rowNumInsert + i - 1].height = averageHeight;
        }
        // await workSheet._workbook.xlsx.writeFile("C:\\Users\\<USER>\\Desktop\\测试\\test.xlsx");
        if (workSheet.name.includes("暂估价表")) {
            workSheet._rows[3]._cells[0].value = "";
        }
    }

    async getTemplateDataRow(workSheet, rowNum) {
        let rowTemp = workSheet._rows[rowNum];
        let contentStat = 0;
        for (let i = 0; i < rowTemp._cells.length; i++) {
            let cell = rowTemp._cells[i];
            if (cell.value && cell._value._master == null) {
                contentStat++;
            }
        }
        if (contentStat >= 3) {  //认为是数据行
            return rowTemp;
        } else {
            return await this.getTemplateDataRow(workSheet, ++rowNum);
        }
        return null;
    }

    //删除空白行  同时返回空白行所在行号
    async deleteBlankRow(workSheet, listNum = []) {
        for (let i = 0; i < workSheet._rows.length; i++) {
            let rowCur = workSheet._rows[i];
            let result = true;
            for (let j = 0; j < rowCur._cells.length; j++) {
                let celltextValue = rowCur._cells[j].model.value;
                if (celltextValue == null && rowCur._cells[j]._value._master != null) {  //如果有属主  则为属主的值
                    celltextValue = rowCur._cells[j]._value._master.model.value
                }
                if (ObjectUtils.isEmpty(celltextValue)) {
                    continue;
                }
                result = false;
            }
            if (result) {
                listNum.push(workSheet._rows[i]);
                workSheet._rows.splice(i, 1);
                await this.resetMergesWhenDel(workSheet, i + 1);
                await this.deleteBlankRow(workSheet, listNum);
                break;
            }
        }
        return listNum;
    }

    async getBlankRow(workSheet, listNum = []) {
        for (let i = 0; i < workSheet._rows.length; i++) {
            if (workSheet._rows[i].height == 0) {
                listNum.push(workSheet._rows[i]._number);
                // workSheet._rows.splice(i,1);
                // await this.getBlankRow(workSheet,listNum);
                // break;
            }
        }
        return listNum;
    }

    //递归定义  如果到达第二页 就增加分页 并增加表头   最后返回总页数
    async pageSplit(workSheet, rowNum, args, totalPage) {  //从1 开始   args表头相关参数
        let differ = 0;
        if (workSheet.name.includes("【封面4】工程审核认证单")) {
            differ = (ExcelEnum.A4HeightHorizontal - ExcelEnum.A4TopHorizontal - ExcelEnum.A4BottomHorizontal);
        } else {
            differ = ExcelEnum.A4Height - ExcelEnum.A4Top - ExcelEnum.A4Bottom;
        }

        totalPage++;
        let {headStartNum, headEndNum} = args;//从1开始
        let height = 0;
        let blankRows = 0;//要求如果分页时经历了空白行  那么分页符应该置在空白行的前面那一行
        for (let i = rowNum - 1; i < workSheet._rows.length - 1; i++) {
            height += workSheet._rows[i].height;
            if (workSheet._rows[i].height == 0) {
                blankRows++;
            }
            if (height + workSheet._rows[i + 1].height > differ) {
                if (blankRows == 0) {  //表示达到分页条件时经历的空白行为0 进行正常分页
                    let rowLast = workSheet.getRow(i + 1);//应该要加小计的行
                    rowLast.addPageBreak();
                } else {    //如果该页经历了空白行，则分页符加在空白行的上一行
                    let rowBefore = workSheet._rows[i - blankRows];
                    rowBefore.addPageBreak();
                    blankRows = 0;
                }
                /***********边界情况******************/
                    //如果刚好遇到下一行是合计的这种边界情况  则插入空白行
                let result = false;//是否需要插入空白行
                if (i + 1 == workSheet._rows.length - 1) {
                    //说明当前行为空白行 下一行为合计
                    result = true;
                }
                let rowInserNum = i + 1;

                //分页后进行表头复制
                for (let j = headStartNum; j <= headEndNum; j++) {
                    await this.copyRowsWithIndex(j, rowInserNum + j, workSheet);
                }
                if (result) {
                    await this.insertPageXiaoJi(rowInserNum + headEndNum + 1, [["", "", "", "", "", "", "", "", "", ""]], workSheet)
                }
                // await workSheet.workbook.xlsx.writeFile("D:\\csClient\\测试\\test.xlsx");
                totalPage = await this.pageSplit(workSheet, rowInserNum + 1, args, totalPage);
                break;
            }
        }
        return totalPage;
    }

    async pageSplitForXiaoJi(workSheet, rowNum, args, totalPage) {  // rowNum从1 开始 表示从1开始统计行高  args表头相关参数
        let differ = 0;
        if (workSheet.name.includes("【封面3】审核签署表")
            || workSheet.name.includes("【封面4】工程审核认证单")
            || workSheet.name.includes("【封面5】工程造价审查书")
            || workSheet.name.includes("【项1】工程审核汇总对比表")
            || workSheet.name.includes("【人材机1】人材机汇总对比表")
            || workSheet.name.includes("【费1】单位工程审核对比表")
            || workSheet.name.includes("【分部1】分部分项清单对比表")
            || workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")
            || workSheet.name.includes("【措施1】措施项目审核对比表")
            || workSheet.name.includes("【人材机2】人材机审核对比表")
            || workSheet.name.includes("【人材机3】人材机价差汇总对比表")
            || workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")
            || workSheet.name.includes("【规费1】规费明细对比表")
            || workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            differ = (ExcelEnum.A4HeightHorizontal - ExcelEnum.A4TopHorizontal - ExcelEnum.A4BottomHorizontal);
        } else {
            differ = ExcelEnum.A4Height - ExcelEnum.A4Top - ExcelEnum.A4Bottom;
        }

        totalPage++;
        let {headStartNum, headEndNum} = args;//从1开始
        let height = 0;
        let count = 0;
        let blankRows = 0;
        for (let i = rowNum; i <= workSheet._rows.length - 1; i++) {
            height += workSheet.getRow(i).height;
            //针对一行过高 占满一页不够的情况、及下一行过高导致本页空出大片区域的情况进行处理   对行进行拆分
            count++;
            if (count == headEndNum && height + workSheet.getRow(i + 1).height + ExcelEnum.xiaoJi > differ) {
                //对当前行行高重新计算赋值 并对新加的一行进行计算
                let originHeight = workSheet.getRow(i + 1).height;
                workSheet.getRow(i + 1).height = differ - ExcelEnum.xiaoJi - height - 1;//减一加一是为了后面进入分页的条件
                //得到比例 获得拆分行的内容分配及行高
                let cellCount = 4;
                if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
                    cellCount = 7;
                }
                let spliceRowHeight = originHeight - workSheet.getRow(i + 1).height + 1;
                let spliceContentLength = Math.trunc(workSheet.getRow(i + 1)._cells[cellCount].value.length * (spliceRowHeight / originHeight));
                let contentLast = workSheet.getRow(i + 1)._cells[cellCount].value.substring(0, workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);
                let spliceContent = workSheet.getRow(i + 1)._cells[cellCount].value.substring(workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);

                workSheet.getRow(i + 1)._cells[cellCount].value = contentLast;
                //增加新增的拆分行  这里借用这个方法
                //进行行拆分后在这里添加本页小计并增加下一页的表头  这里和上面的不一样
                // await this.insertSheetPageLastRow(workSheet, i + 1 + 1, args);
                await this.insertPageXiaoJi(i + 2, await this.insertSheetBlackRowCells(workSheet, spliceContent), workSheet)


                workSheet.getRow(i + 2).height = spliceRowHeight;
                totalPage = await this.pageSplitForXiaoJi(workSheet, i - headEndNum + 1, args, totalPage - 1);
                break;
            }
            //如果本页已有较窄数据 属于下一行过高情况
            //条件  当前行加上下一行 超出一页高度
            if (count > headEndNum && height + workSheet.getRow(i + 1).height + ExcelEnum.xiaoJi > differ
                && height + ExcelEnum.xiaoJi < differ - 2000 //设置当前行离底部的行距高于多少时 考虑拆分下面的一行
                && workSheet.getRow(i + 1).height > 2000
                && false
            ) {
                let originHeight = workSheet.getRow(i + 1).height;
                workSheet.getRow(i + 1).height = differ - ExcelEnum.xiaoJi - height - 1;//减一加一是为了后面进入分页的条件
                //得到比例 获得拆分行的内容分配及行高
                let cellCount = 4;
                if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
                    cellCount = 7;
                }
                let spliceRowHeight = originHeight - workSheet.getRow(i + 1).height + 1;
                let spliceContentLength = Math.trunc(workSheet.getRow(i + 1)._cells[cellCount].value.length * (spliceRowHeight / originHeight));
                let contentLast = workSheet.getRow(i + 1)._cells[cellCount].value.substring(0, workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);
                let spliceContent = workSheet.getRow(i + 1)._cells[cellCount].value.substring(workSheet.getRow(i + 1)._cells[cellCount].value.length - spliceContentLength);

                //**********优化*******************
                if (spliceContent.length <= 10) {
                    //如果拆分之后的下一行分配的字数过少  就不进行拆分
                    //*****************************
                } else {
                    workSheet.getRow(i + 1)._cells[cellCount].value = contentLast;
                    //增加新增的拆分行  这里借用这个方法
                    await this.insertPageXiaoJi(i + 2, await this.insertSheetBlackRowCells(workSheet, spliceContent), workSheet)
                    workSheet.getRow(i + 2).height = spliceRowHeight;

                    //进行行拆分后在这里添加本页小计并增加下一页的表头  这里和上面的不一样
                    await this.insertSheetPageLastRow(workSheet, i + 2, args);

                    let rowLast = workSheet.getRow(i + 2);//应该要加小计的行
                    rowLast.addPageBreak();
                    //分页后进行表头复制
                    for (let j = headStartNum; j <= headEndNum; j++) {
                        await this.copyRowsWithIndex(j, i + 2 + j, workSheet);
                    }
                    totalPage = await this.pageSplitForXiaoJi(workSheet, i + 3, args, totalPage);
                    break;
                }
            }
            //针对边界情况  遇到分页时插入本页小计刚好分在 分隔了 原有的本页小计和合计  这一种情况
            let result = true;
            //如果进行插入本页小计并分页时 下一行也是本页小计 即已经没有了数据行 则不进行插入
            if (workSheet.getRow(i + 1)._cells[1].value == "编制人：") {
                result = false;
            }
            if (workSheet._rows[i].height == 0) {
                blankRows++;
            }

            //当前行加本页小计的行高  刚好到了这一页的末尾
            if ((height + workSheet.getRow(i + 1).height + ExcelEnum.xiaoJi > differ
                && height + ExcelEnum.xiaoJiTrue < differ) && result
            ) {
                if (blankRows == 0) {  //表示达到分页条件时经历的空白行为0 进行正常分页
                    let rowLast = workSheet.getRow(i + 1);//应该要加小计的行
                    rowLast.addPageBreak();
                } else {    //如果该页经历了空白行，则分页符加在空白行的上一行
                    let rowBefore = workSheet._rows[i - blankRows];
                    rowBefore.addPageBreak();
                    blankRows = 0;
                }
                //进行行拆分后在这里添加本页小计并增加下一页的表头  这里和上面的不一样
                await this.insertSheetPageLastRow(workSheet, i + 1, args);


                let result = false;//为true表示增加新页面的表头后在表头下一行插入空白行
                // 以将合计行在后续处理空白行时 移到新一页的最后一行
                if (i + 1 == workSheet._rows.length - 1) {
                    //说明当前行为空白行 下一行为合计
                    result = true;
                }
                //分页后进行表头复制
                for (let j = headStartNum; j <= headEndNum; j++) {
                    await this.copyRowsWithIndex(j, i + 1 + j, workSheet);
                }
                if (result) {
                    await this.insertPageXiaoJi(i + 1 + headEndNum + 1, await this.insertSheetBlackRowCells(workSheet, ""), workSheet)
                }
                // await workSheet.workbook.xlsx.writeFile("D:\\csClient\\测试\\单位工程层级.xlsx");
                totalPage = await this.pageSplitForXiaoJi(workSheet, i + 2, args, totalPage);
                break;
            }
        }
        return totalPage;
    }


    async insertSheetPageLastRow(workSheet, rowInserNumber, args) {
        let workSheetGeshi = args["workSheetGeshi"];

        if (workSheet.name.includes("【封面4】工程审核认证单")) {
            // await this.copyRowsWithOtherSheet(1, rowInserNumber, workSheet, workSheetGeshi, 1, 5, 8);
            // await this.copyRowsWithOtherSheet(2, rowInserNumber+1, workSheet, workSheetGeshi, 1, 5, 8);
            // await this.copyRowsWithOtherSheet(3, rowInserNumber+2, workSheet, workSheetGeshi, 1, 5, 8);
        } else if (workSheet.name.includes("【项1】工程审核汇总对比表")) {
            await this.copyRowsWithOtherSheet(5, rowInserNumber, workSheet, workSheetGeshi, 3, 6, 9);
        } else if (workSheet.name.includes("【人材机1】人材机汇总对比表")) {
            await this.copyRowsWithOtherSheet(7, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 15);
        } else if (workSheet.name.includes("【单项1】单项工程审核对比表")) {
            await this.copyRowsWithOtherSheet(1, rowInserNumber, workSheet, workSheetGeshi, 2, 4, 5);
        } else if (workSheet.name.includes("【费1】单位工程审核对比表")) {
            await this.copyRowsWithOtherSheet(1, rowInserNumber, workSheet, workSheetGeshi, 3, 5, 8);
        } else if (workSheet.name.includes("【分部1】分部分项清单对比表")) {
            await this.copyRowsWithOtherSheet(3, rowInserNumber, workSheet, workSheetGeshi, 3, 7, 12);
        } else if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
            await this.copyRowsWithOtherSheet(5, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 14);
        } else if (workSheet.name.includes("【措施1】措施项目审核对比表")) {
            await this.copyRowsWithOtherSheet(7, rowInserNumber, workSheet, workSheetGeshi, 4, 7, 11);
        } else if (workSheet.name.includes("【其他1】其他项目审核对比表")) {
            await this.copyRowsWithOtherSheet(9, rowInserNumber, workSheet, workSheetGeshi, 2, 4, 6);
        } else if (workSheet.name.includes("【计日工1】计日工审核对比表")) {
            await this.copyRowsWithOtherSheet(11, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 12);
        } else if (workSheet.name.includes("【人材机2】人材机审核对比表")) {
            await this.copyRowsWithOtherSheet(13, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 15);
        } else if (workSheet.name.includes("【人材机3】人材机价差汇总对比表")) {
            await this.copyRowsWithOtherSheet(15, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 16);
        } else if (workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")) {
            await this.copyRowsWithOtherSheet(17, rowInserNumber, workSheet, workSheetGeshi, 5, 10, 14);
        } else if (workSheet.name.includes("【规费1】规费明细对比表")) {
            await this.copyRowsWithOtherSheet(19, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 11);
        } else if (workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            await this.copyRowsWithOtherSheet(21, rowInserNumber, workSheet, workSheetGeshi, 4, 8, 13);
        } else if (workSheet.name.includes("【工程量1】审定工程量计算书")) {
            await this.copyRowsWithOtherSheet(23, rowInserNumber, workSheet, workSheetGeshi, 2, 5, 7);
        } else if (workSheet.name.includes("【增值税4】增值税进项税额对比表")) {
            await this.copyRowsWithOtherSheet(25, rowInserNumber, workSheet, workSheetGeshi, 2, 3, 5);
        }

        workSheet.getRow(rowInserNumber).height = 18;
        workSheet.getRow(rowInserNumber).fontSize = 9;
    }


    async insertSheetBlackRowCells(workSheet, spliceContent) {
        let resultList = [];
        if (workSheet.name.includes("【封面4】工程审核认证单")) {

        } else if (workSheet.name.includes("【项1】工程审核汇总对比表")) {
            resultList = [["", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【人材机1】人材机汇总对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【单项1】单项工程审核对比表")) {
            resultList = [["", "", "", "", ""]];
        } else if (workSheet.name.includes("【费1】单位工程审核对比表")) {
            resultList = [["", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【分部1】分部分项清单对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")) {
            resultList = [["", "", "", "", "", "", "", spliceContent, "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【措施1】措施项目审核对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【其他1】其他项目审核对比表")) {
            resultList = [["", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【计日工1】计日工审核对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【人材机2】人材机审核对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【人材机3】人材机价差汇总对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【规费1】规费明细对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            resultList = [["", "", "", "", "", "", "", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【工程量1】审定工程量计算书")) {
            resultList = [["", "", "", "", "", "", ""]];
        } else if (workSheet.name.includes("【增值税4】增值税进项税额对比表")) {
            resultList = [["", "", "", "", ""]];
        }
        return resultList;
    }


    async copyRowsWithOtherSheet(rowNumber, rowInsertNum, workSheet, otherWorkSheet, start, midlle, last) {
        let mergeMaps = new Map(Object.entries(otherWorkSheet._merges));
        let headArrayValues = [];
        let row = otherWorkSheet._rows[rowNumber - 1];
        headArrayValues.push(row.values);
        workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
        this.resetMerges(workSheet, rowInsertNum);
        let rowLast = workSheet.getRow(rowInsertNum);
        //针对插入的这一行  做特殊的处理
        for (let i = 0; i < rowLast._cells.length; i++) {
            // row1._cells[i].numFmt = workSheet._rows[0]._cells[i].numFmt;
            rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
            rowLast._cells[i].style.alignment.horizontal = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.alignment.horizontal;
            rowLast._cells[i].style.height = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.height;
        }

        workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
        workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
        workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
        workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
        workSheet.unMergeCells(rowLast.number, midlle + 1, rowLast.number, last);
        workSheet.mergeCells(rowLast.number, midlle + 1, rowLast.number, last);
        rowLast.getCell(1).value = "编制人：";
        rowLast.getCell(start + 1).value = "审核人：";
        rowLast.getCell(midlle + 1).value = "审定人：";
    }


    /**
     * 分页最后一行，合并下一行；插入编制人、审核人、审定人格式数据
     * @param rowNumber
     * @param rowInsertNum
     * @param workSheet
     * @param otherWorkSheet
     * @param start
     * @param midlle
     * @param last
     * @returns {Promise<void>}
     */
    async copyRowsWithOtherSheetHeji(rowNumber, rowInsertNum, workSheet, otherWorkSheet, start, midlle, last) {
        let row6 = workSheet.getRow(rowInsertNum - 2);      //合计上一行
        if (row6._cells[1] == "") {
            let rowHeji = workSheet.getRow(rowInsertNum - 1);    //合计行
            for (let i = 0; i < row6._cells.length; i++) {
                // row1._cells[i].numFmt = workSheet._rows[0]._cells[i].numFmt;
                if (undefined != rowHeji._cells[i].value && null != rowHeji._cells[i].value) {
                    row6._cells[i].value = rowHeji._cells[i].value;
                }
                if (undefined != rowHeji._cells[i].style && null != rowHeji._cells[i].style) {
                    row6._cells[i].style = rowHeji._cells[i].style;
                    row6._cells[i].style.alignment.horizontal = rowHeji._cells[i].style.alignment.horizontal;
                    row6._cells[i].style.height = rowHeji._cells[i].style.height;
                }
            }

            let rowLast = workSheet.getRow(rowInsertNum - 1);
            //针对插入的这一行  做特殊的处理
            for (let i = 0; i < rowLast._cells.length; i++) {
                // row1._cells[i].numFmt = workSheet._rows[0]._cells[i].numFmt;
                rowLast._cells[i].value = otherWorkSheet._rows[rowNumber - 1]._cells[i].value;
                rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
                rowLast._cells[i].style.alignment.horizontal = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.alignment.horizontal;
                rowLast._cells[i].style.height = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.height;
            }

            workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.unMergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            workSheet.mergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            rowLast.getCell(1).value = "编制人：";
            rowLast.getCell(start + 1).value = "审核人：";
            rowLast.getCell(midlle + 1).value = "审定人：";
            rowLast.height = 18;
        } else {
            let mergeMaps = new Map(Object.entries(otherWorkSheet._merges));
            let headArrayValues = [];
            let row = otherWorkSheet._rows[rowNumber - 1];
            headArrayValues.push(row.values);
            workSheet.insertRows(rowInsertNum, headArrayValues, 'o');
            this.resetMerges(workSheet, rowInsertNum);
            let rowLast = workSheet.getRow(rowInsertNum);
            //针对插入的这一行  做特殊的处理
            for (let i = 0; i < rowLast._cells.length; i++) {
                // row1._cells[i].numFmt = workSheet._rows[0]._cells[i].numFmt;
                rowLast._cells[i].style = otherWorkSheet._rows[rowNumber - 1]._cells[i].style;
                rowLast._cells[i].style.alignment.horizontal = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.alignment.horizontal;
                rowLast._cells[i].style.height = otherWorkSheet._rows[rowNumber - 1]._cells[i].style.height;
            }

            workSheet.unMergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.mergeCells(rowLast.number, 1, rowLast.number, start);
            workSheet.unMergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.mergeCells(rowLast.number, start + 1, rowLast.number, midlle);
            workSheet.unMergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            workSheet.mergeCells(rowLast.number, midlle + 1, rowLast.number, last);
            rowLast.getCell(1).value = "编制人：";
            rowLast.getCell(start + 1).value = "审核人：";
            rowLast.getCell(midlle + 1).value = "审定人：";
        }
    }

    async insertMergeLastRow(workSheet, rowNumber, start, midlle, last) {
        let rowLast = workSheet.getRow(rowNumber);
        workSheet.unMergeCells(rowLast.number, 1, rowLast.number, 3);
        workSheet.mergeCells(rowLast.number, 1, rowLast.number, 3);
        workSheet.unMergeCells(rowLast.number, 4, rowLast.number, 7);
        workSheet.mergeCells(rowLast.number, 4, rowLast.number, 7);
        workSheet.unMergeCells(rowLast.number, 8, rowLast.number, 12);
        workSheet.mergeCells(rowLast.number, 8, rowLast.number, 12);
        rowLast.getCell(start).value = "编制人：";
        rowLast.getCell(midlle).value = "审核人：";
        rowLast.getCell(last).value = "审定人：";
    }


    async getRatioWidthSheet(workSheet) {
        let pageSetupJsonObject = {
            "fitToPage": false,
            "margins": {
                "left": 0.585166666666667,
                "right": 0.585166666666667,
                "top": 0.979166666666667,
                "bottom": 0,
                "header": 0.979166666666667,
                "footer": 0
            },
            "paperSize": 9,
            "orientation": "portrait",
            "horizontalDpi": 4294967295,
            "verticalDpi": 4294967295,
            "pageOrder": "downThenOver",
            "blackAndWhite": false,
            "draft": false,
            "cellComments": "None",
            "errors": "displayed",
            "scale": 100,
            "fitToWidth": 1,
            "fitToHeight": 1,
            "firstPageNumber": 1,
            "useFirstPageNumber": false,
            "usePrinterDefaults": false,
            "copies": 1,
            "showRowColHeaders": false,
            "showGridLines": false,
            "horizontalCentered": true,
            "verticalCentered": false
        }


        let columnWidthTotal = 0;
        for (let i = 0; i < workSheet._columns.length; i++) {
            let columnWidth = workSheet._columns[i].width;
            columnWidthTotal += columnWidth;
        }
        let differ = 0;
        if (workSheet.name.includes("【封面3】审核签署表")
            || workSheet.name.includes("【封面4】工程审核认证单")
            || workSheet.name.includes("【封面5】工程造价审查书")
            || workSheet.name.includes("【项1】工程审核汇总对比表")
            || workSheet.name.includes("【人材机1】人材机汇总对比表")
            || workSheet.name.includes("【费1】单位工程审核对比表")
            || workSheet.name.includes("【分部1】分部分项清单对比表")
            || workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")
            || workSheet.name.includes("【措施1】措施项目审核对比表")
            || workSheet.name.includes("【人材机2】人材机审核对比表")
            || workSheet.name.includes("【人材机3】人材机价差汇总对比表")
            || workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")
            || workSheet.name.includes("【规费1】规费明细对比表")
            || workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            differ = (ExcelEnum.A4WidthHorizontal - ExcelEnum.A4LeftHorizontal - ExcelEnum.A4RightHorizontal);
        } else {
            differ = (ExcelEnum.A4Width - ExcelEnum.A4Left - ExcelEnum.A4Right);
        }

        for (let i = 0; i < workSheet._columns.length; i++) {
            workSheet._columns[i].width = (workSheet._columns[i].width / columnWidthTotal) * differ;
        }

        // if (workSheet.name.includes("封面") || workSheet.name.includes("扉页") || workSheet.name.includes("编制说明")
        //     || workSheet.name.includes("工程项目总价表")) {
        //     workSheet.pageSetup = pageSetupJsonObject;
        // }

    }

    async traversalRowToCellBottom(row) {
        for (let i = 0; i < row._cells.length; i++) {
            let cell = row._cells[i];
            cell.style.alignment.vertical = 'bottom';
        }
    }

    //sheet表新增一行后 对原有的merges进行向下重置
    async resetMerges(workSheet, rowInsertNum) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let map = new Map();
        for (let [key, value] of mergeMaps) {
            const regex = /([A-Za-z]+)(\d+)/;

            const matches = key.match(regex);
            if (matches && matches.length >= 2) {
                const part1 = matches[1];
                const part2 = matches[2];
                if (Number(part2) < rowInsertNum) {
                    map.set(key, value);
                    continue;
                }
                let after = Number(part2) + 1 + "";
                key = part1 + after;
                //console.log("Part 1:", part1);
                //console.log("Part 2:", part2);
            } else {
                console.log("Invalid string format");
            }
            value.top = value.top + 1;
            value.bottom = value.bottom + 1;
            map.set(key, value);
        }
        workSheet._merges = {};
        for (let [key, value] of map) {
            workSheet._merges[key] = value;
        }
    }

    //sheet表删除一行后 对原有的merges进行重置
    async resetMergesWhenDel(workSheet, rowDelNum) {
        let mergeMaps = new Map(Object.entries(workSheet._merges));
        let map = new Map();
        for (let [key, value] of mergeMaps) {
            const regex = /([A-Za-z]+)(\d+)/;

            const matches = key.match(regex);
            if (matches && matches.length >= 2) {
                const part1 = matches[1];
                const part2 = matches[2];
                if (Number(part2) < rowDelNum) {
                    map.set(key, value);
                    continue;
                }
                if (Number(part2) == rowDelNum) {
                    continue;
                }
                let after = Number(part2) - 1 + "";
                key = part1 + after;
                //console.log("Part 1:", part1);
                //console.log("Part 2:", part2);
            } else {
                //console.log("Invalid string format");
            }
            value.top = value.top - 1;
            value.bottom = value.bottom - 1;
            map.set(key, value);
        }
        workSheet._merges = {};
        for (let [key, value] of map) {
            workSheet._merges[key] = value;
        }
        // let newMergeMaps = new Map(Object.entries(workSheet._merges));
        // for (let [key, value] of newMergeMaps) {
        //     const regex = /([A-Za-z]+)(\d+)/;
        //
        //     const matches = key.match(regex);
        //     if (matches && matches.length >= 2) {
        //         const part1 = matches[1];
        //         const part2 = matches[2];
        //         if (Number(part2) < rowDelNum) {
        //             continue;
        //         }
        //     } else {
        //         console.log("Invalid string format");
        //     }
        //     workSheet.unMergeCells([value.top,value.left,value.bottom,value.right]);
        //     workSheet.mergeCells([value.top,value.left,value.bottom,value.right]);
        // }
    }

    //由于框架中的style对象是共用的 所以有了该方法
    //对单个的cell对象格式进行设置
    async setStyleForCellHorizontal(originalStyle, cell, horizontal) {
        cell.style = {};
        cell.style['font'] = originalStyle.font;
        cell.style['border'] = originalStyle.border;
        cell.style['fill'] = originalStyle.fill;
        let alignment = originalStyle.alignment;
        let newAlignment = {};
        newAlignment['horizontal'] = horizontal;
        newAlignment['vertical'] = alignment.vertical;
        cell.style['alignment'] = newAlignment;
    }

}

module.exports = {
    ShenHeExcelUtil: new ShenHeExcelUtil()
};
