'use strict';


const {
    app: electronApp,<PERSON><PERSON>erWindow,
    dialog
} = require('electron');
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Snowflake} = require("../../../electron/utils/Snowflake");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {FileUtils} = require("../utils/FileUtils");
const {Service} = require("../../../core");
const {ShenHeWinManageUtils} = require("../utils/ShenHeWinManageUtils");
const {FileLevelTreeNode} = require("../../../electron/model/FileLevelTreeNode");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {arrayToTree, treeToArray} = require("../../../electron/main_editor/tree");
const ConstructBiddingTypeConstant = require("../../../electron/enum/ConstructBiddingTypeConstant");
const UpdateStrategy = require("../../../electron/main_editor/update/updateStrategy");
const {toJsonYsfString} = require("../../../electron/main_editor/util");
const {WinManageUtils} = require("../../../common/WinManageUtils");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const {ConstructOperationUtil} = require("../../../electron/utils/ConstructOperationUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");
/**
 * 示例服务
 * @class
 */
class ShenheProjectService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 立即新建审核项目
     */
    async shNewProject (arg) {

        let {projectName,ssFilePath,sdFilePath,matchingSettings} = arg;
        if(ObjectUtils.isEmpty(projectName))return ResponseData.fail("参数有误");
        if(ObjectUtils.isEmpty(ssFilePath))return ResponseData.fail("参数有误");

        //获取到送审项目数据
        let ssObj = await PricingFileFindUtils.getProjectObjByPath(ssFilePath);
        if(ssObj.deStandardReleaseYear == "22")return ResponseData.fail("审核计价暂未开放2022定额序列");
        if(await WinManageUtils.projectIsOpen(ssObj.sequenceNbr))return ResponseData.fail("预算计价文件正在打开，请关闭后重新导入");


        ssObj.sequenceNbr = Snowflake.nextId();
        ObjectUtils.updatePropertyValue(ssObj,"constructId",ssObj.sequenceNbr);
        //复制后的审定
        let newSdObj = ObjectUtils.cloneDeep(ssObj);
        if (ObjectUtils.isNotEmpty(sdFilePath)){
            //获取到审定项目数据
            let sdObj = await PricingFileFindUtils.getProjectObjByPath(sdFilePath);
            if(await WinManageUtils.projectIsOpen(ssObj.sequenceNbr))return ResponseData.fail("预算计价文件正在打开，请关闭后重新导入");
            if(ssObj.deStandardReleaseYear == "22")return ResponseData.fail("审核计价暂未开放2022定额序列");
            //不同计税方式（一般计税&简易计税）
            if(ssObj.projectTaxCalculation.taxCalculationMethod != sdObj.projectTaxCalculation.taxCalculationMethod)return ResponseData.fail("送审和审定文件不是同类文件，请重新上传");
            //不同计价类型（预算&结算）

            //不同项目结构（单位工程项目&招投标项目）
            if(ssObj.biddingType != sdObj.biddingType&&(ssObj.biddingType ===2 || sdObj.biddingType ===2))return ResponseData.fail("送审和审定文件不是同类文件，请重新上传");

            newSdObj = sdObj;
        }
        let oldSequenceNbr = newSdObj.sequenceNbr;
        //处理审定文件
        let sequenceNbr = Snowflake.nextId();
        newSdObj.sequenceNbr = sequenceNbr;
        newSdObj.ysshConstructId = ssObj.sequenceNbr;
        newSdObj.oldConstructName = newSdObj.constructName;
        newSdObj.oldConstructId = oldSequenceNbr;
        newSdObj.constructName = projectName;
        newSdObj.path = ObjectUtils.isNotEmpty(sdFilePath) ? sdFilePath : ssFilePath;
        newSdObj.matchingSettings = matchingSettings;
        ObjectUtils.updatePropertyValue(newSdObj,"constructId",sequenceNbr);
        if(newSdObj.biddingType === 2)newSdObj.unitProject.upName = projectName;

        //ProjectFileUtils.writeUserHistoryListFile(newSdObj);
        //内存数据写入
        PricingFileWriteUtils.writeToMemory(ssObj);
        PricingFileFindUtils.getUnitList(ssObj.sequenceNbr).forEach(k =>{
            k.itemBillProjects = arrayToTree(k.itemBillProjects);
            k.measureProjectTables = arrayToTree(k.measureProjectTables);
        });
        PricingFileWriteUtils.writeToMemory(newSdObj);
        PricingFileFindUtils.getUnitList(newSdObj.sequenceNbr).forEach(k =>{
            k.itemBillProjects = arrayToTree(k.itemBillProjects);
            k.measureProjectTables = arrayToTree(k.measureProjectTables);
        });

        //自动匹配
        if (ObjectUtils.isNotEmpty(ssFilePath) && ObjectUtils.isNotEmpty(sdFilePath)) {
            //差异文件默认对比规则
            this.differenceFileBindingRule(ssObj,newSdObj);
            return ResponseData.success({type:"open",constructId:newSdObj.sequenceNbr});
        }
        await this.autoBindingRule(newSdObj);

        let defaultStoragePath =  await  this.service.commonService.getSetStoragePath(projectName);
        const dialogOptions = {
            title: '保存文件',
            defaultPath: defaultStoragePath.replace('YSF','YSH').toString(),
            filters: [{ name: '云算房文件', extensions: ['YSH'] }]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        if (result){
            if (ShenHeWinManageUtils.pullWin(newSdObj.sequenceNbr))return ResponseData.success();
            newSdObj.path = result;
            ShenHeWinManageUtils.createWindow(newSdObj.constructName,newSdObj.sequenceNbr);
            FileUtils.creatYshFile(ssObj,newSdObj,result);
            ProjectFileUtils.writeUserHistoryListFile(newSdObj);
            return ResponseData.success(newSdObj.sequenceNbr);
        }
        return ResponseData.success();
    }


    async openProject() {
        let defaultStoragePath = await this.service.commonService.getSetStoragePath(null);

        const options = {
            properties: ['openFile'],
            defaultPath: defaultStoragePath, // 默认保存路径
            filters: [
                {name: '云算房', extensions: ["YSH"]} // 可选的文件类型
            ]
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)) {
            console.log("未选中任何文件");
            return;
        }
        //获取选中的路径
        let path = result[0];
        //获取项目数据
        let data =await PricingFileFindUtils.getProjectObjByPath(path);
        let shd =JSON.parse(data.shd);
        let ssh =JSON.parse(data.ssh);
        shd.path = path;
        //将项目数据写入到内存当中
        PricingFileWriteUtils.writeToMemory(ssh);
        PricingFileWriteUtils.writeToMemory(shd);
        if(ssh.UPCContext){
            UPCContext.load(ssh.UPCContext);
        }
        if(shd.UPCContext){
            UPCContext.load(shd.UPCContext);
        }
        this.service.systemService.loadProject(shd);
        this.service.systemService.loadProject(ssh);

        let windowId = shd.sequenceNbr;
        let windowName = shd.constructName;
        if (!ObjectUtils.isEmpty(global.windowMap) && global.windowMap.has(windowId)){
            let newVar = global.windowMap.get(windowId);
            // 获取对应的窗口引用
            let win = BrowserWindow.fromId(newVar);
            if (win.isMinimized()){
                win.restore();
            }
            //将窗口移动到顶部
            win.moveTop();
            return;
        }
        //创建窗口
        let win = ShenHeWinManageUtils.createWindow(shd.constructName,shd.sequenceNbr);
        // 定义全局map
        if (ObjectUtils.isEmpty(global.windowMap)){
            global.windowMap = new Map();
        }
        windowMap.set(windowId,win.id);
        return windowId;
    }


    /**
     * 差异文件绑定规则
     */
    differenceFileBindingRule(ssObj,newSdObj){
        //审定数据查询
        //获取审定的所有单项
        let sdSingleProjects = this.getSingleProjectsById(newSdObj.sequenceNbr);
        //获取审定的所有单位
        let sdUnits = PricingFileFindUtils.getUnitList(newSdObj.sequenceNbr);


        //送审数据查询
        //获取送审的所有单项
        let sshSingleProjects = this.getSingleProjectsById(ssObj.sequenceNbr);
        //获取送审的所有单位
        let sshUnitProjects = PricingFileFindUtils.getUnitList(ssObj.sequenceNbr);
        //重新返回树形状数据

        //循环审定的单项工程
        sdSingleProjects.forEach(sdSingleProject => {
            //送审中如果有和审定相同的单项
            let sshItemData = sshSingleProjects.find(singleProject => singleProject.projectName === sdSingleProject.projectName);
            if(ObjectUtils.isNotEmpty(sshItemData)){
                sdSingleProject.ysshSingleId = sshItemData.sequenceNbr;
                //获取单项下的单位工程
                let sdSingleByUnits = sdUnits.filter(k =>k.spId == sdSingleProject.sequenceNbr);
                let ssSingleByUnits = sshUnitProjects.filter(k =>k.spId == sshItemData.sequenceNbr);

                sdSingleByUnits.forEach(k =>{
                    let sshUnit = ssSingleByUnits.find(i => i.upName === k.upName);
                    if(ObjectUtils.isNotEmpty(sshUnit))k.ysshUnitId = sshUnit.sequenceNbr;
                });


            }
        })

        //循环审定的单位
        // sdUnits.forEach(sdUnit => {
        //     let sshItemData = sshUnitProjects.find(ssUnit => ssUnit.upName === sdUnit.upName);
        //     if(ObjectUtils.isNotEmpty(sshItemData))sdUnit.ysshUnitId = sshItemData.sequenceNbr;
        // })
    }


    /**
     * 自动绑定规则
     * @param obj
     */
    async autoBindingRule(obj) {

        let {sequenceNbr,ysshConstructId} = obj;
        let singleProjectList = this.getSingleProjectsById(sequenceNbr);

        //所有单项工程
        if (ObjectUtils.isNotEmpty(singleProjectList)){
            singleProjectList.forEach((item,index)=>{
                item.ysshSingleId = item.sequenceNbr;
            });
        }
        //所有单位工程
        let unitList = PricingFileFindUtils.getUnitList(sequenceNbr);
        if (ObjectUtils.isNotEmpty(unitList)){
            for (let item in unitList) {
                const unitItem = unitList[item];
                unitItem.ysshUnitId = unitItem.sequenceNbr;
                unitItem.ysshSingleId = unitItem.spId;
                unitItem.ysshConstructId = ysshConstructId;
                await this.init(sequenceNbr, unitItem.spId, unitItem.sequenceNbr, ysshConstructId, unitItem.ysshSingleId, unitItem.ysshUnitId);
            }
        }



    }

    /**
     * 数据初始化  -用于其他功能挂关系地方
     * @param constructId 审定ID
     * @param singleId
     * @param unitId
     * @param ssConstructId 送审ID
     * @param ssSingleId
     * @param ssUnitId
     * @return {Promise<void>}
     */
    async init(constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId) {
        // matchingSettingArr 该参数是匹配设置参数

        let matchingSettingArr=PricingFileFindUtils.getProjectObjById(constructId).matchingSettings;
        if(ObjectUtils.isEmpty(matchingSettingArr)){
            // 01 清单12位编码
            // 02 清单前9位编码
            // 03 清单名称
            // 04 清单项目特征
            matchingSettingArr="02,03";
        }
        let args = { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId,matchingSettingArr};

        //分部分项初始化
       try{
           await this.service.shenHeYuSuanProject.ysshFbfxService.matchBillProject(args);
       }catch (e) {
           console.log('分部分项初始化数据报错'+e.stack);
       }
       try {
           await this.service.shenHeYuSuanProject.ysshMeasureService.initMatchMeasureProject(args)
       }catch (e){
           console.log('措施项目初始化关联关系：'+e.stack)
       }
        // 其他项目数据初始化
        await this.service.shenHeYuSuanProject.ysshOtherProjectService.initOtherProjectAllMatch(args);
        try {
            // 费用汇总数据初始化
            await this.service.shenHeYuSuanProject.ysshCostSummaryService.initMatchCostSummaryProject(args);
        }catch (e) {
            console.log('费用汇总数据初始化关联关系：'+e.stack)
        }

        //整体分部分项  措施项目清单解锁
        this.service.unitProjectService.unLockAll(args);
        args.constructId=ssConstructId
        args.singleId=ssSingleId
        args.unitId=ssUnitId
        this.service.unitProjectService.unLockAll(args);


    }



    async generateLevelTreeNodeStructure(arg){
        let {sequenceNbr} = arg;
        //获取项目结构树
        let projectObj =  PricingFileFindUtils.getProjectObjById(sequenceNbr);
        const result = await this.service.constructProjectService.generateLevelTreeNodeStructure(arg);

        // let result = new Array();
        // await this.generateLevelTreeNode(projectObj, result);

        //获取审定的所有单项


        let sdhUnitProjects = PricingFileFindUtils.getUnitList(sequenceNbr);
        //let sdhSingleProjects = PricingFileFindUtils.getSingleProjectList(sequenceNbr);
        let sdhMap = ConstructOperationUtil.flatConstructTreeToMapById(sequenceNbr)
        let sdhConstructOperation = Array.from(sdhMap.values()).map(value => value);
        let sdhSingleProjects = sdhConstructOperation.filter(k =>k.levelType == 2);



        let sshUnitProjects = PricingFileFindUtils.getUnitList(projectObj.ysshConstructId);
        //let sshSingleProjects = PricingFileFindUtils.getSingleProjectList(projectObj.ysshConstructId);

        let sshMap = ConstructOperationUtil.flatConstructTreeToMapById(projectObj.ysshConstructId);
        let sshConstructOperation = Array.from(sshMap.values()).map(value => value);
        let sshSingleProjects = sshConstructOperation.filter(k =>k.levelType == 2);
        let sshUnitCopy = ConvertUtil.deepCopy(sshUnitProjects);
        let sshSingleCopy = ConvertUtil.deepCopy(sshSingleProjects);
        //审定树
        result.forEach(item => {
            if (1 == item.levelType){
                item.ysshConstructId = projectObj.ysshConstructId;
            }

            let unit = sdhUnitProjects.find(k => 3 == item.levelType && k.sequenceNbr == item.id);
            if (ObjectUtils.isNotEmpty(unit)){
                item.ysshUnitId = unit.ysshUnitId;
            }
            let single = sdhSingleProjects.find(k => 2 == item.levelType && k.sequenceNbr == item.id);
            if (ObjectUtils.isNotEmpty(single)){
                item.ysshSingleId = single.ysshSingleId;

            }



            if (item.levelType === 2 && ObjectUtils.isNotEmpty(item.ysshSingleId)) {
                let single = sshSingleCopy.find(item2 => item2.sequenceNbr === item.ysshSingleId);
                single.matchFlag = true;
                item.ysshParentId = single.constructId;
            }
            if (item.levelType === 3 && ObjectUtils.isNotEmpty(item.ysshUnitId)) {
                let unit = sshUnitCopy.find(item2 => item2.sequenceNbr === item.ysshUnitId);
                //todo 处理送审拉单位特殊逻辑
                unit.matchFlag = true;
                //无需判断类型biddingtype
                item.ysshSingleId = unit.spId;
                item.ysshParentId = ObjectUtils.isEmpty(item.spId) ? unit.constructId : unit.spId;
                if (ObjectUtils.isNotEmpty(unit.special)){
                    item.type = 'delete';
                }
            }
            if (item.levelType !== 1 && ObjectUtils.isEmpty(item.ysshSingleId) && ObjectUtils.isEmpty(item.ysshUnitId)) {
                item.type = 'add';
            }
        })

        sshSingleCopy ? sshSingleCopy.forEach(item => {
            if (!item.matchFlag) {
                let constructTreeNode = new FileLevelTreeNode();
                let singleProject = item;
                //constructTreeNode.id = singleProject.sequenceNbr + "_ss";
                constructTreeNode.id = singleProject.sequenceNbr;
                constructTreeNode.name = singleProject.projectName;
                constructTreeNode.type = 'delete';
                constructTreeNode.levelType = 2;
                constructTreeNode.ysshSingleId = singleProject.sequenceNbr;
                constructTreeNode.parentId = projectObj.sequenceNbr;
                result.push(constructTreeNode);
            }
        }) : null;
        sshUnitCopy ? sshUnitCopy.forEach(item => {

            if (!item.matchFlag) {
                let unitProject = item;
                let unitTreeNode = new FileLevelTreeNode();
                //unitTreeNode.id = unitProject.sequenceNbr + "_ss";
                unitTreeNode.id = unitProject.sequenceNbr;
                unitTreeNode.name = unitProject.upName;
                unitTreeNode.levelType = 3;
                unitTreeNode.type = 'delete';
                unitTreeNode.ysshSingleId = unitProject.spId;
                unitTreeNode.ysshUnitId = unitProject.sequenceNbr;
                unitTreeNode.constructMajorType = unitProject.constructMajorType;

                let unitParentid = projectObj.sequenceNbr;
                if (ObjectUtils.isNotEmpty(unitProject.spId)) {
                    let shdSingleProjects = PricingFileFindUtils.getSingleProjectList(projectObj.sequenceNbr);
                    let singleProject = shdSingleProjects.find(singleItem => singleItem.ysshSingleId === unitProject.spId);
                    //singleProject ? unitParentid = singleProject.sequenceNbr : unitParentid = unitProject.spId + "_ss";
                    singleProject ? unitParentid = singleProject.sequenceNbr : unitParentid = unitProject.spId ;
                }
                unitTreeNode.parentId = unitParentid;
                unitTreeNode.libraryCode = unitProject.mainDeLibrary;
                unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
                result.push(unitTreeNode);
            }
        }) : null;
        return result;




    }


    /**
     * 另存为
     */
    async fileSaveAs(args,fileNameSuffix){
        let {constructId} = args;

        let projectObjById = PricingFileFindUtils.getProjectObjById(constructId);
        let ssProjectObjById = PricingFileFindUtils.getProjectObjById(projectObjById.ysshConstructId);

        let defaultStoragePath =  await  this.service.commonService.getSetStoragePath(projectObjById.constructName + fileNameSuffix);

        const dialogOptions = {
            title: '另存为',
            defaultPath: defaultStoragePath.replace('YSF','YSH').toString(),
            filters: [{ name: '云算房文件', extensions: ["YSH"] }],
        };
        let filePath = dialog.showSaveDialogSync(null, dialogOptions);
        if (filePath) {
            if (!filePath.toUpperCase().endsWith(".YSH")) {
                filePath += ".YSH";
            }
            //查询选择的路径是否已经有被打开的文件
            let result =  await this.service.constructProjectFileService.getOneProDataByPath(filePath);
            if (!ObjectUtils.isEmpty(result)){
                let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
                if (!ObjectUtils.isEmpty(projectObj)){
                    return  ResponseData.success(2);
                }
            }
            //复制一份原始数据
            let copyObj = ConvertUtil.deepCopy(projectObjById);
            copyObj.path = filePath;
            //重新刷新所有的项目ID
            let constructId = Snowflake.nextId();
            copyObj.sequenceNbr = constructId;
            ObjectUtils.updatePropertyValue(copyObj, 'constructId', constructId);

            //PricingFileWriteUtils.creatYsfFile(copyObj);
            PricingFileWriteUtils.writeToMemory(copyObj);
            FileUtils.creatYshFile(ssProjectObjById,projectObjById,filePath);
            //await this.service.ysfHandlerService.creatYsfFile(copyObj);
            global.constructProject[copyObj.sequenceNbr] = null;
            return  ResponseData.success(1);
        }
        return  ResponseData.success(0);
    }


    /**
     * 查询数据
     * @param  arg
     */
    async shQueryDetail (arg) {
        let {constructId} = arg;

        //审定数据查询
        let sdObj = PricingFileFindUtils.getProjectObjById(constructId);
        if(ObjectUtils.isEmpty(sdObj))return new ResponseData('100','未找到对应数据');
        let sdMap = ConstructOperationUtil.flatConstructTreeToMapByObj(sdObj);
        let sdConstructOperation = Array.from(sdMap.values()).map(value => value);

        //获取审定的所有单项
        let sdSingleProjects = sdConstructOperation.filter(k =>k.levelType == 2);
        //获取审定的所有单位
        let sdUnits = PricingFileFindUtils.getUnitList(constructId);


        //送审数据查询
        let ysshConstructId = sdObj.ysshConstructId;
        let ssObj = PricingFileFindUtils.getProjectObjById(ysshConstructId);
        if(ObjectUtils.isEmpty(ssObj))return new ResponseData('100','未找到对应数据');
        //获取送审的所有单项
        let ssMap = ConstructOperationUtil.flatConstructTreeToMapById(ysshConstructId);
        let ssConstructOperation = Array.from(ssMap.values()).map(value => value);
        let sshSingleProjects = ssConstructOperation.filter(k =>k.levelType == 2);
        //获取送审的所有单位
        let sshUnitProjects = PricingFileFindUtils.getUnitList(ysshConstructId);
        //重新返回树形状数据



        //送审数据操作copy的
        let sshSpCopy = ConvertUtil.deepCopy(sshSingleProjects);
        let sshUnitCopy = ConvertUtil.deepCopy(sshUnitProjects);


        //循环审定的单项工程
        sdSingleProjects.forEach(sdSingleProject => {
            //送审中如果有和审定相同的单项
            let sshItemData = sshSpCopy.find(singleProject => singleProject.sequenceNbr === sdSingleProject.ysshSingleId);
            if(ObjectUtils.isNotEmpty(sshItemData))sshItemData.matchFlag = true;
        })

        //循环审定的单位
        sdUnits.forEach(sdUnit => {
            let sshItemData = sshUnitCopy.find(ssUnit => ssUnit.sequenceNbr === sdUnit.ysshUnitId);
            if(ObjectUtils.isNotEmpty(sshItemData))sshItemData.matchFlag = true;
        })
        return this.generTreeListNode(sdObj,ssObj,sshSpCopy,sshUnitCopy,sdSingleProjects);
    }

    generTreeListNode(sdObj,ssObj,sshSpCopy,sshUnitCopy,shdSingleProjects){
        //组装送审树
        let shObjArray = new Array();
        FileUtils.generateLevelTreeNode(ssObj,shObjArray);
        //组装审定树
        let result =new Array()
        //此时可操作审定数据了
        let shendObjCopy = ConvertUtil.deepCopy(sdObj);

        //组装审定树
        FileUtils.generateLevelTreeNode(shendObjCopy,result);

        //判断审定树
        if(ObjectUtils.isNotEmpty(result)){
            result.forEach(item=>{
                if(item.levelType === 1){
                    item.ssName  = ssObj.constructName;
                    item.ssId  = ssObj.sequenceNbr;
                }
                if(item.levelType === 2){
                    let ssSingleData = item.ysshSingleId?sshSpCopy.find(itemData => itemData.sequenceNbr === item.ysshSingleId):null;
                    item.ssName  = ssSingleData?ssSingleData.projectName:null;
                    item.ssId  = ssSingleData?ssSingleData.sequenceNbr:null;
                }

                if(item.levelType === 3){
                    let ssdData = item.ysshUnitId?sshUnitCopy.find(itemData => itemData.sequenceNbr === item.ysshUnitId):null;
                    item.ssName = ssdData?ssdData.upName:'';
                    item.ssId = ssdData?ssdData.sequenceNbr:'';
                }
            } );
        }
        sshSpCopy.forEach(item=>{
            if(!item.matchFlag){
                let constructTreeNode = new FileLevelTreeNode();
                let singleProject = item;
                //constructTreeNode.ssId = singleProject.sequenceNbr+"_ss";
                constructTreeNode.ssId = singleProject.sequenceNbr;
                constructTreeNode.ssName = singleProject.projectName;
                constructTreeNode.type = 'delete';
                constructTreeNode.levelType = 2;
                constructTreeNode.parentId = shendObjCopy.sequenceNbr;
                result.push(constructTreeNode);
            }
        })
        sshUnitCopy.forEach(item=>{
            if(!item.matchFlag){
                let unitProject = item;
                let unitTreeNode = new FileLevelTreeNode();
                //unitTreeNode.ssId = unitProject.sequenceNbr+"_ss";
                unitTreeNode.ssId = unitProject.sequenceNbr;
                unitTreeNode.ssName = unitProject.upName;
                unitTreeNode.levelType = 3;
                unitTreeNode.type = 'delete';
                unitTreeNode.constructMajorType = unitProject.constructMajorType;
                let unitParentid = sdObj.sequenceNbr;
                if( ObjectUtils.isNotEmpty(unitProject.spId)){
                    let singleProject = shdSingleProjects?shdSingleProjects.find(singleItem=>singleItem.ysshSingleId===unitProject.spId):null;
                    //singleProject?unitParentid = singleProject.sequenceNbr:unitParentid = unitProject.spId+"_ss";
                    singleProject?unitParentid = singleProject.sequenceNbr:unitParentid = unitProject.spId;
                }
                unitTreeNode.parentId = unitParentid;
                unitTreeNode.libraryCode = unitProject.mainDeLibrary;
                unitTreeNode.secondInstallationProjectName = unitProject.secondInstallationProjectName;
                result.push(unitTreeNode);
            }
        })

        result.forEach(k =>{
           // k.id = Snowflake.nextId();

            if (ObjectUtils.isNotEmpty(k.sdId)){
                k.id = k.sdId;
            }else {
                k.id = k.ssId;
            }



        })
        return ResponseData.success({
            "list":result,
            "ssh":shObjArray,
        });
    }


    /**
     * 调整明细
     * @param  arg
     */
    async shSaveDetail (arg) {
        let constructId = arg.constructId
        let list = JSON.parse(arg.list);


        //审定
        let shendObj = PricingFileFindUtils.getProjectObjById(arg.constructId);

        //送审
        let sshUnitProjects = PricingFileFindUtils.getUnitList(shendObj.ysshConstructId);
        //审定
        let unitProjects = PricingFileFindUtils.getUnitList(constructId);


        //维护单位及单项的关系
        if(ObjectUtils.isNotEmpty(list)){
            //内存中挂关系
            for(let i=0;i<list.length;i++){
                let item = list[i];
                if(item.levelType === 2 && ObjectUtils.isEmpty(item.type)){
                    //单项
                    let singleProject =  PricingFileFindUtils.getSingleProject(constructId,item.id);
                    if(ObjectUtils.isNotEmpty(singleProject)){
                        singleProject.ysshSingleId = item.ysshSingleId;
                    }
                }
                if(item.levelType === 3 && ObjectUtils.isEmpty(item.type)){
                    //单位
                    let unitProject = PricingFileFindUtils.getUnit(constructId,item.parentId,item.id);
                    if(ObjectUtils.isNotEmpty(unitProject)){
                        unitProject.ysshUnitId = item.ysshUnitId;
                    }
                }

                if (item.levelType === 3 && item.type == 'delete'){
                    let sshUnit = sshUnitProjects.find(k=>k.sequenceNbr == item.ssId);

                    let cloneSdUnit = ObjectUtils.cloneDeep(sshUnit);
                    cloneSdUnit.spId = item.parentId;
                    cloneSdUnit.constructId = constructId;
                    cloneSdUnit.ysshUnitId = sshUnit.sequenceNbr;
                    cloneSdUnit.ysshSingleId = sshUnit.spId;
                    cloneSdUnit.ysshConstructId = shendObj.ysshConstructId;
                    cloneSdUnit.sequenceNbr= Snowflake.nextId();
                    sshUnit.special = 1;
                    ObjectUtils.updatePropertyValue(cloneSdUnit, 'constructId', constructId);
                    ObjectUtils.updatePropertyValue(cloneSdUnit, 'unitId', cloneSdUnit.sequenceNbr);
                    //let single = PricingFileFindUtils.getSingleProject(constructId,item.parentId);
                    let singleProject = this.getSingleProjectsById(constructId);
                    let single = singleProject.find(k =>k.sequenceNbr == item.parentId);
                    if (ObjectUtils.isNotEmpty(single)){
                        if (ObjectUtils.isNotEmpty(single.unitProjects)){
                            single.unitProjects.push(cloneSdUnit);
                            let fbfxupdateStrategy =   new UpdateStrategy({constructId,singleId:cloneSdUnit.spId,unitId:cloneSdUnit.sequenceNbr,pageType:"fbfx"});
                            let csxmupdateStrategy =   new UpdateStrategy({constructId,singleId:cloneSdUnit.spId,unitId:cloneSdUnit.sequenceNbr,pageType:"csxm"});
                            //清单工程量置为0
                            let fbfxQd = cloneSdUnit.itemBillProjects.filter(k => k.kind == '03');
                            if (ObjectUtils.isNotEmpty(fbfxQd)){
                                for (let item of fbfxQd) {
                                    await fbfxupdateStrategy.execute({pointLineId:item.sequenceNbr,upDateInfo:{column:"quantityExpression",value:"0"}});
                                }
                            }

                            //清单工程量置为0
                            let csxmQd = cloneSdUnit.measureProjectTables.filter(k => k.kind == '03');
                            if (ObjectUtils.isNotEmpty(csxmQd)){
                                for (let item of csxmQd) {
                                    await csxmupdateStrategy.execute({pointLineId:item.sequenceNbr,upDateInfo:{column:"quantityExpression",value:"0"}});
                                }
                            }
                        }
                    }
                }
            }
        }
        //审定
        unitProjects = PricingFileFindUtils.getUnitList(constructId);
        if(ObjectUtils.isNotEmpty(unitProjects) && ObjectUtils.isNotEmpty(sshUnitProjects)){
            for(let i = 0;i<unitProjects.length;i++){
                let unitProject = unitProjects[i];
                if(ObjectUtils.isNotEmpty(unitProject.ysshUnitId)){
                    let sshUnitProject = sshUnitProjects.find(item=>item.sequenceNbr === unitProject.ysshUnitId);
                    await this.init(constructId, unitProject.spId, unitProject.sequenceNbr, shendObj.ysshConstructId, sshUnitProject.spId, sshUnitProject.sequenceNbr);

                }
            }
            // let shenheObjCopy = ConvertUtil.deepCopy(shendObj);
            // let projectSequenceNbrCopy = Snowflake.nextId();
            // shenheObjCopy.sequenceNbr = projectSequenceNbrCopy;
            // ObjectUtils.updatePropertyValue(shenheObjCopy,"constructId",projectSequenceNbrCopy);
            // shenheObjCopy.oldConstructId = shendObj.sequenceNbr;
            // PricingFileWriteUtils.writeToMemory(shenheObjCopy);
            // return ResponseData.success({constructId:shenheObjCopy.sequenceNbr});
            return ResponseData.success({constructId:shendObj.sequenceNbr});
        }else{
            return ResponseData.fail('未找到单位项目');
        }
    }

    /**
     * 选择文件
     */
    async shYsfSaveLocation(arg) {

        let defaultStoragePath =  await  this.service.commonService.getSetStoragePath(null);

        const options ={
            properties: ['openDirectory'],
            defaultPath: defaultStoragePath.replace('YSF','YSH').toString(),
            filters: []
        };
        let result = dialog.showOpenDialogSync(null, options);
        if (ObjectUtils.isEmpty(result)){
            console.log("未选中任何文件夹");
            return;
        }
        //获取选中的路径
        let filePath = result[0];
        if (!FileUtils.checkFileExistence(filePath)){
            console.log("路径有误");
            return;
        }
        return filePath;

    }

    /**
     * 取消数据
     * type
     * constructId 为备份的id
     * @param {*} arg
     */
    async shRecoveryData (arg) {
        let {type, constructId,isSave} = arg;
        if(type == 'match'){
            let shenheObj = PricingFileFindUtils.getProjectObjById(constructId);
            if(ObjectUtils.isNotEmpty(isSave)&&isSave){
                if(ObjectUtils.isEmpty(shenheObj.oldConstructId)){
                    return ResponseData.fail('未找到审定项目数据');
                }
                // let shenheObj = PricingFileFindUtils.getProjectObjById(newProject.oldConstructId);
                // if(ObjectUtils.isEmpty(shenheObj)){
                //     return ResponseData.fail('未找到审定项目数据');
                // }

                let defaultStoragePath =  await  this.service.commonService.getSetStoragePath(shenheObj.constructName);
                const dialogOptions = {
                    title: '保存文件',
                    defaultPath: defaultStoragePath.replace('YSF','YSH').toString(),
                    filters: [{ name: '云算房文件', extensions: ['YSH'] }]
                };
                //相同文件，则直接打开保存
                let result = dialog.showSaveDialogSync(null, dialogOptions);
                if (result && !result.canceled) {
                    let filePath = result;
                    //查询是否覆盖路径
                    // FileUtils.isOverlayFile(filePath);
                    // 在这里处理保存文件的操作
                    if (!filePath.endsWith(".YSH")) {
                        filePath += ".YSH";
                    }
                    let sshObj = PricingFileFindUtils.getProjectObjById(shenheObj.ysshConstructId);
                    if(ObjectUtils.isEmpty(sshObj)){
                        return ResponseData.fail("未找到送审数据");
                    }
                    shenheObj.path = filePath;
                    //保存到最近使用项目，此方法会重新生成sequnceNbr
                    // FileUtils.writeUserHistoryListFile(shenheObj);
                    FileUtils.creatYshFile(sshObj,shenheObj,filePath);
                    ShenHeWinManageUtils.createWindow(shenheObj.constructName,shenheObj.sequenceNbr);
                    ProjectFileUtils.writeUserHistoryListFile(shenheObj);
                }else{
                    return ResponseData.fail('未选择保存路径');
                }
            }
            // if(ObjectUtils.isNotEmpty(shenheObj)&&ObjectUtils.isNotEmpty(shenheObj.ysshConstructId)){
            //     //删除缓存数据
            //     delete global.constructProject[shenheObj.ysshConstructId];
            // }

        }
        return ResponseData.success();
    }


    /**
     * 绑定项目关系
     * @param arg
     * @return {Promise<ResponseData|null>}
     */
    async bindingProRelation (arg) {
        let {detailId,detailLevel,matchingId,matchLevel,constructId} = arg

        if(detailLevel !== matchLevel){
            return ResponseData.fail('跨层级匹配错误')
        }
        let shendObj = PricingFileFindUtils.getProjectObjById(constructId);
        if(ObjectUtils.isEmpty(shendObj)){
            return ResponseData.fail('未找到对应审定数据');
        }
        let ysshConstructId = shendObj.ysshConstructId;
        //审定数据
        let shdMap = ConstructOperationUtil.flatConstructTreeToMapById(constructId)
        let shdConstructOperation = Array.from(shdMap.values()).map(value => value);
        //获取审定的所有单项
        let shdSingleProjects = shdConstructOperation.filter(k =>k.levelType == 2);


        //let shdSingleProjects = PricingFileFindUtils.getSingleProjectList(constructId);
        let shdUnits = PricingFileFindUtils.getUnitList(constructId);
        //送审数据
        //let sshSingleProjects = PricingFileFindUtils.getSingleProjectList(ysshConstructId);
        let sshMap = ConstructOperationUtil.flatConstructTreeToMapById(ysshConstructId)
        let sshConstructOperation = Array.from(sshMap.values()).map(value => value);
        //获取审定的所有单项
        let sshSingleProjects = sshConstructOperation.filter(k =>k.levelType == 2);

        let sshUnitProjects = PricingFileFindUtils.getUnitList(ysshConstructId);

        let result = null;
        if(detailLevel === 2){
            if(ObjectUtils.isEmpty(shdSingleProjects)||ObjectUtils.isEmpty(sshSingleProjects)){
                return ResponseData.fail('未找到对应数据');
            }
            let singleProject = shdSingleProjects.find(item => item.sequenceNbr === detailId);
            if(ObjectUtils.isEmpty(singleProject)){
                return ResponseData.fail('未找到对应审定数据');
            }

            let sshSingleProject = sshSingleProjects.find(item => item.sequenceNbr === matchingId);
            if(ObjectUtils.isEmpty(sshSingleProject)){
                return ResponseData.fail('未找到对应送审数据');
            }
            //重置为空
            let ysshSingleProject = shdSingleProjects.find(item => item.ysshSingleId === matchingId);
            if(ObjectUtils.isNotEmpty(ysshSingleProject)){
                ysshSingleProject.ysshSingleId = null;
            }

            let sshUnits = sshSingleProject.unitProjects;
            //被挂载的关系解除
            if(!ObjectUtils.isEmpty(sshUnits)){
                sshUnits.forEach(item => {
                    let shdUnit = shdUnits.find(item2 => item.sequenceNbr === item2.ysshUnitId);
                    if(ObjectUtils.isNotEmpty(shdUnit) && ObjectUtils.isNotEmpty(shdUnit.ysshUnitId)){
                        shdUnit.ysshUnitId = null
                    }
                });
            }
            //挂关系
            singleProject.ysshSingleId = sshSingleProject.sequenceNbr;
            let shdUnitProjects = singleProject.unitProjects;
            shdUnitProjects?shdUnitProjects.forEach(sdItemData => {
                //主动解除以前的关系:null;
                if(ObjectUtils.isNotEmpty(sdItemData.ysshUnitId)){
                    sdItemData.ysshUnitId = null;
                }
                let sshItemData = sshUnits ? sshUnits.find(ssQdData => {
                    if(ObjectUtils.isEmpty(ssQdData.spId) && ObjectUtils.isEmpty(sdItemData.spId)){
                        return ObjectUtils.compareStringsIgnoringSpaces(ssQdData.upName,sdItemData.upName)
                    }
                    if(ObjectUtils.isNotEmpty(ssQdData.spId) && ObjectUtils.isNotEmpty(sdItemData.spId)){
                        let shdSP = shdSingleProjects.find(item => item.sequenceNbr == sdItemData.spId)

                        return shdSP&& shdSP.ysshSingleId === ssQdData.spId&& ObjectUtils.compareStringsIgnoringSpaces(ssQdData.upName,sdItemData.upName)
                    }

                    return false;
                }) : null;
                if(ObjectUtils.isNotEmpty(sshItemData)){
                    sdItemData.ysshUnitId = sshItemData.sequenceNbr;
                }
            }):null;

        }
        if(detailLevel === 3){
            result = this.resetYsshGlId(detailId,matchingId,shdUnits,sshUnitProjects);
        }
        if(ObjectUtils.isNotEmpty(result)){
            return result;
        }
        return ResponseData.success();
    }

    resetYsshGlId(detailId,matchingId,sdUnitProjects,ssUnitProjects){

        if(ObjectUtils.isEmpty(sdUnitProjects)||ObjectUtils.isEmpty(ssUnitProjects)){
            return ResponseData.fail('未找到对应数据');
        }
        let ysshUnitProject = sdUnitProjects.find(item => item.ysshUnitId === matchingId);
        if(!ObjectUtils.isEmpty(ysshUnitProject)){
            ysshUnitProject.ysshUnitId = null;
        }
        let unitProject = sdUnitProjects.find(item => item.sequenceNbr === detailId);

        let ssUnitProject = ssUnitProjects.find(item => item.sequenceNbr === matchingId);
        if(ObjectUtils.isEmpty(unitProject)||ObjectUtils.isEmpty(ssUnitProject)){
            return ResponseData.fail('未找到对应数据');
        }
        unitProject.ysshUnitId = ssUnitProject.sequenceNbr;
        return null;
    }


    /**
     * 转为预算
     * isContainDelete = true  保留
     * isOpenYsf  == true 打开本地预算项目
     */
    async shProjectToBudget(arg) {
        let {constructId, type, saveLocation, isContainDelete, isOpenYsf} = arg;
        let result = {name:arg.name}
        let shdObj = PricingFileFindUtils.getProjectObjById(constructId);
        if(ObjectUtils.isEmpty(shdObj)){
            return ResponseData.fail('项目不存在');
        }
        let shdObjCopy = ConvertUtil.deepCopy(shdObj);
        //防止generxmljson后内存数据被覆盖
        let projectSequenceNbr = Snowflake.nextId();
        shdObjCopy.sequenceNbr = projectSequenceNbr;
        ObjectUtils.updatePropertyValue(shdObjCopy,"constructId",projectSequenceNbr);

        PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy).forEach(k =>{
            k.itemBillProjects = treeToArray(k.itemBillProjects);
            k.measureProjectTables = treeToArray(k.measureProjectTables);
        });
        let constructProject = null;

        if(type === 1){
            result.id = shdObjCopy.sequenceNbr;
            if(ObjectUtils.isNotEmpty(shdObjCopy)){
                shdObjCopy.path = null;
            }
            if(!isContainDelete){
                //let unitListCopy = this.getUnitList(shdObjCopy);
                let unitListCopy = PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy);
                let ssUnitList = PricingFileFindUtils.getUnitList(shdObj.ysshConstructId);
                if(ObjectUtils.isNotEmpty(unitListCopy)){
                    unitListCopy.forEach(item=>{

                        let ssUnit = ssUnitList.find(ssItem=>ssItem.sequenceNbr == item.ysshUnitId);
                        if(ObjectUtils.isNotEmpty(ssUnit)){
                            let ssMeasureTables = ssUnit.measureProjectTables||[];
                            let ssFbfxDeals = ssUnit.itemBillProjects||[];
                            let measureTables = item.measureProjectTables||[];
                            let fbfxDeals = item.itemBillProjects||[];
                            let newMeasureTables = measureTables.filter((sdItemData,index)=>{
                                if(ObjectUtils.isNotEmpty(sdItemData.ysshGlId)){
                                    let ssItemData = ssMeasureTables.find(ssMeasureItem=>ssMeasureItem.sequenceNbr == sdItemData.ysshGlId);
                                    if(ObjectUtils.isNotEmpty(ssItemData) && (ObjectUtils.isEmpty(sdItemData.total) || sdItemData.total === 0) && ObjectUtils.isNotEmpty(ssItemData.total) && ssItemData.total !== 0){
                                        return false;
                                    }

                                }
                                return true;
                            });
                            let newfbfxDeals = fbfxDeals.filter((sdItemData,index)=>{
                                if(ObjectUtils.isNotEmpty(sdItemData.ysshGlId)){
                                    let ssItemData = ssFbfxDeals.find(ssMeasureItem=>ssMeasureItem.sequenceNbr == sdItemData.ysshGlId);
                                    if(ObjectUtils.isNotEmpty(ssItemData) && (ObjectUtils.isEmpty(sdItemData.total) || sdItemData.total === 0) && ObjectUtils.isNotEmpty(ssItemData.total) && ssItemData.total !== 0){
                                        return false;
                                    }
                                }
                                return true;
                            });
                            item.itemBillProjects = newfbfxDeals;
                            item.measureProjectTables = newMeasureTables;
                        }
                    })
                }
            }


            //let s = toJsonYsfString(shdObjCopy);
            //内存数据写入
            // PricingFileFindUtils.getUnitListByConstructObj(shdObjCopy).forEach(k =>{
            //     k.itemBillProjects = treeToArray(k.itemBillProjects);
            //     k.measureProjectTables = treeToArray(k.measureProjectTables);
            // });
            //清除数据
            this.deleteProperty(shdObjCopy,'yssh');
            constructProject = shdObjCopy;
        } else if(type === 2){
            let sshObj = PricingFileFindUtils.getProjectObjById(shdObjCopy.ysshConstructId);
            if(ObjectUtils.isEmpty(sshObj)){
                return ResponseData.fail('送审项目不存在');
            }
            PricingFileFindUtils.getUnitListByConstructObj(sshObj).forEach(k =>{
                k.itemBillProjects = treeToArray(k.itemBillProjects);
                k.measureProjectTables = treeToArray(k.measureProjectTables);
            });
            let sshObjCopy = ConvertUtil.deepCopy(sshObj);
            result.id = sshObjCopy.sequenceNbr;
            constructProject = sshObjCopy;
        }else{
            return ResponseData.fail('参数类型错误');
        }

        result.filePath = saveLocation;
        result.constructProject = constructProject;
        if(isOpenYsf){
            let result2 = await this.exportYsfFile(result);
            if(result2.status == 200){
                //导出数据打开预算软件的窗口
                await this.service.systemService.openWindowForProject(result2.result);
                return ResponseData.success(0);
            }else{
                return result2
            }

        }else{

            return await this.exportYsfFile(result);
        }
    }

    async exportYsfFile (args){
        let {filePath, constructProject} = args;

        if (!filePath.endsWith(".YSF")) {
            filePath += ".YSF";
        }
        //查询选择的路径是否已经有被打开的文件
        let result =  await this.service.constructProjectFileService.getOneProDataByPath(filePath);

        if (!ObjectUtils.isEmpty(result)){
            let projectObj = PricingFileFindUtils.getProjectObjById(result.sequenceNbr);
            if (!ObjectUtils.isEmpty(projectObj)){
                return  ResponseData.success(2);
            }
        }
        constructProject.path = filePath;



        let map = ConstructOperationUtil.flatConstructTreeToMapByObj(constructProject);

        let constructOperation = Array.from(map.values()).map(value => value);
        //获取审定的所有单项
        let unitList = constructOperation.filter(k =>k.levelType == 3);
        unitList.forEach(k =>{
            k.itemBillProjects = arrayToTree(k.itemBillProjects);
            k.measureProjectTables = arrayToTree(k.measureProjectTables);
        });

        //重新刷新所有的项目ID
        await this.service.ysfHandlerService.creatYsfFile(constructProject);

        return  ResponseData.success(filePath);
    }
    /**
     * 删除属性
     * @param {*} obj
     * @param {*} propertyKey
     */
    deleteProperty(obj, startPropertyKey) {
        if (ObjectUtils.isEmpty(obj))return;
        for (let key in obj) {
            if (typeof obj[key] === 'object') {
                if (Array.isArray(obj[key])) {
                    // 如果属性的值是数组，则循环遍历数组并递归调用更新函数
                    obj[key].forEach((item) => {
                        this.deleteProperty(item, startPropertyKey);
                    });
                } else {
                    // 如果属性的值是对象，则递归调用更新函数
                    this.deleteProperty(obj[key], startPropertyKey);
                }
            } else if (key.startsWith(startPropertyKey) && !ObjectUtils.isEmpty(obj[key])) {
                // 如果属性的键等于目标属性键，并且属性具有值，则更新属性的值
                obj[key] = undefined ;
                //delete obj.key;
            }
        }
    }

    getSingleProjectsById(sequenceNbr){
        let map = ConstructOperationUtil.flatConstructTreeToMapById(sequenceNbr)
        let constructOperation = Array.from(map.values()).map(value => value);
        return constructOperation.filter(k =>k.levelType == 2);
    }
    getSingleProjectsByObj(obj){

        let map = ConstructOperationUtil.flatConstructTreeToMapById(sequenceNbr)
        let constructOperation = Array.from(map.values()).map(value => value);
        return constructOperation.filter(k =>k.levelType == 2);
    }



    async getUnitProject(args){
        let {constructId, singleId,unitId} = args;
        let unit = PricingFileFindUtils.getUnit(constructId, singleId,unitId);
        return  unit;
    }

}

ShenheProjectService.toString = () => '[class ShenheProjectService]';
module.exports = ShenheProjectService;
