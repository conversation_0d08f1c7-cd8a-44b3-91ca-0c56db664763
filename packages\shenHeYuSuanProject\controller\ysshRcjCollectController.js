const {ResponseData} = require("../../../common/ResponseData");
const { Controller } = require('../../../core');


/**
 * 预算审核 人材机汇总相关
 */
class YsshRcjCollectController extends Controller{

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }



    /**
     * 单位工程和工程项目 人材机 汇总比对
     * @param args
     * @returns {Promise<void>}
     */
    async ysshRcjCollectComparison(args){

        let rcjComparison = await this.service.shenHeYuSuanProject.ysshRcjCollectService.ysshRcjCollectComparison(args);

        return ResponseData.success(rcjComparison);

    }

    /**
     * 人材机更改匹配
     * @param args
     * @returns {Promise<void>}
     */
    async unitRcjChangeGL(args){

        await this.service.shenHeYuSuanProject.ysshRcjCollectService.unitRcjChangeGL(args);

        return ResponseData.success();
    }


}


YsshRcjCollectController.toString = () => '[class YsshRcjCollectController]';
module.exports = YsshRcjCollectController;