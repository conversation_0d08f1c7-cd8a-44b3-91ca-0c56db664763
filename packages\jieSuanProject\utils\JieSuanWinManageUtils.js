const {
	app: electronApp,
	dialog,
	shell,
	BrowserView,
	Notification,
	powerMonitor,
	screen,
	nativeTheme,
	BrowserWindow,
} = require('electron');
const { WinManageUtils } = require('../../../common/WinManageUtils');
const Conf = require('../../../core/config');
const EE = require('../../../core/ee');

/**
 * 结算项目窗口操作工具类
 */
class JieSuanWinManageUtils {
	/**
	 * 获取项目所有的窗口ID信息
	 * @return {*}
	 */
	getAllWindowMap() {
		if (!global.windowMap) {
			global.windowMap = new Map();
		}
		return global.windowMap;
	}

	/**
	 * 根据项目ID获取当前项目的窗口ID
	 * @param projectId 项目ID
	 */
	getChildWinIdByProjectId(projectId) {
		let winIds = this.getAllWindowMap();
		return winIds.get(projectId);
	}

	/**
	 * 判断项目窗口是否被打开
	 * @param projectId 项目ID
	 */
	projectIsOpen(projectId) {
		let winIds = this.getAllWindowMap();
		if (!winIds.get(projectId)) {
			return false;
		}
		return true;
	}

	/**
	 * 创建一个新的项目窗口
	 * @param windowName
	 * @param windowId
	 * @return {*}
	 */
	createWindow(windowName, windowId) {
		if (this.getAllWindowMap().has(windowId)) {
			// 获取对应的窗口引用
			let win = BrowserWindow.fromId(
				WinManageUtils.getAllWindowIdCache().get(windowId)
			);
			if (win.isMinimized()) {
				win.restore();
			}
			//将窗口移动到顶部
			win.moveTop();
			return;
		}
		let addr = 'http://localhost:8080';
		const config = Conf.all();
		if (config.env == 'prod') {
			const mainServer = config.mainServer;
			addr =
				mainServer.protocol + mainServer.host + ':' + mainServer.port;
		}
		let opt = {
			title: windowName,
		};
		const addonWindow = EE.app.addon.window;
		const win = addonWindow.create(windowId, opt);
		//const winContentsId = win.webContents.id;
		let content =
			addr +
			'#/projectDetail/customize?constructSequenceNbr=' +
			windowId +
			'&type=jieSuan';
		//全局map
		this.getAllWindowMap().set(windowId, win.id);
		//添加窗口关闭事件
		win.on('closed', () => {
			this.getAllWindowMap().delete(windowId);
			global.windowMap[windowId] = null;
		});
		win.loadURL(content);
		// win.openDevTools();
		return win;
	}
}

module.exports = {
	JieSuanWinManageUtils: new JieSuanWinManageUtils(),
};
