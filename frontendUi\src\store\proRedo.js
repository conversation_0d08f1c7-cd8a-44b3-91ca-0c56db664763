/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-02 09:02:03
 * @LastEditors: kong<PERSON>qiang
 * @LastEditTime: 2025-01-16 11:22:33
 */
import { defineStore } from 'pinia';
import redoData from '@/hooks/redoData';

export const proRedo = defineStore('proRedo', {
  state: () => {
    return {
      localRedoList: [], //本地历史记录数据，用于记录历史数据所属页面
      doList: [], //内存回退列表
      noMatchedRedoList: [], //未匹配的历史记录，用于监听修改数据
    };
  },
  actions: {
    addData(data) {
      this.localRedoList.push(data);
    },
    addDolist(data) {
      // 使用Map来存储this.localRedoList中sequenceNbr与对应对象的索引的映射
      const localRedoIndexes = new Map();
      this.localRedoList.forEach((item, index) =>
        localRedoIndexes.set(item.id, index)
      );
      this.localRedoList.forEach(itemB => {
        const matchingItemA = redoData.paramsDataMap.find(
          itemA => itemA.channel.includes(itemB.channel)
        );
        if (matchingItemA) {
          itemB.name =  replaceTemplate(itemB.name, matchingItemA, itemB.params);
        } else {
          console.log(
            `redoData在数组 a 中未找到匹配的 channel: ${itemB.channel}`
          );
        }
      });
      // 遍历this.noMatchedRedoList，找出与this.localRedoList中sequenceNbr相同的对象，并进行重新赋值
      // 从后向前遍历this.noMatchedRedoList，找出与this.localRedoList中sequenceNbr相同的最后一个对象
      for (let i = this.noMatchedRedoList.length - 1; i >= 0; i--) {
        const noMatchitem = this.noMatchedRedoList[i];
        if (localRedoIndexes.has(noMatchitem.sequenceNbr)) {
          // if(redoData.find(a=>a.channel === this.noMatchedRedoList[i].channel)){
          //   console.log('dataaa',redoData.find(a=>a.channel === this.noMatchedRedoList[i].channel))
          // }

          const index1 = localRedoIndexes.get(noMatchitem.sequenceNbr);
          if (typeof this.noMatchedRedoList[i].checkType === 'boolean'||typeof this.noMatchedRedoList[i].checkType === 'number') {
            this.localRedoList[index1].name = replaceCheckTemplate(
              this.localRedoList[index1].checkName,
              noMatchitem
            );
          } else {
            if (this.noMatchedRedoList[i].newValue != null) {
              this.localRedoList[index1].name = replaceTemplate(
                this.localRedoList[index1].name,
                noMatchitem
              );
            } else {
              this.localRedoList.splice(index1, 1);
            }
          }
          if(this.localRedoList[index1])
          this.localRedoList[index1].isBind = true;
          this.noMatchedRedoList.splice(i, 1);
          break;
        }
      }
      this.localRedoList.map((item, index) => {
        let undoIndex = data.undo.findIndex(
          a => a.sequenceNbr === item.sequenceNbr
        );
        let redoIndex = data.redo.findIndex(
          a => a.sequenceNbr === item.sequenceNbr
        );
        if (undoIndex != -1) {
          if (item.name) {
            data.undo[undoIndex].name = item.name;
          }
        }
        if (redoIndex != -1) {
          data.redo[redoIndex].name = item.name;
        }
        if (undoIndex != -1 && redoIndex != -1) {
          this.localRedoList.splice(index, 1);
        }
      });
      // 匹配勾选模板
      function replaceCheckTemplate(template, values) {
        const result = template.replace(/{([^}]+)}/g, (match, key) => {
          console.log('values[key]', values[key]);
          if (typeof values[key] === 'boolean') {
            return values[key] ? '勾选' : '取消勾选';
          } else if (typeof values[key] === 'number') {
            return values[key] === 1 ? '勾选' : '取消勾选';
          } else {
            return values[key] || '';
          }
        });
        return result;
      }
      // 匹配模板
      function replaceTemplate(template, values, params) {
        const result = template
          .replace(/由 \【{oldValue}】/g, match => {
            if (
              values.oldValue === '' ||
              values.oldValue === null ||
              values.oldValue === undefined
            ) {
              return '';
            }
            return match;
          })
          .replace(/{([^}]+)}/g, (match, key) => {
            if (params) {
              // 先检查是否在values中有对应的映射配置
              const mapping = values[key]?.find(a => a.key === params[key]);
              if (mapping) {
                return mapping.value;
              }
              // 如果没有映射配置，直接返回参数值
              return params[key] ?? '';
            }
            return values[key] ?? '';
          });
        console.log('values', template, values, result);
        return result;
      }
      this.doList = data;
    },
    addnoMatchedRedoList(data) {
      console.log('addnoMatchedRedoList',data)
      const index = this.noMatchedRedoList.findIndex(
        item => item.sequenceNbr === data.sequenceNbr && !item.isBind
      );

      // 创建完整的 redo 数据对象
      const redoItem = {
        ...data,
        id: data.sequenceNbr, // 确保有 id 字段
        isBind: false, // 标记为未绑定
      };

      // 如果找到了，就替换掉原来的对象；否则，添加新对象到列表末尾
      if (index !== -1) {
        this.noMatchedRedoList[index] = redoItem;
      } else {
        this.noMatchedRedoList.push(redoItem);
      }
    },
  },
  persist: {
    // 指定需要持久化的状态
    keys: ['localRedoList', 'doList', 'noMatchedRedoList'],
    // 可选：指定存储位置（localStorage 或 sessionStorage）
    storage: sessionStorage,
  },
});
