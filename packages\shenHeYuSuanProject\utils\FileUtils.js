const fs = require("fs");
const path = require("path");
const {writeFile} = require("fs");
const os = require("os");
const JSZip = require("jszip");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ConstructProject} = require("../../../electron/model/ConstructProject");
const {FileLevelTreeNode} = require("../../../electron/model/FileLevelTreeNode");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {toJsonYsfString} = require("../../../electron/main_editor/util");
const {ConstructOperationUtil} = require("../../../electron/utils/ConstructOperationUtil");

class FileUtils {
    // 检查文件是否存在
    checkFileExistence(filePath) {
        try {
            fs.accessSync(filePath, fs.constants.F_OK);
            return true;
        } catch (err) {
            return false;
        }
    }


    /**
     * 只有在初始化工程项目的时候才会使用该方法
     * @param obj
     * @return {*}
     */
    async creatYshFile(sshObj, shdOj, filePath) {
        if (!sshObj instanceof ConstructProject || !shdOj instanceof ConstructProject) {
            throw new Error("参数有误");
        }
        let allObj = {
            "ssh":toJsonYsfString(sshObj),
            "shd":toJsonYsfString(shdOj)
        }
        let data = ObjectUtils.toJsonString(allObj);

        //let data = toJsonYsfString(obj)
        //data = this.encryptData(data);
        // 将数据写入文件
        // writeFile(filePath, data, (err) => {
        //     if (err) {
        //         console.error('写入文件时发生错误：', err);
        //         return;
        //     }
        //     console.log('数据已成功写入文件！');
        // });
        // 创建一个新的压缩包实例
        const zip = new JSZip();
        // 添加JSON数据到压缩包中
        zip.file('file.json', data);

        /*
         * 添加依据文件数据
         */
        this.addYiJuFiles(shdOj, zip);

        // 生成压缩包
        await zip.generateAsync({ type: 'nodebuffer' }).then(function (content) {
            // 将压缩包数据写入磁盘并将后缀名改为ysf
            fs.writeFileSync(filePath, content);
        }).catch(function (error) {
            console.error('创建压缩包时发生错误:', error);
        });
        return allObj;
    }

    /**
     * 更新ysh文件
     * @return {Promise<*>}
     * @param sshObj 送审数据
     * @param shdOj  审定数据
     */
    async updateYshFile(sshObj, shdOj) {
        if (!sshObj instanceof ConstructProject || !shdOj instanceof ConstructProject) {
            throw new Error("参数有误");
        }
        let allObj = {
            "ssh":sshObj,
            "shd":shdOj
        }
        let data = ObjectUtils.toJsonString(allObj);
        // 将数据写入文件
        // writeFile(shdOj.path, data, (err) => {
        //     if (err) {
        //         console.error('写入文件时发生错误：', err);
        //         return;
        //     }
        //     console.log('数据已成功写入文件！');
        // });
        // 创建一个新的压缩包实例
        const zip = new JSZip();
        // 添加JSON数据到压缩包中
        zip.file('file.json', data);
        /*
         * 添加依据文件数据
         */
        await this.addYiJuFiles(shdOj, zip);
        // 生成压缩包
        await zip.generateAsync({ type: 'nodebuffer' }).then(function (content) {
            // 将压缩包数据写入磁盘并将后缀名改为ysf
            fs.writeFileSync(shdOj.path, content);
        }).catch(function (error) {
            console.error('创建压缩包时发生错误:', error);
        });
        return allObj;
    }

    /**
     * 更新ysh文件
     * @param obj
     * @return {Promise<*>}
     */
    async loadYshFile(path) {
        // try {
        //     const data = await fs.promises.readFile(path, 'utf8');
        //     const jsonObject = JSON.parse(data);
        //     return jsonObject;
        // } catch (err) {
        //     console.error('Error reading or parsing the JSON:', err);
        //
        //     return null;
        // }
        try {
            // // 读取压缩包文件
            const zipFileContent = fs.readFileSync(path);
            // 创建一个新的JSZip实例
            const zip = new JSZip();
            // 读取压缩包
            const zipObject = await zip.loadAsync(zipFileContent);
            // 读取指定文件的内容
            const fileContent = await zipObject.file("file.json").async("string");
            //审核文件全对象
            const allObj = JSON.parse(fileContent);
            //解压依据文件到本地目录
            await this.unZipYiJuFiles(zipObject, allObj.shd);
            return allObj;
        } catch (error) {
            throw error;
        }

    }


    /**
     * 生成树形结构
     * @param {*} projectObj
     * @param {*} result
     * @returns
     */
    async generateLevelTreeNode(projectObj, result) {
        let {sequenceNbr,ysshConstructId} = projectObj;
        let isFlag = false;
        if(ObjectUtils.isNotEmpty(ysshConstructId)){
            //审定项目
            isFlag = true;
        }
        let map = ConstructOperationUtil.flatConstructTreeToMapByObj(projectObj);
        //
        let constructOperation = Array.from(map.values()).map(value => value);

        constructOperation.forEach(k =>{
            //生成树节点
            let node = new FileLevelTreeNode();
            node.levelType = k.levelType;
            if (k.levelType == 1){
                if (isFlag){
                    node.sdId = projectObj.sequenceNbr;
                    node.sdName = projectObj.oldConstructName;
                }else {
                    node.ssId = projectObj.sequenceNbr;
                    node.ssName = projectObj.constructName;
                }
                node.biddingType = projectObj.biddingType;
            }
            //单项
            if (k.levelType == 2){
                let {sequenceNbr,projectName,ysshSingleId} = k;
                //每一个单项数据
                if (isFlag){
                    node.sdId = sequenceNbr;
                    node.sdName = projectName;
                    if (ObjectUtils.isNotEmpty(ysshSingleId))node.ysshSingleId = ysshSingleId;
                }else {
                    node.ssId = sequenceNbr;
                    node.ssName = projectName;
                }
                node.parentId = k.parentId;
            }
            //单位
            if (k.levelType == 3){
                let {sequenceNbr,upName,ysshUnitId,constructMajorType,mainDeLibrary,secondInstallationProjectName} = k;
                if (isFlag){
                    node.sdId = sequenceNbr;
                    node.sdName = upName;
                    if (ObjectUtils.isNotEmpty(ysshUnitId))node.ysshUnitId = ysshUnitId;
                }else {
                    node.ssId = sequenceNbr;
                    node.ssName = upName;
                }
                node.constructMajorType = constructMajorType;
                node.parentId = k.spId;
                node.libraryCode = mainDeLibrary;
                node.secondInstallationProjectName =secondInstallationProjectName;
            }
            result.push(node);
        });
        return result;
    }

    /**
     * 复制文件
     * @param src 原文件目录
     * @param dest 目标文件目录
     * @returns {Promise<void>}
     */
    async copyFile(src, dest) {
        const destDir = path.dirname(dest);
        if (!fs.existsSync(destDir)) {
            fs.mkdirSync(destDir, { recursive: true });
        }
        try{
            fs.copyFileSync(src, dest);
            console.log("复制文件成功！%s", dest);
        }catch (e){
            console.error(e);
        }
    }

    /**
     * 获取文件信息
     * 文件名：fileName，文件路径（不含文件名）：filePathDir，文件大小(字节)：fileSize, 文件后缀：fileExtension
     * @param filePath
     * @returns {Promise<{}>}
     */
    async getFileInfo(filePath){
        let fileInfo = {};
        let stats;//文件状态
        try {
            stats = fs.statSync(filePath);
        } catch (err) {
            console.error('无法获取文件信息：', err);
            return null;
        }
        // 获取文件名
        const fileName = path.basename(filePath);
        // 获取文件路径（不含文件名）
        const filePathDir = path.dirname(filePath);
        // 获取文件大小
        const fileSize = stats.size;
        // 获取文件后缀
        const fileExtension = path.extname(fileName);
        fileInfo.filePathDir = filePathDir;//E:\\test\\data"
        fileInfo.fileName = fileName;//demo.txt
        fileInfo.fileExtension = fileExtension;//.txt
        fileInfo.fileSize = fileSize;//5237
        // console.log(ObjectUtils.toJsonString(fileInfo));
        return fileInfo;
    }

    /**
     * 添加依据文件数据
     * @param shdOj 审定数据工程项目数据
     * @param zip 压缩文件
     */
    async addYiJuFiles(shdOj, zip) {
        try{
            //审定工程ID
            const sdConstructId = shdOj.sequenceNbr;
            //根存储目录
            let filesStoragePath = await this.getFilesStoragePath();
            //依据文件存储目录:将此目录下文件打包进zip
            let yjStoragePath = filesStoragePath.concat(path.sep).concat('shenHe')
                .concat(path.sep).concat(sdConstructId);
                // .concat('ysYiJuFiles');
            //将文件夹下所有文件添加到压缩文件
            await this.addFilesToZip(zip, yjStoragePath);
        }catch (err) {
            console.error('添加依据文件出错：', err);
        }
    }

    /**
     * 添加文件到压缩包
     * @param zip
     * @param dirPath 目录路径
     * @param relativePath 相对路径
     */
    async addFilesToZip(zip, dirPath, relativePath = '') {
        if(!fs.existsSync(dirPath)){
            console.log("保存审核文件，依据文件数据为空:%s", dirPath);
            return;
        }
        const entries = fs.readdirSync(dirPath, { withFileTypes: true });
        if(ObjectUtils.isEmpty(entries)){
            console.log("保存审核文件，依据文件数据为空:%s", dirPath);
            return;
        }
        for (const entry of entries) {
            const fullPath = path.join(dirPath, entry.name);
            const relPath = path.join(relativePath, entry.name);
            if (entry.isDirectory()) {
                //递归添加所有子文件
                await this.addFilesToZip(zip, fullPath, relPath);
            } else if(fs.existsSync(fullPath)){
                const data = fs.readFileSync(fullPath);
                zip.file(relPath, data);
                console.log("添加文件到压缩包:%s", fullPath);
            }
        }
    }

    /**
     * 解压ZIP中的依据文件
     * @param zipObject
     * @param shdObj 审定数据
     * @returns {Promise<void>}
     */
    async unZipYiJuFiles(zipObject, shdObj) {
        try{
            if(!shdObj){
                console.log('未获取到审定数据');
                return;
            }
            //审定工程ID
            const sdConstructId = shdObj.sequenceNbr;
            //根存储目录
            let filesStoragePath = await this.getFilesStoragePath();
            //依据文件存储目录:将此目录下文件打包进zip
            let yjStoragePath = filesStoragePath.concat(path.sep).concat('shenHe')
                .concat(path.sep).concat(sdConstructId);
            //解压zip到指定目录
            const ignoreFiles = ['file.json', 'file.xml'];
            await this.unZipFilesForZipObj(zipObject, yjStoragePath, ignoreFiles);
        }catch (err) {
            console.error('解压ZIP中的依据文件出错：', err);
        }

    }

    /**
     * 获取根存储目录
     * @returns {Promise<{}>}
     */
    async getFilesStoragePath() {
        const baseDataDir = `${os.homedir()}\\.xilidata\\userHistory.json`;
        //读取数据
        const data = fs.readFileSync(baseDataDir, 'utf8');
        const userHistoryData = JSON.parse(data);
        let result = {content:userHistoryData.DEF_SAVE_PATH}
        //此处需要判断是否登录 正常登录  离线登录  未登录
        if(ObjectUtils.isEmpty(global.idInformation)){
            return `${result.content}`;
        }else {
            //获取用户信息
            let sequenceNbr = global.idInformation.sequenceNbr;
            let identity = global.idInformation.identity;
            return `${result.content}\\${sequenceNbr}\\${identity}`;
        }
    }

    /**
     * 解压文件到指定目录
     * 忽略 file.json 和 file.xml
     * @param zipObject
     * @param storagePath
     * @param ignoreFiles
     * @returns {Promise<void>}
     */
    async unZipFilesForZipObj(zipObject, storagePath, ignoreFiles) {
        // 遍历 ZIP 文件中的每个文件或文件夹
        zipObject.forEach((relativePath, zipEntry) => {
            // 构建目标路径
            const targetPath = path.join(storagePath, relativePath);
            //是否解压此文件
            const isUnzip = !ignoreFiles || !ignoreFiles.some(itemName => itemName === zipEntry.name);
            if(isUnzip){
                // 如果是文件，则解压到目标路径
                if (!zipEntry.dir) {
                    //忽略指定文件
                    zipEntry.async('nodebuffer').then((data) => {
                        //文件目录不存在则创建
                        const targetDir = path.dirname(targetPath);
                        if (!fs.existsSync(targetDir)) {
                            fs.mkdirSync(targetDir, { recursive: true });
                        }
                        //生成文件
                        fs.writeFileSync(targetPath, data);
                        console.log(`解压完成: ${targetPath}`);
                    });

                } else {
                    // 如果是文件夹，则创建对应的目录
                    if (!fs.existsSync(targetPath)) {
                        fs.mkdirSync(targetPath, { recursive: true });
                        console.log(`创建目录: ${targetPath}`);
                    }
                }
            }
        });
        console.log('解压完成');
    }

    /**
     * 解压文件到指定目录
     * @param zipPath zip文件路径
     * @param storagePath 目标目录
     * @param ignoreFiles const ignoreFiles = ['file.json', 'file.xml'];
     * @returns {Promise<void>}
     */
    async unZipFiles(zipPath, storagePath, ignoreFiles){
        // // 读取压缩包文件
        const zipFileContent = fs.readFileSync(path);
        // 创建一个新的JSZip实例
        const zip = new JSZip();
        // 读取压缩包
        const zipObject = await zip.loadAsync(zipFileContent);
        //开始解压文件
        await this.unZipFilesForZipObj(zipObject, storagePath, ignoreFiles);
    }
/**
     * 写入用户文件列表历史数据
     * @param path
     * @param data
     */
writeUserHistoryListFile(obj) {
    //获取用户文件列表数据
    let userHistoryData = PricingFileFindUtils.userHistoryData();
    let idKey = PricingFileFindUtils.userInfoData();

    let userHistoryFileList = userHistoryData[idKey];
    if (ObjectUtils.isEmpty(userHistoryFileList)){
        userHistoryData[idKey] = [];
    }

    let result = userHistoryData[idKey].filter(k =>k.path === obj.path && k.sequenceNbr ===obj.sequenceNbr);
    //历史记录路径
    let userPath = PricingFileFindUtils.userHistoryDataPath();

    //如果是新打开的一个文件 则需要先入记录到表里面
    if (ObjectUtils.isEmpty(result)){

        result = {
            sequenceNbr:obj.sequenceNbr,
            constructName:obj.constructName,//无语
            path:obj.path,
            openTime:DateUtils.now('YYYY-MM-DD HH:mm:ss')
        };
        userHistoryData[idKey].push(result);
    }else {
        result[0].openTime = DateUtils.now('YYYY-MM-DD HH:mm:ss');
    }
    //数据写入历史文件中
    PricingFileWriteUtils.writeUserHistoryFile(userPath,userHistoryData);
    return obj;
}
}
module.exports = {
    FileUtils: new FileUtils()
};
