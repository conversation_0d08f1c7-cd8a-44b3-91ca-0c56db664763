const {FileUtils} = require("../utils/FileUtils");
const {ResponseData} = require("../../../common/ResponseData");
const { Controller } = require('../../../core');


/**
 * 预算审核 导入依据
 */
class YsshYiJuController extends Controller{

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }



    /**
     * 导入依据文件
     * @param args
     * @returns {Promise<void>}
     */
    async importYiJuFile(args){

        let rcjComparison = await this.service.shenHeYuSuanProject.ysshYiJuService.importYiJuFile(args);

        return ResponseData.success(rcjComparison);

    }

    /**
     * 删除清单依据文件
     * @param args
     * @returns {Promise<*|string>}
     */
    async removeYiJuFile(args) {

        let result = await this.service.shenHeYuSuanProject.ysshYiJuService.removeYiJuFile(args);
        return ResponseData.success(result);
    }


    /**
     * 打开依据文件
     * @param args
     * @returns {Promise<*>}
     */
    async openYiJuFile(args) {
        //返回文件流
        const {filePath} = args;
        if(!FileUtils.checkFileExistence(filePath)){
            console.error("依据文件不存在，filePath=%s！" + filePath);
            return ResponseData.fail("依据文件不存在！");
        }
        //打开文件
        if(await this.service.shenHeYuSuanProject.ysshYiJuService.openYiJuFile(args)){
            console.log("依据文件打开成功！");
            return ResponseData.success("打开文件成功！");
        }else{
            return ResponseData.fail("依据文件打开失败！");
        }
    }

}


YsshYiJuController.toString = () => '[class YsshYiJuController]';
module.exports = YsshYiJuController;