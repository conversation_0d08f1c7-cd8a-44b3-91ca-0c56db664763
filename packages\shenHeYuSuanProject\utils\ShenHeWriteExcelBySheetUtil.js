// exceljs 所需的 polyfills
// require('core-js/modules/es.promise');
// require('core-js/modules/es.string.includes');
// require('core-js/modules/es.object.assign');
// require('core-js/modules/es.object.keys');
// require('core-js/modules/es.symbol');
// require('core-js/modules/es.symbol.async-iterator');
// require('regenerator-runtime/runtime');

// import ExcelJS from 'exceljs';
const ExcelJS = require('exceljs');
const CellVo = require("../../../electron/vo/CellVo");
const SheetStyle = require("../../../electron/vo/SheetStyle");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ExcelUtil} = require("../../../electron/utils/ExcelUtil");
const {Service} = require('../../../core');
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const Decimal = require("decimal.js");

class ShenHeWriteExcelBySheetUtil {

    constructor() {
    }

    /********工程项目层级*************/
    //1、【封面1】工程审核书封面
    async writeDataToProjectSheet1(data, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "项目名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充审定时间
        let importCell = ExcelUtil.findValueCell(worksheet, "日期：");
        let filterImport = data.filter(object => object.name == "审定时间")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[1].value = "日期：" + filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);

        //填充编制单位名称：
        let importCompanyCell = ExcelUtil.findValueCell(worksheet, "编制单位名称： ");
        let filterCompanyImport = data.filter(object => object.name == "审定单位")[0];
        let importCompanyRow = worksheet.getRow(importCompanyCell.cell._row._number);
        if (filterCompanyImport != null && filterCompanyImport.remark != null) {
            importCompanyRow._cells[1].value = "编制单位名称：" + filterCompanyImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importCompanyRow);
    }


    //2、【封面2】工程审核书签署页
    async writeDataToProjectSheet2(data, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "项目名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充编制人
        let importCell = ExcelUtil.findValueCell(worksheet, "编 制 人：");
        let filterImport = data.filter(object => object.name == "编制人")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[1].value = "编 制 人：" + filterImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);

        //填充审核人
        let importCell1 = ExcelUtil.findValueCell(worksheet, "审 核 人：");
        let filterImport1 = data.filter(object => object.name == "审核人")[0];
        let importRow1 = worksheet.getRow(importCell1.cell._row._number);
        if (filterImport1 != null && filterImport1.remark != null) {
            importRow1._cells[1].value = "审 核 人：" + filterImport1.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);

        //填充审定人
        let importCell2 = ExcelUtil.findValueCell(worksheet, "审 定 人：");
        let filterImport2 = data.filter(object => object.name == "审定人")[0];
        let importRow2 = worksheet.getRow(importCell2.cell._row._number);
        if (filterImport2 != null && filterImport2.remark != null) {
            importRow2._cells[1].value = "审 定 人：" + filterImport2.remark;
        }

        //填充法定代表人或其授权人
        let importCell3 = ExcelUtil.findValueCell(worksheet, "法定代表人或其授权人：");
        let filterImport3 = data.filter(object => object.name == "审核单位法定代表人或其授权人")[0];
        let importRow3 = worksheet.getRow(importCell3.cell._row._number);
        if (filterImport3 != null && filterImport3.remark != null) {
            importRow3._cells[1].value = "法定代表人或其授权人：" + filterImport3.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow);
    }


    //3、【封面3】审核签署表
    async writeDataToProjectSheet3(data, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充工程地址
        // let importCell = ExcelUtil.findValueCell(worksheet, "工程地址");
        // let filterImport = data.filter(object => object.name == "工程地址")[0];
        // let importRow = worksheet.getRow(importCell.cell._row._number);
        // if (filterImport != null && filterImport.remark != null) {
        //     importRow._cells[6].value = filterImport.remark;
        // }
        // ExcelUtil.traversalRowToCellBottom(importRow);

        //填充审定日期
        let importCell1 = ExcelUtil.findValueCell(worksheet, "审定日期");
        let filterImport1 = data.filter(object => object.name == "审定时间")[0];
        let importRow1 = worksheet.getRow(importCell1.cell._row._number);
        if (filterImport1 != null && filterImport1.remark != null) {
            importRow1._cells[6].value = filterImport1.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow1);


        //填充报审造价
        let importCellPrice = ExcelUtil.findValueCell(worksheet, "报审造价");
        let filterImportPrice = data.filter(object => object.name == "送审造价")[0];
        let importRowPrice = worksheet.getRow(importCellPrice.cell._row._number);
        if (filterImportPrice != null && filterImportPrice.remark != null) {
            importRowPrice._cells[1].value = filterImportPrice.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRowPrice);


        //填充调整金额（+、—）
        let importCell3 = ExcelUtil.findValueCell(worksheet, "调整金额（+、—）");
        let filterImport3 = data.filter(object => object.name == "调整金额（+、—）")[0];
        let importRow3 = worksheet.getRow(importCell3.cell._row._number);
        if (filterImport3 != null && filterImport3.remark != null) {
            importRow3._cells[6].value = filterImport3.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow3);

        //填充大写
        let importCell2 = ExcelUtil.findValueCell(worksheet, "大写");
        let filterImport2 = data.filter(object => object.name == "审定造价大写")[0];
        let importRow2 = worksheet.getRow(importCell2.cell._row._number);
        if (filterImport2 != null && filterImport2.remark != null) {
            importRow2._cells[2].value = NumberUtil.numToCny(filterImport2.remark);
        }
        ExcelUtil.traversalRowToCellBottom(importRow2);

        //填充小写
        let importCell5 = ExcelUtil.findValueCell(worksheet, "小写");
        let filterImport5 = data.filter(object => object.name == "审定造价")[0];
        let importRow5 = worksheet.getRow(importCell5.cell._row._number);
        if (filterImport5 != null && filterImport5.remark != null) {
            importRow5._cells[6].value = filterImport5.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRow5);
    }


    //4、【封面4】审核签署表
    async writeDataToProjectSheet4(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        let totalS = "公司：\t\n" +
            "    你单位送来的工程结算资料，已经我公司审核完毕。该工程本次审核" +
            `范围：,送审金额为：{SSJE}元，审定金额为：{SDJE}元,核减` +
            "金额为：{HJJE}元，核增金额为：{HZJE}元。（详见右表）\t\n" +
            "    请你单位会同施工单位复核认证，签注意见并加盖单位公章退还我公" +
            "司。\t\n" +
            "                                      有限公司\t\n";

        let SSZJ = data.filter(object => object.name == "送审造价")[0];
        let SSZJMoney = null;
        if (SSZJ != null && SSZJ.remark != null) {
            SSZJMoney = SSZJ.remark;
        } else {
            SSZJMoney = "";
        }
        let SDZJ = data.filter(object => object.name == "审定造价")[0];
        let SDZJMoney = null;
        if (SDZJ != null && SDZJ.remark != null) {
            SDZJMoney = SDZJ.remark;
        } else {
            SDZJMoney = "";
        }
        let TZJE = data.filter(object => object.name == "调整金额（+、—）")[0];
        let HJJEMoney = null;
        let HZJEMoney = null;
        if (TZJE != null && TZJE.remark != null) {
            if (TZJE.remark == 0) {
                HJJEMoney = "";
                HZJEMoney = "";
            } else if (TZJE.remark > 0) {
                HJJEMoney = "";
                HZJEMoney = TZJE.remark;
            } else if (TZJE.remark < 0) {
                HJJEMoney = Math.abs(TZJE.remark);
                HZJEMoney = "";
            }
        }

        totalS = totalS.replace("{SSJE}", SSZJMoney);
        totalS = totalS.replace("{SDJE}", SDZJMoney);
        totalS = totalS.replace("{HJJE}", HJJEMoney);
        totalS = totalS.replace("{HZJE}", HZJEMoney);

        let totalSS = {};
        totalSS.name = "totalS";
        totalSS.remark = totalS;
        data.push(totalSS);

        //填充第一列的合并数据
        // let importCell5 = ExcelUtil.findValueCell(worksheet, "${summary}");
        // let importRow5 = worksheet.getRow(importCell5.cell._row._number);
        // importRow5._cells[0].value = totalS;
        // ExcelUtil.traversalRowToCellBottom(importRow5);

        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 5;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = "";
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].projectName;
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.gczj)) {
                        cell.value = list[countRow].ysshSysj.gczj;
                    }
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].gczj;
                }
                if (cell.col == 6) {
                    if (list[countRow].changeTotal > 0) {
                        cell.value = list[countRow].changeTotal;
                    } else {
                        cell.value = "";
                    }
                }
                if (cell.col == 7) {
                    if (list[countRow].changeTotal < 0) {
                        let number = Math.abs(list[countRow].changeTotal);
                        cell.value = number;
                    } else {
                        cell.value = "";
                    }
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].changeRatio;
                }
            }
        }

        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[3].value = SSZJMoney;//送审金额合计
        row._cells[4].value = SDZJMoney;//审定金额合计
        row._cells[5].value = HZJEMoney;//审增合计
        row._cells[6].value = Math.abs(HJJEMoney);//审减合计
    }


    //5、【封面5】【封面5】工程造价审查书
    async writeDataToProjectSheet5(data, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        //填充工程地址
        let importCell = ExcelUtil.findValueCell(worksheet, "送审工程造价：");
        let filterImport = data.filter(object => object.name == "送审造价")[0];
        let importRow = worksheet.getRow(importCell.cell._row._number);
        if (filterImport != null && filterImport.remark != null) {
            importRow._cells[5].value = filterImport.remark + " 元";
        }
        ExcelUtil.traversalRowToCellBottom(importRow);

        //填充审定日期
        let importCell1 = ExcelUtil.findValueCell(worksheet, "审定工程造价：");
        let filterImport1 = data.filter(object => object.name == "审定造价")[0];
        let importRow1 = worksheet.getRow(importCell1.cell._row._number);
        if (filterImport1 != null && filterImport1.remark != null) {
            importRow1._cells[5].value = filterImport1.remark + " 元";
        }
        ExcelUtil.traversalRowToCellBottom(importRow1);


        //填充报审造价
        let importCellDate = ExcelUtil.findValueCell(worksheet, "日期：");
        let filterImportDate = data.filter(object => object.name == "审定时间")[0];
        let importRowDate = worksheet.getRow(importCellDate.cell._row._number);
        if (filterImportDate != null && filterImportDate.remark != null) {
            importRowDate._cells[2].value = "日期：" + filterImportDate.remark;
        }
        ExcelUtil.traversalRowToCellBottom(importRowDate);
    }


    //6、【项1】工程审核汇总对比表
    async writeDataToProjectSheet6(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        let SSZJ = data.filter(object => object.name == "送审造价(含设备费及其税金)")[0];
        let SSZJMoney = null;
        if (SSZJ != null && SSZJ.remark != null) {
            SSZJMoney = SSZJ.remark;
        } else {
            SSZJMoney = "";
        }
        let SDZJ = data.filter(object => object.name == "审定造价(含设备费及其税金)")[0];
        let SDZJMoney = null;
        if (SDZJ != null && SDZJ.remark != null) {
            SDZJMoney = SDZJ.remark;
        } else {
            SDZJMoney = "";
        }
        let TZJE = data.filter(object => object.name == "调整金额（+、—）")[0];
        let TZJEMoney = null;
        if (TZJE != null && TZJE.remark != null) {
            TZJEMoney = TZJE.remark;
        } else {
            TZJEMoney = "";
        }

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].projectName;
                }
                if (cell.col == 3) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.gczj)) {
                        cell.value = list[countRow].ysshSysj.gczj;
                    }
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.sbfsj)) {
                        cell.value = list[countRow].ysshSysj.sbfsj;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.gczj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.sbfsj)) {
                        cell.value = list[countRow].ysshSysj.gczj + list[countRow].ysshSysj.sbfsj;
                    }
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].gczj;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].sbfsj;
                }
                if (cell.col == 8) {
                    if(!ObjectUtils.isUndefined(list[countRow].sbfsj)){
                        cell.value = NumberUtil.add(list[countRow].gczj, list[countRow].sbfsj);
                    } else {
                        cell.value = list[countRow].gczj;
                    }
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].changeTotal;
                }
            }
        }

        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[4].value = SSZJMoney;//送审金额合计
        row._cells[7].value = SDZJMoney;//审定金额合计
        row._cells[8].value = TZJEMoney;//金额审增合计
    }


    //7、【人材机1】人材机汇总对比表
    async writeDataToProjectSheet7(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "项目名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);

        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义保留距离  ex:表1-6模板的最后两行进行保留
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 7 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.totalNumber)) {
                    cell.value = list[countRow].ysshSysj.totalNumber;
                }
                if (cell.col == 8 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.marketPrice)) {
                    cell.value = list[countRow].ysshSysj.marketPrice;
                }
                if (cell.col == 9 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                    cell.value = list[countRow].ysshSysj.total;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 13 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changePrice)) {
                    cell.value = list[countRow].ysshSysj.changePrice;
                }
                if (cell.col == 14 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                    cell.value = list[countRow].ysshSysj.changeTotal;
                }
                if (cell.col == 15 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeExplain)) {
                    cell.value = list[countRow].ysshSysj.changeExplain;
                }
            }
        }

        let {sstotal, sdtotal, changePrice, changeTotal} = data["projectRcjHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[8].value = sstotal;//送审合价合计
        row._cells[11].value = sdtotal;//审定合价合计
        row._cells[12].value = changePrice;//增减单价合计
        row._cells[13].value = changeTotal;//增减合价合计
    }


    /********单项工程层级*************/
    //1、【单项1】单项工程审核对比表
    async writeDataToSingleSheet1(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].projectName;
                }
                if (cell.col == 3 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.gczj)) {
                    cell.value = list[countRow].ysshSysj.gczj;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].gczj;
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = NumberUtil.subtract(list[countRow].gczj, list[countRow].ysshSysj.gczj).toFixed(2);
                    } else {
                        cell.value = list[countRow].gczj;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changeTotal} = data["singleHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计（不含设备及其税金）");

        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[2].value = sstotal;//送审金额合计
        row._cells[3].value = sdtotal;//审定金额合计
        row._cells[4].value = changeTotal;//增减金额合计
    }


    /********单位工程层级*************/
    //1、【费1】单位工程审核对比表
    async writeDataToUnitSheet1(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = "（" + list[countRow].instructions + "） * " + (ObjectUtils.isEmpty(list[countRow].ysshSysj.rate)?'100':list[countRow].ysshSysj.rate+'') + "%";
                    } else {
                        cell.value = "（" + list[countRow].instructions + "）";
                    }
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.price)) {
                        cell.value = list[countRow].ysshSysj.price;
                    }
                }
                if (cell.col == 5) {
                    cell.value = "（" + list[countRow].instructions + "） * " + (ObjectUtils.isEmpty(list[countRow].rate)?'100':list[countRow].rate+'') + "%";
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].remark;
                }
            }
        }
    }


    //2、【分部1】分部分项清单对比表
    async writeDataToUnitSheet2(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];     //从第五行开始写数据
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.changeExplain == "[删项]") {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.bdCode;
                    if (!ObjectUtils.isUndefined(objectInfo.redArray) && objectInfo.redArray.length > 0) {
                        cell.value = cell.value + objectInfo.redArray;
                    }
                    if (!ObjectUtils.isUndefined(objectInfo.blackArray) && objectInfo.blackArray.length > 0) {
                        cell.value = cell.value + objectInfo.blackArray;
                    }
                }
                if (cell.col == 3) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.changeExplain == "[删项]") {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    if (!ObjectUtils.isUndefined(objectInfo.projectAttr) && null != objectInfo.projectAttr) {
                        cell.value = objectInfo.name + "\t\n" + objectInfo.projectAttr;
                    } else {
                        cell.value = objectInfo.name;
                    }

                }
                if (cell.col == 4) {
                    cell.value = list[countRow].unit;
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.changeExplain == "[删项]") {
                        cell.value = list[countRow].ysshSysj.unit;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.quantity)) {
                        cell.value = list[countRow].ysshSysj.quantity;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.price)) {
                        cell.value = list[countRow].ysshSysj.price;
                    }

                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                        cell.value = list[countRow].ysshSysj.total;
                    }
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 11) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 12) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeExplain)) {
                        cell.value = list[countRow].ysshSysj.changeExplain;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changeTotal} = data["unitFbfxHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = sstotal;//送审金额合计
        row._cells[9].value = sdtotal;//审定金额合计
        row._cells[10].value = changeTotal;//增减金额合计
    }


    //3、【分部6】分部分项清单对比表(含关联项)
    async writeDataToUnitSheet3(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let listCopy = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    listCopy.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, listCopy, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1 && !ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                    cell.value = list[countRow].ysshSysj.bdCode;
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj.redArray) && list[countRow].ysshSysj.redArray.length > 0) {
                        cell.value = cell.value + list[countRow].ysshSysj.redArray;
                    }
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj.blackArray) && list[countRow].ysshSysj.blackArray.length > 0) {
                        cell.value = cell.value + list[countRow].ysshSysj.blackArray;
                    }
                }
                if (cell.col == 2 && !ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj.projectAttr) && null != list[countRow].ysshSysj.projectAttr) {
                        cell.value = list[countRow].ysshSysj.name + "\t\n" + list[countRow].ysshSysj.projectAttr;
                    } else {
                        cell.value = list[countRow].ysshSysj.name;
                    }
                }
                if (cell.col == 3 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.unit)) {
                    cell.value = cell.value = list[countRow].ysshSysj.unit;
                }
                if (cell.col == 4 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.quantity)) {
                    cell.value = list[countRow].ysshSysj.quantity;
                }
                if (cell.col == 5 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.price)) {
                    cell.value = list[countRow].ysshSysj.price;
                }
                if (cell.col == 6 && !ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                    cell.value = list[countRow].ysshSysj.total;
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].bdCode;
                    if (!ObjectUtils.isUndefined(list[countRow].redArray) && list[countRow].redArray.length > 0) {
                        cell.value = cell.value + list[countRow].redArray;
                    }
                    if (!ObjectUtils.isUndefined(list[countRow].blackArray) && list[countRow].blackArray.length > 0) {
                        cell.value = cell.value + list[countRow].blackArray;
                    }
                }
                if (cell.col == 8) {
                    if (!ObjectUtils.isUndefined(list[countRow].projectAttr) && null != list[countRow].projectAttr) {
                        cell.value = list[countRow].name + "\t\n" + list[countRow].projectAttr;
                    } else {
                        cell.value = list[countRow].name;
                    }
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 13) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 14) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeExplain)) {
                        cell.value = list[countRow].ysshSysj.changeExplain;
                    }
                }
            }
        }


        // let {sstotal, sdtotal, changeTotal} = data["unitFbfxglHeji"];
        let {sstotal, sdtotal, changeTotal} = data["unitFbfxHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = sstotal;//送审金额合计
        row._cells[11].value = sdtotal;//审定金额合计
        row._cells[12].value = changeTotal;//增减金额合计
    }

    async writeDataToUnitSheet3Copy(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标

        let hang = 0; //统计二层数组的

        for (let i = 0; i < list.length; i++) {
            for (let j = 0; j < list[i].gl.length; j++) {
                let rowObject;
                headCount++;//记录当前数据插入行的索引
                rowObject = worksheet._rows[headCount];
                let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
                let rowNext = worksheet._rows[headCount + copyDistance];
                if (rowNext == null) {
                    //插入新行后最后一行的合并单元格丢失
                    /****插入一条新行**************/
                    let list = [];
                    //为什么从前第二行复制样式 因为最后一行有粗线
                    for (let m = 0; m < rowObject._cells.length; m++) {
                        list.push("");
                    }
                    rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                    let mergeMaps = new Map(Object.entries(worksheet._merges));
                    for (let m = 0; m < rowNext._cells.length; m++) {
                        //获取模板行的合并单元格
                        let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                        if (mergeName != null) {
                            let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                            worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                            worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        }
                        rowNext._cells[m].style = rowObject._cells[m].style;
                    }
                    /**end**插入一条新行**************/
                }
                countRow = hang;
                hang++;
            }
        }


        let headCount1 = 3;//表示表头行索引的最大值
        let countRow1 = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let gl = list[i].gl;

            if (gl.length == 1) {
                countRow1++;
                headCount1++;
            } else {
                let countRowStart = headCount1 + 2;
                let countRowEnd = headCount1 + 2 + gl.length - 1;
                for (let m = 0; m < gl.length; m++) {

                    let glElement = gl[m];

                    headCount1++;

                    let rowObject = worksheet._rows[headCount1]; //该行的对象
                    for (let j = 0; j < rowObject._cells.length; j++) {
                        let cell = rowObject._cells[j];
                        if (cell.col == 1) {
                            cell.value = list[i].xmbm;
                        }
                        if (cell.col == 2) {
                            cell.value = list[i].xmmcytz;
                        }
                        if (cell.col == 3) {
                            cell.value = list[i].dw;
                        }
                        if (cell.col == 4) {
                            cell.value = list[i].ssgcl;
                        }
                        if (cell.col == 5) {
                            cell.value = list[i].sszhdj;
                        }
                        if (cell.col == 6) {
                            cell.value = list[i].sszhhj;
                        }
                        if (cell.col == 7) {
                            cell.value = glElement.xmbm;
                        }
                        if (cell.col == 8) {
                            cell.value = glElement.xmmcytz;
                        }
                        if (cell.col == 9) {
                            cell.value = glElement.dw;
                        }
                        if (cell.col == 10) {
                            cell.value = glElement.sdgcl;
                        }
                        if (cell.col == 11) {
                            cell.value = glElement.sdzhdj;
                        }
                        if (cell.col == 12) {
                            cell.value = glElement.sdzhhj;
                        }
                        if (cell.col == 13) {
                            cell.value = list[i].zjje;
                        }
                        if (cell.col == 14) {
                            cell.value = list[i].zjsm;
                        }
                    }
                    countRow1++;
                }

                //需要合并单元格
                worksheet.unMergeCells([countRowStart, 1, countRowEnd, 1]);
                worksheet.mergeCells([countRowStart, 1, countRowEnd, 1]);
                worksheet.unMergeCells([countRowStart, 2, countRowEnd, 2]);
                worksheet.mergeCells([countRowStart, 2, countRowEnd, 2]);
                worksheet.unMergeCells([countRowStart, 3, countRowEnd, 3]);
                worksheet.mergeCells([countRowStart, 3, countRowEnd, 3]);
                worksheet.unMergeCells([countRowStart, 4, countRowEnd, 4]);
                worksheet.mergeCells([countRowStart, 4, countRowEnd, 4]);
                worksheet.unMergeCells([countRowStart, 5, countRowEnd, 5]);
                worksheet.mergeCells([countRowStart, 5, countRowEnd, 5]);
                worksheet.unMergeCells([countRowStart, 6, countRowEnd, 6]);
                worksheet.mergeCells([countRowStart, 6, countRowEnd, 6]);

                worksheet.unMergeCells([countRowStart, 13, countRowEnd, 13]);
                worksheet.mergeCells([countRowStart, 13, countRowEnd, 13]);
                worksheet.unMergeCells([countRowStart, 14, countRowEnd, 14]);
                worksheet.mergeCells([countRowStart, 14, countRowEnd, 14]);
            }
        }

        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = 200;//送审金额合计
        row._cells[11].value = 100;//审定金额合计
        row._cells[12].value = -100;//增减金额合计
    }


//4、【措施1】措施项目审核对比表
    async writeDataToUnitSheet4(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.fxCode;
                }
                if (cell.col == 2) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.name;
                }
                if (cell.col == 3) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.unit;
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.quantity)) {
                        cell.value = list[countRow].ysshSysj.quantity;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.price)) {
                        cell.value = list[countRow].ysshSysj.price;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                        cell.value = list[countRow].ysshSysj.total;
                    }
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 10) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 11) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeExplain)) {
                        cell.value = list[countRow].ysshSysj.changeExplain;
                    }
                }
            }
        }


        let {sstotal, sdtotal, changeTotal} = data["unitCuoshiHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = sstotal;//送审金额合计
        row._cells[8].value = sdtotal;//审定金额合计
        row._cells[9].value = changeTotal;//增减金额合计
    }


    //5、【其他1】其他项目审核对比表
    async writeDataToUnitSheet5(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].extraName;
                }
                if (cell.col == 3) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                        cell.value = list[countRow].ysshSysj.total;
                    }
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeExplain)) {
                        cell.value = list[countRow].ysshSysj.changeExplain;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changeTotal} = data["unitOtherHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[2].value = sstotal;//送审金额合计
        row._cells[3].value = sdtotal;//审定金额合计
        row._cells[4].value = changeTotal;//
    }


    //6、【计日工1】计日工审核对比表
    async writeDataToUnitSheet6(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.dispNo;
                }
                if (cell.col == 2) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.worksName;
                }
                if (cell.col == 3) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.specification;
                }
                if (cell.col == 4) {
                    let objectInfo = {};
                    if (ObjectUtils.isNotEmpty(list[countRow].ysshSysj) && list[countRow].ysshSysj.change == 2) {
                        objectInfo = list[countRow].ysshSysj;
                    }else {
                        objectInfo = list[countRow];
                    }
                    cell.value = objectInfo.unit;
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.tentativeQuantity)) {
                        cell.value = list[countRow].ysshSysj.tentativeQuantity;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.price)) {
                        cell.value = list[countRow].ysshSysj.price;
                    }
                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                        cell.value = list[countRow].ysshSysj.total;
                    }
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].tentativeQuantity;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].price;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 11) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 12) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeExplain)) {
                        cell.value = list[countRow].ysshSysj.changeExplain;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changeTotal} = data["unitOtherJrgHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = sstotal;//送审金额合计
        row._cells[9].value = sdtotal;//审定金额合计
        row._cells[10].value = changeTotal;//增减金额合计
    }


    //7、【人材机2】人材机审核对比表
    async writeDataToUnitSheet7(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.totalNumber;
                    }
                }
                if (cell.col == 8) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.marketPrice;
                    }
                }
                if (cell.col == 9) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.total;
                    }
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 13) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.changePrice;
                    }
                }
                if (cell.col == 14) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
                if (cell.col == 15) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.changeExplain;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changePrice, changeTotal} = data["unitRcjHeji1"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[8].value = sstotal;//送审金额合计
        row._cells[11].value = sdtotal;//审定金额合计
        row._cells[12].value = changePrice;//增减单价合计
        row._cells[13].value = changeTotal;//增减金额合计
    }


    //8、【人材机3】人材机价差汇总对比表
    async writeDataToUnitSheet8(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialCode;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].specification;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 6) {
                    cell.value = list[countRow].dePrice;
                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.totalNumber;
                    }
                }
                if (cell.col == 8) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.marketPrice;
                    }
                }
                if (cell.col == 9) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.dwjc;
                    }
                }
                if (cell.col == 10) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.jchj;
                    }
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 13) {
                    cell.value = list[countRow].dwjc;
                }
                if (cell.col == 14) {
                    cell.value = list[countRow].jchj;
                }
                if (cell.col == 15) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj)) {
                        cell.value = list[countRow].ysshSysj.zjjc;
                    }
                }
                if (cell.col == 16) {
                    cell.value = "";
                }
            }
        }

        let {ssjchi, sdjchj, zjjc} = data["unitRcjHeji2"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[9].value = ssjchi;//送审金额合计
        row._cells[13].value = sdjchj;//审定金额合计
        row._cells[14].value = zjjc;//增减金额合计
    }


//9、【增值税1】材料、机械、设备增值税对比表
    async writeDataToUnitSheet9(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].materialCode;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].materialName;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.totalNumber)) {
                        cell.value = list[countRow].ysshSysj.totalNumber;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.taxRemoval)) {
                        cell.value = list[countRow].ysshSysj.taxRemoval;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.marketPrice)) {
                        cell.value = list[countRow].ysshSysj.marketPrice;
                    }
                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.jxTotal)) {
                        cell.value = list[countRow].ysshSysj.jxTotal;
                    }
                }
                if (cell.col == 8) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.xxTotal)) {
                        cell.value = list[countRow].ysshSysj.xxTotal;
                    }
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].totalNumber;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].taxRemoval;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].marketPrice;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].jxTotal;
                }
                if (cell.col == 13) {
                    cell.value = list[countRow].xxTotal;
                }
                if (cell.col == 14) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].jxszjje)) {
                        cell.value = list[countRow].ysshSysj.jxszjje;
                    }
                }
            }
        }

        let {ssjxTotal, ssxxTotal, sdjxTota, sdxxTotal, jxszjje} = data["unitcljxsbHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = ssjxTotal;//送审金额合计
        row._cells[7].value = ssxxTotal;//送审金额合计
        row._cells[11].value = sdjxTota;//审定金额合计
        row._cells[12].value = sdxxTotal;//增减金额合计
        row._cells[13].value = jxszjje;//增减金额合计
    }


    //10、【规费1】规费明细对比表
    async writeDataToUnitSheet10(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = j + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].costMajorName;
                }
                if (cell.col == 3) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.costFeeBase)) {
                        cell.value = list[countRow].ysshSysj.costFeeBase;
                    }
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.costFeeBase)) {
                        cell.value = list[countRow].ysshSysj.costFeeBase;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.gfeeRate)) {
                        cell.value = list[countRow].ysshSysj.gfeeRate;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.feeAmount)) {
                        cell.value = list[countRow].ysshSysj.feeAmount;
                    }
                }
                if (cell.col == 7) {
                    cell.value = list[countRow].costFeeBase;
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].costFeeBase;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].gfeeRate;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].feeAmount;
                }
                if (cell.col == 11) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changeTotal} = data["unitGuifeiHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[5].value = sstotal;//送审金额合计
        row._cells[9].value = sdtotal;//送审金额合计
        row._cells[10].value = changeTotal;//审定金额合计
    }


//11、【安全文施1】安全文明施工费明细对比表
    async writeDataToUnitSheet11(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 3;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = j + 1;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].costMajorName;
                }
                if (cell.col == 3) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.costFeeBase)) {
                        cell.value = list[countRow].ysshSysj.costFeeBase;
                    }
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.costFeeBase)) {
                        cell.value = list[countRow].ysshSysj.costFeeBase;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.basicRate)) {
                        cell.value = list[countRow].ysshSysj.basicRate;
                    }
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.addRate)) {
                        cell.value = list[countRow].ysshSysj.addRate;
                    }
                }
                if (cell.col == 7) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.feeAmount)) {
                        cell.value = list[countRow].ysshSysj.feeAmount;
                    }
                }
                if (cell.col == 8) {
                    cell.value = list[countRow].costFeeBase;
                }
                if (cell.col == 9) {
                    cell.value = list[countRow].costFeeBase;
                }
                if (cell.col == 10) {
                    cell.value = list[countRow].basicRate;
                }
                if (cell.col == 11) {
                    cell.value = list[countRow].addRate;
                }
                if (cell.col == 12) {
                    cell.value = list[countRow].feeAmount;
                }
                if (cell.col == 13) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.changeTotal)) {
                        cell.value = list[countRow].ysshSysj.changeTotal;
                    }
                }
            }
        }

        let {sstotal, sdtotal, changeTotal} = data["unitAnwenfeiHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[6].value = sstotal;//送审金额合计
        row._cells[11].value = sdtotal;//送审金额合计
        row._cells[12].value = changeTotal;//审定金额合计
    }


    //12、【工程量1】审定工程量计算书
    async writeDataToUnitSheet12(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 1;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].dispNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].bdName;
                    // if (!ObjectUtils.isUndefined(list[countRow].projectAttr) && !ObjectUtils.isNull(list[countRow].projectAttr)) {
                    //     cell.value = list[countRow].name + "\t\n" + list[countRow].projectAttr;
                    // } else {
                    //     cell.value = list[countRow].name;
                    // }
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].quantity;
                }
                if (cell.col == 4) {
                    cell.value = list[countRow].unit;
                }
                if (cell.col == 5) {
                    cell.value = list[countRow].quantityExpression;
                }
                if (cell.col == 6) {
                    if (!ObjectUtils.isUndefined(list[countRow].xj) && !ObjectUtils.isNull(list[countRow].xj)) {
                        cell.value = list[countRow].xj;
                    }
                }
                if (cell.col == 7) {
                    cell.value = "工程量";
                }
            }
        }
    }


    //11、【安全文施1】安全文明施工费明细对比表
    async writeDataToUnitSheet13(data, list, worksheet) {
        //填充项目名称
        let constructNameCell = ExcelUtil.findValueCell(worksheet, "工程名称：");
        let constructNameImport = data.filter(object => object.name == "工程名称")[0];
        let constructNameRow = worksheet.getRow(constructNameCell.cell._row._number);
        if (constructNameImport != null && constructNameImport.remark != null) {
            constructNameRow._cells[1].value = "工程名称：" + constructNameImport.remark;
        }
        ExcelUtil.traversalRowToCellBottom(constructNameRow);


        let amount = 0;
        let gfeeTotal = 0;
        let awenFeeTotal = 0;

        let headCount = 2;//表示表头行索引的最大值
        let countRow = 0;//索引  记录当前数据写入的游标
        for (let i = 0; i < list.length; i++) {
            let rowObject;
            headCount++;//记录当前数据插入行的索引
            rowObject = worksheet._rows[headCount];
            let copyDistance = 2;//定义复制距离  即当前数据插入行与复制行之间的距离
            let rowNext = worksheet._rows[headCount + copyDistance];
            if (rowNext == null) {
                //插入新行后最后一行的合并单元格丢失
                /****插入一条新行**************/
                let list = [];
                //为什么从前第二行复制样式 因为最后一行有粗线
                for (let m = 0; m < rowObject._cells.length; m++) {
                    list.push("");
                }
                rowNext = worksheet.insertRow(headCount + 2, list, 'o');
                let mergeMaps = new Map(Object.entries(worksheet._merges));
                for (let m = 0; m < rowNext._cells.length; m++) {
                    //获取模板行的合并单元格
                    let mergeName = ExcelUtil.getMergeName(worksheet._merges, rowObject._cells[m]);
                    if (mergeName != null) {
                        let {top, left, bottom, right} = mergeMaps.get(mergeName).model;
                        worksheet.unMergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                        worksheet.mergeCells([top + copyDistance, left, bottom + copyDistance, right]);
                    }
                    rowNext._cells[m].style = rowObject._cells[m].style;
                }
                /**end**插入一条新行**************/
            }
            countRow = i;

            for (let j = 0; j < rowObject._cells.length; j++) {
                let cell = rowObject._cells[j];
                if (cell.col == 1) {
                    cell.value = list[countRow].sortNo;
                }
                if (cell.col == 2) {
                    cell.value = list[countRow].name;
                }
                if (cell.col == 3) {
                    cell.value = list[countRow].total;
                }
                if (cell.col == 4) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.total)) {
                        cell.value = list[countRow].ysshSysj.total;
                    }
                }
                if (cell.col == 5) {
                    if (!ObjectUtils.isUndefined(list[countRow].ysshSysj) && !ObjectUtils.isUndefined(list[countRow].ysshSysj.zjje)) {
                        cell.value = list[countRow].ysshSysj.zjje;
                    }
                }
            }
        }

        let {sstotal, sdtotal, zjje} = data["unitZzsjxseHeji"];
        //定位到最后的合计行
        let heJiCell = ExcelUtil.findValueCell(worksheet, "合计");
        let row = worksheet.getRow(heJiCell.cell._row._number);
        row._cells[2].value = sstotal;//送审金额合计
        row._cells[3].value = sdtotal;//送审金额合计
        row._cells[4].value = zjje;//审定金额合计
    }

}

module.exports = {
    ShenHeWriteExcelBySheetUtil: new ShenHeWriteExcelBySheetUtil()
};
