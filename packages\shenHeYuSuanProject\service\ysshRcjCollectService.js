const YsshssConstant = require("../enum/YsshssConstant");
const {NumberUtil} = require("../../../electron/utils/NumberUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {Service} = require("../../../core");


class YsshRcjCollectService extends Service{

    constructor(ctx) {
        super(ctx);
    }



    rcjProcess = this.service.rcjProcess;


    /**
     * 单位工程 人材机汇总查询对比
     * @param args
     * @returns {Promise<void>}
     */
    async ysshRcjCollectComparison(args){
        if (args.type ==2 || ObjectUtils.isEmpty(args.type) ){
            //单位工程
           return  await this.unitRcjCollectComparison(args);
        }else {
            //工程项目
            return await this.constructRcjCollectComparison(args);
        }

    }


    /**
     * 单位工程人材机汇总对比
     * @param args
     * @returns {Promise<*[]>}
     */
    async unitRcjCollectComparison(args){

        //type   数据类型 1 工程项目层级  2 单位层级
        //kind   人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土
        let {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId,type, kind } = args;
        if (ObjectUtils.isEmpty(kind)){
            kind = 0;
        }

        if(ObjectUtils.isEmpty(type)){
            type =2;
        }


        //送审人材机汇总
        let ssRcj = ObjectUtils.isNotEmpty(ssUnitId)?this.rcjProcess.queryConstructRcjByDeIdNew(type,kind,ssConstructId, ssSingleId, ssUnitId):null;
        //审定人材机汇总
        let sdRcj = ObjectUtils.isNotEmpty(unitId)?this.rcjProcess.queryConstructRcjByDeIdNew(type,kind,constructId, singleId, unitId):null;

        //给前端返回的数据
        let resultList = [];

        //审定单位工程 匹配
        let ysshChangeMatchingRecord =null;

        ysshChangeMatchingRecord = PricingFileFindUtils.getUnit(constructId, singleId, unitId).ysshChangeMatchingRecord;
        let set1 = new Set();
        if (!ObjectUtils.isEmpty(ysshChangeMatchingRecord)){
            const materialCodes = Object.values(ysshChangeMatchingRecord).map(item => item.materialCode);
            set1 = new Set(materialCodes);
        }

        if (ObjectUtils.isEmpty(ssRcj) && ObjectUtils.isEmpty(sdRcj)){
            return resultList;
        }

        //全为审删
        if (ObjectUtils.isEmpty(sdRcj)){
            for (let ssRcjKey of ssRcj) {
                let promise =await this.rcjSs(ssRcjKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //全为审增
        if (ObjectUtils.isEmpty(ssRcj)){
            for (let sdRcjKey of sdRcj) {
                let promise =await this.rcjSz(sdRcjKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //审改 或不发生变化
        if (!ObjectUtils.isEmpty(ssRcj) && !ObjectUtils.isEmpty(sdRcj)){
            //审定关联 送审 的清单id set
            let set = new Set();
            //手动匹配过后的送审 材料id
            let ssMaterialCodeSet = new Set();
            for (let sdRcjKey of sdRcj) {
                //送审对象
                let find;
                //优先判断有没有 手动匹配
                if (!ObjectUtils.isEmpty(ysshChangeMatchingRecord) && !ObjectUtils.isEmpty(ysshChangeMatchingRecord[sdRcjKey.materialCode])
                    && !ObjectUtils.isEmpty(!ObjectUtils.isEmpty(ysshChangeMatchingRecord[sdRcjKey.materialCode]).materialCode)){

                    find = ssRcj.find(i=>!ObjectUtils.isEmpty(i.materialCode) && ysshChangeMatchingRecord[sdRcjKey.materialCode].materialCode == i.materialCode);
                    ssMaterialCodeSet.add(ysshChangeMatchingRecord[sdRcjKey.materialCode].materialCode);
                }else {
                    //没有优先匹配走 自动匹配


                    find = ssRcj.find(i=>!ObjectUtils.isEmpty(i.materialCode) && !ssMaterialCodeSet.has(i.materialCode)&& i.materialCode == sdRcjKey.materialCode);
                    if (!ObjectUtils.isEmpty(set1) && !ObjectUtils.isEmpty(find) &&!ObjectUtils.isEmpty(find.materialCode) && set1.has(find.materialCode)){
                        find =null;
                    }
                }

                let promise ={};
                if (!ObjectUtils.isEmpty(find)){
                    //两边都有
                    promise =await this.rcjSg(sdRcjKey,find);
                    set.add(find.sequenceNbr);
                }else {
                    //审增项
                    promise =await this.rcjSz(sdRcjKey);
                }
                resultList.push(promise);
            }

            //送审 审删逻辑 处理，此处逻辑迁移自fbfx
            let ssdeList = ssRcj.filter(i=>{
                return !set.has(i.sequenceNbr);
            });

            for (let ssdeListElement of ssdeList) {
                let ssqdDe = await this.rcjSs(ssdeListElement);
                resultList.push(ssqdDe);
            }
            return resultList;
        }
    }

    /**
     * 工程项目人材机汇总对比
     * @param args
     * @returns {Promise<void>}
     */
    async constructRcjCollectComparison(args){
        //type   数据类型 1 工程项目层级  2 单位层级
        //kind   人材机类型 0所有人才机  1：人工；2：材料；3：机械；4：设备；5：主材； 6 预拌混凝土
        const {ssConstructId, ssSingleId, ssUnitId, constructId, singleId, unitId,type, kind } = args;

        //送审人材机汇总
        let ssRcj = ObjectUtils.isNotEmpty(ssConstructId)?this.rcjProcess.queryConstructRcjByDeIdNew(type,kind,ssConstructId, ssSingleId, ssUnitId):null;
        //审定人材机汇总
        let sdRcj = ObjectUtils.isNotEmpty(constructId)?this.rcjProcess.queryConstructRcjByDeIdNew(type,kind,constructId, singleId, unitId):null;

        //给前端返回的数据
        let resultList = [];

        if (ObjectUtils.isEmpty(ssRcj) && ObjectUtils.isEmpty(sdRcj)){
            return resultList;
        }

        //全为审删
        if (ObjectUtils.isEmpty(sdRcj)){
            for (let ssRcjKey of ssRcj) {
                let promise =await this.rcjSs(ssRcjKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //全为审增
        if (ObjectUtils.isEmpty(ssRcj)){
            for (let sdRcjKey of sdRcj) {
                let promise =await this.rcjSz(sdRcjKey);
                resultList.push(promise);
            }
            return resultList;
        }

        //审改 或不发生变化
        if (!ObjectUtils.isEmpty(ssRcj) && !ObjectUtils.isEmpty(sdRcj)){
            //审定关联 送审 的清单id set
            let set = new Set();
            //手动匹配过后的送审 材料id
            let ssMaterialCodeSet = new Set();
            for (let sdRcjKey of sdRcj) {
                //送审对象
                let find;
                //没有优先匹配走 自动匹配
                find = ssRcj.find(i=>!ObjectUtils.isEmpty(i.materialCode) && !ssMaterialCodeSet.has(i.materialCode)&& i.materialCode == sdRcjKey.materialCode);
                let promise ={};
                if (!ObjectUtils.isEmpty(find)){
                    //两边都有
                    promise =await this.rcjSg(sdRcjKey,find);
                    set.add(find.sequenceNbr);
                    ssMaterialCodeSet.add(ssRcj.materialCode);
                }else {
                    //审增项
                    promise =await this.rcjSz(sdRcjKey);
                }
                resultList.push(promise);
            }

            //送审 审删逻辑 处理，此处逻辑迁移自fbfx
            let ssdeList = ssRcj.filter(i=>{
                return !set.has(i.sequenceNbr);
            });

            for (let ssdeListElement of ssdeList) {
                let ssqdDe = await this.rcjSs(ssdeListElement);
                resultList.push(ssqdDe);
            }
            return resultList;
        }
    }

    /**
     * 审 删 人材机 (送审有 审定没有)
     * @param rcj
     * @returns {Promise<void>}
     */
    async rcjSs(rcj){
        let sssj = {};
        //送审数据 编码 名称 类别 规格型号,单位,定额单价,市场价,初始含量,含量,合计数量,合计金额,价格来源,是否汇总,除税系数,进项税额
        let {materialCode,materialName,type,specification,unit,dePrice,marketPrice,initResQty,resQty,totalNumber,total,
            sourcePrice,markSum,taxRemoval,jxTotal,sequenceNbr,parentId,ifDonorMaterial,donorMaterialNumber,kind}=rcj;
        sssj.materialCode = materialCode;
        sssj.materialName = materialName;
        sssj.type = type;
        sssj.specification = specification;
        sssj.unit = unit;
        sssj.dePrice = dePrice;
        sssj.marketPrice = marketPrice;
        sssj.initResQty = initResQty;
        sssj.resQty = resQty;
        sssj.totalNumber = totalNumber;
        sssj.total = total;
        sssj.sourcePrice = sourcePrice;
        sssj.markSum = markSum;
        sssj.taxRemoval = taxRemoval;
        sssj.jxTotal = jxTotal;
        sssj.ifDonorMaterial = ifDonorMaterial;
        sssj.donorMaterialNumber = donorMaterialNumber;
        sssj.sequenceNbr = sequenceNbr;

        //审核状态
        sssj[YsshssConstant.change] = YsshssConstant.delete;

        //增减金额
        sssj[YsshssConstant.changeTotal]= "-"+total;

        //增减单价
        sssj[YsshssConstant.changePrice]= "-"+marketPrice;

        //增减比例
        sssj[YsshssConstant.changeRatio] = -100;

        //增减说明
        sssj[YsshssConstant.changeExplain]= "[减项]";

        let assign = {};

        assign.totalNumber =0;
        assign.jxTotal =0;

        assign[YsshssConstant.ysshSysj] = sssj;
        assign['parentId'] = parentId;
        assign['sequenceNbr'] = sequenceNbr;

        assign.materialCode =materialCode;
        assign.materialName =materialName;
        assign.type =type;
        assign.unit =unit;
        assign.specification =specification;
        assign.dePrice =dePrice;
        assign.kind =kind;
        assign.markSum  =rcj.markSum;
        assign.levelMark =rcj.levelMark;

        return assign;

    }

    /**
     * 审 增人材机 (送审没有 审定有)
     * @param rcj
     * @returns {Promise<void>}
     */
    async rcjSz(rcj){
        let sssj = {};

        //审核状态
        sssj[YsshssConstant.change] = YsshssConstant.insert;

        //增减金额
        sssj[YsshssConstant.changeTotal]= rcj.total;

        //增减单价
        sssj[YsshssConstant.changePrice]= rcj.marketPrice;

        //增减说明
        sssj[YsshssConstant.changeExplain]= "[增项]";

        //增减比例
        sssj[YsshssConstant.changeRatio] = 100;

        sssj.totalNumber = 0;
        sssj.jxTotal = 0;

        if (ObjectUtils.isEmpty(sssj.donorMaterialNumber)){
            sssj.donorMaterialNumber = 0;
        }

        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]:sssj},rcj); //新对象

        return assign;
    }

    /**
     * 审 改 或不变 人材机 (送审有 审定有)
     * @param sdRcj 审定对象
     * @param ssRcj 送审对象
     * @returns {Promise<any>}
     */
    async rcjSg(sdRcj,ssRcj){
        let sssj = {};
        //送审数据 编码 名称 类别 规格型号,单位,定额单价,市场价,初始含量,含量,合计数量,合计金额,价格来源,是否汇总,除税系数,进项税额
        let {materialCode,materialName,type,specification,unit,dePrice,marketPrice,initResQty,resQty,totalNumber,total,
            sourcePrice,markSum,taxRemoval,jxTotal,sequenceNbr,parentId,ifDonorMaterial,donorMaterialNumber}=ssRcj;
        sssj.materialCode = materialCode;
        sssj.materialName = materialName;
        sssj.type = type;
        sssj.specification = specification;
        sssj.unit = unit;
        sssj.dePrice = dePrice;
        sssj.marketPrice = marketPrice;
        sssj.initResQty = initResQty;
        sssj.resQty = resQty;
        sssj.totalNumber = totalNumber;
        sssj.total = total;
        sssj.sourcePrice = sourcePrice;
        sssj.markSum = markSum;
        sssj.taxRemoval = taxRemoval;
        sssj.jxTotal = jxTotal;
        sssj.ifDonorMaterial = ifDonorMaterial;
        sssj.donorMaterialNumber = donorMaterialNumber;
        sssj.sequenceNbr = sequenceNbr;

        if (ObjectUtils.isEmpty(sssj.donorMaterialNumber)){
            sssj.donorMaterialNumber = 0;
        }

        if (ObjectUtils.isEmpty(sdRcj.donorMaterialNumber)){
            sdRcj.donorMaterialNumber = 0;
        }

        //合价差
        let subtract = NumberUtil.subtract(sdRcj.total,total);

        //单价差
        let subtract1 = NumberUtil.subtract(sdRcj.marketPrice,marketPrice);

        //量差
        let subtract2 = NumberUtil.subtract(sdRcj.totalNumber,totalNumber);

        //增减金额
        sssj[YsshssConstant.changeTotal]= subtract;

        //增减状态
        sssj[YsshssConstant.change] = YsshssConstant.noChange;

        //增减单价
        sssj[YsshssConstant.changePrice]= subtract1;

        //增减比例
        sssj[YsshssConstant.changeRatio] = NumberUtil.numberScale2(NumberUtil.multiply(NumberUtil.divide(subtract,ssRcj.total),100));

        let changeExplain = "";

        /*if (subtract2 !=0){
            changeExplain = changeExplain + "[调量]";
            //sssj[YsshssConstant.change] = YsshssConstant.update;
        }*/
        if (subtract!=0){

            //sssj[YsshssConstant.change] = YsshssConstant.update;
        }


        if (sdRcj.marketPrice != marketPrice || sdRcj.taxRemoval != ssRcj.taxRemoval ){
            sssj[YsshssConstant.change] = YsshssConstant.update;
            changeExplain = changeExplain + "[调价]"
        }

        //增减说明
        sssj[YsshssConstant.changeExplain]= changeExplain;


        //给前端返回的最终对象
        let assign = Object.assign({[YsshssConstant.ysshSysj]:sssj},sdRcj); //新对象

        return assign;
    }


    /**
     * 单位工程汇总人材机关联关系切换
     * @param args
     * @returns {Promise<void>}
     */
    async unitRcjChangeGL(args){

        //审定 材料编码 materialCode
        //送审 材料编码 ssMaterialCode
        const {constructId, singleId, unitId,materialCode,ssMaterialCode} = args;
        if(ObjectUtils.isEmpty(materialCode) || ObjectUtils.isEmpty(ssMaterialCode)){
            return null;
        }
        let unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);

        if (ObjectUtils.isEmpty(unit.ysshChangeMatchingRecord)){
            unit.ysshChangeMatchingRecord ={};
        }

        //删除记录过的 送审 匹配
        let targetKey = null;
        for (const key in unit.ysshChangeMatchingRecord) {
            if (unit.ysshChangeMatchingRecord[key].materialCode === ssMaterialCode) {
                targetKey = key;
                break; // 找到目标对象后结束循环
            }
        }

        if (targetKey !== null) {
            delete unit.ysshChangeMatchingRecord[targetKey];
        }


        let rcjMaterialCodeChangeExplain ={};
        rcjMaterialCodeChangeExplain.materialCode = ssMaterialCode;
        unit.ysshChangeMatchingRecord[materialCode] =rcjMaterialCodeChangeExplain;


    }
}


YsshRcjCollectService.toString = () => '[class YsshRcjCollectService]';
module.exports = YsshRcjCollectService;