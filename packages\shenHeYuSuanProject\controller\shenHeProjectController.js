const {Controller} = require("../../../core");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");


class ShenHeProjectController extends Controller{

  /**
     * 构造函数
     * @param ctx
     */
  constructor(ctx) {
    super(ctx);
  }


  /**
     * 立即新建审核项目
     */
  async shNewProject (arg) {
      return await this.service.shenHeYuSuanProject.shenHeProjectService.shNewProject(arg);
  }

  /**
   * 查询对比匹配树结构
   */
  async shQueryDetail (arg) {
    return await this.service.shenHeYuSuanProject.shenHeProjectService.shQueryDetail(arg);
  }


  /**
   * 菜单栏功能---另存为
   * @param arg
   * @return {*}
   */
  async fileSaveAs(arg) {
    return await this.service.shenHeYuSuanProject.shenHeProjectService.fileSaveAs(arg, "");

  }


  /**
   * 打开项目
   */
  async openProject (arg) {
    return await this.service.shenHeYuSuanProject.shenHeProjectService.openProject(arg);
  }



  /**
   * 确定对比数据
   */
  async shSaveDetail (arg) {
    return await this.service.shenHeYuSuanProject.shenHeProjectService.shSaveDetail(arg);
  }



  /**
   * 双击项目绑定关系
   */
  async bindingProRelation (arg) {
    return await this.service.shenHeYuSuanProject.shenHeProjectService.bindingProRelation(arg);
  }



  /**
   * 生成项目层级树结构-铺平
   */
  async generateLevelTreeNodeStructure (arg) {
    return ResponseData.success(await this.service.shenHeYuSuanProject.shenHeProjectService.generateLevelTreeNodeStructure(arg));
  }

  /**
   * 取消数据
   * constructId 为备份的id
   * @param {*} arg
   */
  async shRecoveryData (arg) {
    return await this.service.shenHeYuSuanProject.shenHeProjectService.shRecoveryData(arg);
  }


  /**
   * 转为预算
   */
  async shProjectToBudget (arg) {
    return  await this.service.shenHeYuSuanProject.shenHeProjectService.shProjectToBudget(arg);
  }

  /**
   * 预算文件存储位置
   */
  async shYsfSaveLocation (arg) {
    const result = await this.service.shenHeYuSuanProject.shenHeProjectService.shYsfSaveLocation(arg);
    return ResponseData.success(result);
  }



  /**
   * 查询送审数据
   * {
   *  type: 1 分布分项,2 措施,3  其他 31 暂列金 32 专业暂估价 33 总承包费用 34 计日工 ,4 人材机 5 费用汇总,
   *  ysshConstructId:  审定项目id
   *  ysshSpId：  审定单项id
   *  ysshUnitId：审定单位id
      }
   */
  async shQuerySSDetail (arg) {
    let {type, ysshConstructId, ysshSpId, ysshUnitId} = arg;
    if(ObjectUtils.isEmpty(ysshUnitId)){
      return ResponseData.fail('单位id和单项id不能同时为空');
    }
    // 1 分布分项,2 措施,3  其他 31 暂列金 32 专业暂估价 33 总承包费用 34 计日工 ,4 人材机 5 费用汇总,(31 暂列金 32 专业暂估价.)
    let res = null;
    if(type === 1){
      res = PricingFileFindUtils.getFbFx(ysshConstructId, ysshSpId, ysshUnitId).getAllNodes();
    }else if(type === 2){
      res = PricingFileFindUtils.getCSXM(ysshConstructId, ysshSpId, ysshUnitId).getAllNodes();
    }else if(type === 3){
      res = PricingFileFindUtils.getOtherProject(ysshConstructId, ysshSpId, ysshUnitId);
    }else if(type === 31){
      res = PricingFileFindUtils.getOtherProjectProvisional(ysshConstructId, ysshSpId, ysshUnitId);
    }else if(type === 32){
      res = PricingFileFindUtils.getOtherProjectZygcZgjList(ysshConstructId, ysshSpId, ysshUnitId);
    }else if(type === 33){
      res = PricingFileFindUtils.getOtherProjectServiceCost(ysshConstructId, ysshSpId, ysshUnitId);
    }else if(type === 34){
      res = PricingFileFindUtils.getOtherProjectDayWork(ysshConstructId, ysshSpId, ysshUnitId);
    }else if(type === 4){
      res = this.service.rcjProcess.queryConstructRcjByDeIdNew(2,0,ysshConstructId, ysshSpId, ysshUnitId);
    }else if(type === 5){
      res = PricingFileFindUtils.getUnitCostSummary(ysshConstructId, ysshSpId, ysshUnitId);
    }else{
      return ResponseData.fail('类型参数错误');
    }

    return ResponseData.success(res);
  }

  /**
   * 获取单位工程信息
   * @param args
   * @returns {Promise<void>}
   */
  async getUnitProject(args) {
    const result = await this.service.shenHeYuSuanProject.shenHeProjectService.getUnitProject(args);
    return result;
  }

}

ShenHeProjectController.toString = () => '[class ShenHeProjectController]';
module.exports = ShenHeProjectController;