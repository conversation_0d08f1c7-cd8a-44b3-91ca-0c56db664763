<template>
  <div class="table-content">
    <vxe-table
      align="center"
      height="98%"
      :style="{ width: columnWidth(585) + 'px', maxWidth: '100%' }"
      ref="basicInfoTable"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
        keyField: cacheAll.rowKeyFiled,
      }"
      :data="tableData"
      :tree-config="{
        expandAll: true,
        children: cacheAll.treeChildrenKey,
        reserve: true,
      }"
      :tooltip-config="tooltipConfig"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        showIcon: false,
        showStatus: false,
      }"
      @keydown="keyDownHandler"
      @cell-click="
        cellData => {
          useCellClickEvent(cellData);
        }
      "
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      class="table-edit-common table-no-outer-border"
      :cell-class-name="
        ({ $columnIndex, row, column }) => {
          const selectName = selectedClassName({ $columnIndex, row, column });
          if (
            column.field === 'name' &&
            ['基本信息', '投标信息', '招标信息'].includes(row.name) &&
            row.addFlag === 0
          ) {
            return 'title-bold ' + selectName;
          }

          if (column.field === 'name' && row.type === 'title') {
            return 'title-bold ' + selectName;
          }
          if (column.field === 'name' && colorFieldList.includes(row.name)) {
            return 'color-red ' + selectName;
          }
          return selectName;
        }
      "
      :row-class-name="
        ({ row }) => {
          if (row.lockFlag == 1) {
            return 'row-lock-color';
          }
        }
      ">
      <vxe-column field="dispNo" :width="columnWidth(50)" title="序号"></vxe-column>
      <vxe-column
        field="name"
        align="left"
        title="名称"
        :width="columnWidth(235)"
        :edit-render="{ autofocus: '.vxe-input--inner' }">
        <template #default="{ row }">
          <!-- <a-popover
            placement="topRight"
            trigger="hover"
          >
            <template #content>
              <span>注意：建筑面积需要与取费表中“建筑面积”设置的选项保持一致，否则可能影响评标结果。
              </span>
            </template>
            <icon-font
              class="icon"
              type="icon-tishineirong"
              style="font-size: 12px;margin-right: 5px;"
              v-if="avergeList.includes(row.name.trim())"
            />
          </a-popover> -->
          <span>{{ row.name }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-if="row.addFlag && !row.lockFlag"
            placeholder="请输入名称"
            v-model="row.name"
            type="text"
            name="name"
            @blur="inputFinish(row, $event, 'name', '名称')"></vxe-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        align="left"
        title="备注"
        :width="columnWidth(300)"
        :edit-render="{ autofocus: '.vxe-input--inner' }">
        <template #edit="{ row }">
          <vxeTableEditSelect
            :filedValue="row.remark"
            :list="row.jsonStr"
            :isNotLimit="true"
            v-if="
              ifShowSelect(row) &&
              ![...avergeList, ...dateSelect].includes(row.name.trim()) &&
              !inputDisabled(row)
            "
            @update:filedValue="
              newValue => {
                saveCustomInput(newValue, row, 'remark', $rowIndex, '备注');
              }
            "></vxeTableEditSelect>
          <vxe-input
            v-else-if="ifShowSelect(row) && dateSelect.includes(row.name)"
            v-model="row.remark"
            @focus="onDateFocus(row)"
            @change="timeSelect(row, { $event })"
            placeholder="日期选择"
            type="date"></vxe-input>
          <vxe-input
            v-else-if="ifShowSelect(row) && avergeList.includes(row.name.trim())"
            v-model="row.remark"
            :disabled="() => inputDisabled(row)"
            @blur="averageBlur(row, $event, 'remark')"
            @keyup="row.remark = limitNum(row.remark)"></vxe-input>
          <span v-else>{{ row.remark }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { onMounted, onUpdated, onActivated, ref, watch, inject, getCurrentInstance } from 'vue';
import xeUtils from 'xe-utils';
import { message, Modal } from 'ant-design-vue';
import { projectDetailStore } from '@/store/projectDetail';
import csProject from '@/api/csProject';
import operateList from '../operate';
import { insetBus } from '@/hooks/insetBus';
import { comBasicInfoFun } from './comBasiciInfo';
import { columnWidth } from '@/hooks/useSystemConfig';
import { useDecimalPoint } from '@/hooks/useDecimalPoint';
import dayjs from 'dayjs';
const { qdDeAmountFormat } = useDecimalPoint();

import { useCellClick } from '@/hooks/useCellClick';
const { useCellClickEvent, cellBeforeEditMethod, selectedClassName } = useCellClick();
const props = defineProps({
  activeKey: {
    type: Number,
    default: 1,
  },
});
const colorFieldList = [
  //字体标红字段列表
  '建筑面积',
  '工程规模',
  '编制单位法定代表人',
  '招标人(发包人)',
  '招标人(发包人)法人或其授权人',
  '工程造价咨询人',
  '工程造价咨询人法人或其授权人',
  '编制人',
  '编制时间',
  '核对人(复核人)',
  '核对(复核)时间',
  '投标人(承包人)',
  '投标人(承包人)法人或其授权人',
];
const dateSelect = ['开工日期', '竣工日期', '编制时间', '核对(复核)时间']; //时间选择器设置字段列表
const avergeList = ['建筑面积', '工程规模']; //建筑面积列表
const notEditList = ['基本信息', '工程所在地', '招标信息', '投标信息']; //不可编辑字段
const cxt = getCurrentInstance();
const bus = cxt.appContext.config.globalProperties.$bus;
let lockData = operateList.value.find(item => item.name === 'lock');
console.log('lockData', lockData);
let basicInfoTable = ref();
let loading = ref(false);
let constructLevel = ref(true);
const activeKey = ref(1);
const inputMaxLength = ref(50);
const projectStore = projectDetailStore();

let tableData = ref([]);

const createRequestParams = () => {};
const { getPageData, delOperateFun, saveAndUpdateOperate, lockOperate, dafaultParams } =
  comBasicInfoFun({ activeKey, pageType: 'jbgcxx' });
const flashFun = () => {
  console.log('*****************基本信息');
  if (
    projectStore.asideMenuCurrentInfo?.key === '11' &&
    props.activeKey === 1 &&
    projectStore.currentTreeInfo?.levelType !== 2 &&
    ['项目概况', '工程概况'].includes(projectStore.tabSelectName)
  ) {
    getBasicInfo();
  }
};
let oldRemark = ref('');
const ifShowSelect = row => {
  return (
    (projectStore.currentTreeInfo?.levelType === 1 &&
      !row.lockFlag &&
      !notEditList.includes(row.name)) ||
    row.addFlag ||
    (projectStore.currentTreeInfo?.levelType === 3 && !['工程专业'].includes(row.name))
  );
};
const limitNum = value => {
  if (typeof value !== 'string') return value;
  return value.replace(/[^(-?\d+)\.?(\d*)$]/g, '');
};
const averageBlur = (row, e, attr) => {
  let value = xeUtils.trim(e.value);
  if (row[attr] !== '') {
    row[attr] = qdDeAmountFormat(row[attr]);
  }
  oldRemark.value = row.remark;
  console.log('🚀 ~ saveCustomInput ~ newValue:', value, row.remark);
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true, value, oldRemark.value, attr, row.sequenceNbr);
};
let isRefresh = ref(false);
const saveCustomInput = (newValue, row, name, index, title) => {
  if (row.name === '项目编号' && !newValue) {
    message.warning('项目编码不可为空');
    return;
  }
  oldRemark.value = row.remark;
  console.log('saveOrUpdateBasicInfo', row.remark, oldRemark.value);
  const list = [null, undefined, ''];
  if ((list.includes(row[name]) && list.includes(newValue)) || row[name] === newValue) return;
  row[name] = newValue;
  if (row.name === '工程名称' || row.name === '单位工程名称') {
    isRefresh.value = true;
  } else {
    isRefresh.value = false;
  }
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, false, newValue,oldRemark.value, title, row.sequenceNbr);
};
const inputDisabled = row => {
  console.log('🚀 ~ inputDisabled ~ row:', projectStore.constructConfigInfo);
  if (
    row.name === '项目编号' &&
    row.remark &&
    projectStore.constructConfigInfo?.constructCodeFlag === 0
  ) {
    return true;
  }
  return false;
};
watch(() => projectStore.asideMenuCurrentInfo, flashFun);

watch(() => projectStore.currentTreeInfo, flashFun);
watch(() => projectStore.isRefreshBaseInfo, flashFun);
// 保存日期字段的旧值
const onDateFocus = (row) => {
  oldRemark.value = row.remark;
  console.log('onDateFocus - saved oldValue:', oldRemark.value);
};

const timeSelect = (row, { $event }) => {
  // debugger;
  console.log('timeSelect', $event, 'row:', row);
  const newValue = $event.value;
  const oldValue = oldRemark.value; // 使用focus时保存的旧值
  console.log('timeSelect values:', { newValue, oldValue, remarkFromRow: row.remark });
  console.log('saveOrUpdateBasicInfo', newValue, oldValue, 'sequenceNbr:', row.sequenceNbr);
  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true, newValue, oldValue, '备注', row.sequenceNbr);
};
const getBasicInfo = async () => {
  let levelType = projectStore.currentTreeInfo?.levelType;
  if (!levelType) {
    return;
  }
  let res = await getPageData();
  if (res.status === 200) {
    loading.value = false;
    if (projectStore.type === 'jieSuan' && projectStore.currentTreeInfo.levelType === 1) {
      res.result = res.result.filter(i => i.groupCode === 1);
    }
    //设置默认时间日期
    // res.result.map(a => {
    //   a.childrenList &&
    //     a.childrenList.map(b => {
    //       if (dateSelect.includes(b.name)) {
    //         b.remark = dayjs(b.remark, 'YYYY-MM-DD', true).isValid()
    //           ? dayjs().format('YYYY-MM-DD')
    //           : b.remark;
    //       }
    //     });
    // });
    res.result.map(a => {
      if (a?.groupCode === 3) {
        a.childrenList &&
          a.childrenList.map(b => {
            if (b.name === '担保类型' && b.jsonStr?.length === 0) {
              b.jsonStr = '支票,电汇,汇票,现金,其他';
            }
          });
      }
    });
    tableData.value = res.result;
    cacheAll.currentRecord =
      cacheAll.newAddRowSeq === null
        ? tableData.value
          ? tableData.value[0]
          : null
        : findLastAddRow(tableData.value);
    resetCurrentRow();
    cacheAll.newAddRowSeq = null;
    console.log('********getBasicInfo', res.result);
  }
};

const findLastAddRow = tree => {
  console.log('cacheAll.newAddRowSeq', cacheAll.newAddRowSeq, tree);
  let targetBeforeIdx = tree.findIndex(i => i.sequenceNbr === cacheAll.newAddRowSeq);
  let target = tree[targetBeforeIdx];
  return target;
};

const deleteRowForBasicInfo = async sequenceNbr => {
  let res = await delOperateFun(sequenceNbr);
  if (res.status === 200) {
    message.success('删除成功');
    getBasicInfo();
  }
};

const saveOrUpdateBasicInfo = async (param, isUpdate = false, value, oldValue, title, sequenceNbr) => {
  let res = await saveAndUpdateOperate(param.data, value, oldValue, title, sequenceNbr);
  console.log('saveOrUpdateBasicInfo', res, param.data, value, oldValue, title, sequenceNbr);
  res.status === 200 ? message.success('操作成功') : '';
  if (res.status === 200 && !isUpdate) {
    getBasicInfo();
  }
  if (res.status === 200 && isRefresh.value) {
    projectStore.SET_IS_REFRESH_PROJECT_TREE(true);
  }
};
let lockStatus = ref(0);
watch(
  () => lockStatus.value,
  val => {
    lockData.label = val ? '解锁' : '锁定';
    console.log(lockData);
  }
);
const cacheAll = {
  newAddRowSeq: null,
  currentRecord: null,
  treeChildrenKey: 'childrenList',
  rowKeyFiled: 'sequenceNbr',
  newRecord: function (parentId, groupCode) {
    return {
      sequenceNbr: Date.now(),
      name: '',
      remark: null,
      addFlag: 1,
      lockFlag: 0,
      parentId: parentId,
      groupCode: groupCode,
    };
  },
  copyRow: function (row) {
    let newRow = {};
    Object.assign(newRow, row);
    newRow.sequenceNbr = Date.now();
    newRow.childrenList = null;
    newRow.addFlag = 1;
    newRow.lockFlag = 0;
    newRow.recDate = null;

    return newRow;
  },
};

const inputFinish = (row, e, attr, title) => {
  let value = xeUtils.trim(e.value);
  const oldValue = oldRemark.value; // 使用之前保存的旧值
  if (value.length > 50) {
    value = value.slice(0, 50);
    row[attr] = value;
    message.warning('输入过长，请输入50个字符范围内');
  }
  row[attr] = value;

  saveOrUpdateBasicInfo({ data: xeUtils.clone(tableData.value, true) }, true, value, oldValue, title, row.sequenceNbr);

  // emits('saveOrUpdateBasicInfo', { data: tableData.value });
};

const insertInData = (tree, selectNode, newNode) => {
  const key = cacheAll.rowKeyFiled;

  let levelType = projectStore.currentTreeInfo?.levelType;
  if (levelType === 3) {
    const index = tree.findIndex(item => item[key] === selectNode[key]) + 1;
    tree.splice(index, 0, newNode);
    return;
  }

  for (let pos = 0; pos < tree.length; pos++) {
    let node = tree[pos];
    if (node[key] === selectNode[key]) {
      node.childrenList.splice(0, 0, newNode);
      return;
    } else {
      let index = node.childrenList.findIndex(item => item[key] === selectNode[key]);
      if (index !== -1) {
        node.childrenList.splice(index + 1, 0, newNode);
        return;
      }
    }
  }
};

const resetCurrentRow = () => {
  cacheAll.currentRecord = cacheAll.currentRecord || (tableData.value ? tableData.value[0] : null);
  basicInfoTable.value?.setCurrentRow(cacheAll.currentRecord);
};

function modalTip(content) {
  return new Promise((resolve, reject) => {
    Modal.confirm({
      title: '',
      content: content,
      onOk: () => {
        resolve(true);
      },
      onCancel() {
        resolve(false);
      },
    });
  });
}

async function insertHandle(posRow, newRow) {
  const selectRecord = posRow || basicInfoTable.value?.getCurrentRecord();
  console.log(basicInfoTable.value?.getCurrentRecord());
  if (!selectRecord) {
    await modalTip('请选中要插入的位置');
    return;
  }

  let allData = tableData.value;
  let newRecord =
    newRow ||
    cacheAll.newRecord(
      projectStore.currentTreeInfo?.levelType === 3
        ? null
        : selectRecord.parentId || selectRecord.sequenceNbr,
      projectStore.currentTreeInfo?.levelType === 3 ? allData.length + 1 : selectRecord.groupCode
    );

  cacheAll.newAddRowSeq = newRecord.sequenceNbr;
  console.log('=========11====: ', cacheAll.newAddRowSeq);
  insertInData(allData, selectRecord, newRecord);
  // cacheAll.currentRecord = newRecord
  // basicInfoTable.value.loadData(allData)
  // basicInfoTable.value.setEditRow(cacheAll.currentRecord)
  // resetCurrentRow()
  saveOrUpdateBasicInfo({ data: xeUtils.clone(allData, true) }, false, '', '', '新增', newRecord.sequenceNbr);
  // emits('saveOrUpdateBasicInfo', { data: allData });
}

const resetLockHandle = async () => {
  let res = await lockOperate(lockStatus.value);
  if (res.status === 200) {
    message.success('操作成功');
    getBasicInfo();
    lockStatus.value = !lockStatus.value;
  }
};

async function deleteHandle(row) {
  const selectRecord = row || basicInfoTable.value?.getCurrentRecord();
  if (!selectRecord?.addFlag) {
    //await modalTip('默认信息不能删除');
    message.warning('默认信息不能删除');
    return;
  }

  if (selectRecord.lockFlag) {
    //await modalTip('锁定行不能被删除');
    message.warning('锁定行不能被删除');
    return;
  }

  const status = await modalTip('确定要删除选中行？');
  if (!status) {
    return;
  }

  deleteRowForBasicInfo(selectRecord[cacheAll.rowKeyFiled]);
}

const menuConfig = ref({
  body: {
    options: [
      [
        { code: 'insertRow', name: '插入行', disabled: false },
        { code: 'copyRow', name: '复制行', disabled: false },
        { code: 'deleteRow', name: '删除行', disabled: false },
      ],
    ],
  },
  visibleMethod({ row, type, options }) {
    const $table = basicInfoTable.value;
    $table.setCurrentRow(row);
    if ($table) {
      if (type === 'body') {
        options.forEach(list => {
          list.forEach(item => {
            if (item.code === 'deleteRow') {
              if (row && (!row.addFlag || row.lockFlag)) {
                item.disabled = true;
              } else {
                item.disabled = false;
              }
            }
          });
        });
      }
    }
    return true;
  },
});

const contextMenuClickEvent = ({ menu, row, column }) => {
  const $table = basicInfoTable.value;
  if ($table) {
    switch (menu.code) {
      case 'insertRow':
        insertHandle(row);
        break;
      case 'copyRow':
        let newRow = cacheAll.copyRow(row);
        insertHandle(row, newRow);
        break;
      case 'deleteRow':
        deleteHandle(row);
        break;
    }
  }
};
const keyDownHandler = event => {
  let code = event.$event.code;
  if (code == 'Delete') {
    deleteHandle();
  }
};
onUpdated(() => {
  //设置展开所有节点
  basicInfoTable.value.setAllTreeExpand(true);

  //设置选中行为第一行
  resetCurrentRow();

  constructLevel.value = projectStore.currentTreeInfo?.levelType === 1;

  //重置锁定状态
  lockStatus.value = tableData.value[1]?.lockFlag;
});
onActivated(() => {
  insetBus(bus, projectStore.componentId, 'basicInfo', async data => {
    if (data.name === 'insert') insertHandle(null);
    if (data.name === 'lock') resetLockHandle(null);
    if (data.name === 'delete') deleteHandle(null);
  });
});
const tooltipConfig = {
  // showAll: true,
  // enterable: true,
  // contentMethod: ({ type, column, row, items, _columnIndex }) => {
  //   const { name } = row;
  //   // 重写默认的提示内容
  //   if (name.trim() === '建筑面积') {
  //     return '注意：建筑面积需要与取费表中“建筑面积”设置的选项保持一致，否则可能影响评标结果。';
  //   }
  //   // 其余的单元格使用默认行为
  //   return '';
  // },
};
onMounted(() => {
  getBasicInfo();
});
defineExpose({
  insertHandle,
  resetLockHandle,
  deleteHandle,
});
</script>
<style lang="scss" scoped>
.head {
  display: flex;
  align-items: center;
  padding: 0 10px;
}
.table-content {
  height: calc(100%);
}
.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }
  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }
  .vxe-cell .vxe-cell--label {
    // ::selection {
    user-select: none;
    // }
  }
}
</style>
