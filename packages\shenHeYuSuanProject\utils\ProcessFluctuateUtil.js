const { NumberUtil } = require('../../../common/NumberUtil');
const { ObjectUtil } = require('../../../common/ObjectUtil');
const YsshssConstant = require('../enum/YsshssConstant');

class ProcessFluctuateUtil {

  /**
   * 计算增减金额和增减比例
   * @param ssObj       送审对象
   * @param sdObj       审定对象
   * @param fieldName   计算差值的字段名
   */
  getChangeTotalAndRatio(ssObj, sdObj, fieldName) {
    const resObj = {};
    const sdObjElement = Number(ObjectUtil.isEmpty(sdObj[fieldName]) ? 0 : sdObj[fieldName]);
    const ssObjElement = Number(ObjectUtil.isEmpty(ssObj[fieldName]) ? 0 : ssObj[fieldName]);
    resObj[YsshssConstant.changeTotal] = NumberUtil.roundHalfUp(sdObjElement - ssObjElement);
    if (NumberUtil.isEqualNum(ssObjElement, 0)) {
      resObj[YsshssConstant.changeRatio] = 100;
    } else {
      resObj[YsshssConstant.changeRatio] = NumberUtil.roundHalfUp(NumberUtil.roundHalfUp(NumberUtil.divide(resObj[YsshssConstant.changeTotal], ssObjElement)) * 100);
    }
    return resObj;
  }


}

module.exports = {
  ProcessFluctuateUtil: new ProcessFluctuateUtil()
};