const YsshssConstant = require("../enum/YsshssConstant");
const {NumberUtil} = require("../../../common/NumberUtil");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {Service} = require("../../../core");
const zdxgl = require("../jsonData/zdxgl.json");
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");
const fyhz_ybjs = require("../../../electron/jsonData/fyhz_ybjs.json");
const fyhz_jyjs = require("../../../electron/jsonData/fyhz_jyjs.json");
const projectLevelConstant = require("../../../electron/enum/ProjectLevelConstant");
const {ArrayUtil} = require("../../../electron/utils/ArrayUtil");
const {FeeCollectionVO} = require("../../../electron/model/FeeCollectionVO");


class YsshYjsqfService extends Service{

    constructor(ctx) {
        super(ctx);
    }


    /**
     * 单位工程查询一键审取费
     * @param args
     * @returns {Promise<void>}
     */
    async getYjsqf(args){
        //查询单价构成
        let djgc =await this.getUnitDjgc(args);

        //查询费用汇总
        let summary = await this.getUnitSummary(args);

        let result ={};
        result.djgc = djgc;
        result.summary = summary;

        return  result;
    }


    /**
     * 查询一键审取费 单价构成
     * @param args
     * @returns {Promise<void>}
     */
    async getUnitDjgc(args) {
        let levelType = args.levelType;

        // 查询出工程项目下的所有取费文件
        let feeCollectionList = this.service.baseFeeFileService.getFeeCollectionList(args);

        let vo = new FeeCollectionVO();
        //对于项目的取费文件进行去重
        let feeFiles = ArrayUtil.distinctList(feeCollectionList, "feeFileId");

        // 费用总览列表数据
        this.service.baseFeeFileService.setCostOverview(vo, feeCollectionList);

        if (projectLevelConstant.construct == levelType) {
            if (ObjectUtils.isEmpty(feeFiles)) {
                return null;
            }
            // 费用总览列表数据
            this.service.baseFeeFileService.setCostOverview(vo, feeFiles);
        } else {
            //单位
            // 费用总览列表数据
            this.service.baseFeeFileService.setCostOverview(vo, feeCollectionList);
        }

        if (ObjectUtils.isEmpty(vo) && ObjectUtils.isEmpty(vo.costOverview)) {
            return null;
        }
        //获取费率总览
        let costOverview = vo.costOverview;

        for (let costOverviewElement of costOverview) {
            let t = feeFiles.find(i => i.feeFileCode == costOverviewElement.feeFileCode);
            //企业管理费 费率
            let glfTemplateRate = 15;
            //利润费率
            let lrTemplateRate = 10;

            if (!ObjectUtils.isEmpty(t)) {
                glfTemplateRate = t.managementFeeBackUp;
                lrTemplateRate = t.profitBackUp;
            }

            let createDefaultMbDjgcData = this.createDefaultMbDjgc();

            for (let defaultMbDjgcElement of createDefaultMbDjgcData) {
                //标准模板中的费率
                defaultMbDjgcElement.templateRate = null;
                //标准模板中的计算基数
                defaultMbDjgcElement.templateCaculateBase = defaultMbDjgcElement.caculateBase;

                //管理费
                if (defaultMbDjgcElement.type === 7) {
                    defaultMbDjgcElement.rate = costOverviewElement.managementFee;
                    //管理费为 15
                    defaultMbDjgcElement.templateRate = glfTemplateRate;
                }

                //利润
                if (defaultMbDjgcElement.type === 8) {
                    defaultMbDjgcElement.rate = costOverviewElement.profit;
                    //利润为10
                    defaultMbDjgcElement.templateRate = lrTemplateRate;
                }
            }

            costOverviewElement.djgc = createDefaultMbDjgcData;
        }

        return costOverview;

    }

    /**
     * 一键审取费 费用汇总
     * @param args
     * @returns {Promise<void>}
     */
    async getUnitSummary (args){
        let constructId = args.constructId;
        let singleId = args.singleId;
        let unitId = args.unitId;
        let unit = PricingFileFindUtils.getUnit(constructId,singleId,unitId);

        //计税方式
        let projectTaxCalculation = unit.projectTaxCalculation

        //单位工程 费用汇总
        let unitCostSummarys = unit.unitCostSummarys;

        //标准模板
        let jsmb ;

        //一般计税
        if(projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.GENERAL.code){
            jsmb = fyhz_ybjs;
        }

        //简易计税
        if (projectTaxCalculation.taxCalculationMethod == TaxCalculationMethodEnum.SIMPLE.code){
            jsmb = fyhz_jyjs;
        }
        let result = [];

        for (let i in unitCostSummarys) {
            let unitCostSummary = unitCostSummarys[i];
            let find = jsmb.find(i=>i.type ==unitCostSummary.type);
            if (!ObjectUtils.isEmpty(find)) {
                let summary = {};
                summary.templateName = find.name;
                summary.templateCode = find.code;
                summary.templateCalculateFormula = find.calculateFormula;
                summary.templateRate = null;

                if (find.type == "销项税额") {
                    summary.templateRate = 9;
                }

                if (find.type == "附加税费") {
                    summary.templateRate = 13.22;
                }


                let assign = Object.assign(summary, unitCostSummary);
                result.push(assign);
            }

        }
        return result;
    }


    /**
     *一键审取费 修改
     * @param args
     * @returns {Promise<void>}
     */
    async updataYjsqf(args){
        let constructId = null;
        let singleId = null;
        let unitId = null;
        //单价构成修改
        if (!ObjectUtils.isEmpty(args.djgc)){
            let djgcList = args.djgc;
            constructId = djgcList[0].constructId;
            singleId = djgcList[0].singleId;
            unitId = djgcList[0].unitId;
            for (let i in djgcList) {
                this.service.baseFeeFileService.saveCostOverview(djgcList[i]);
            }
        }

        //费用汇总修改
        if (!ObjectUtils.isEmpty(args.summary)){
            let summary = args.summary;

            constructId = summary[0].constructId;
            singleId = summary[0].singleId;
            unitId = summary[0].unitId;
            for (let i in summary) {
                this.service.unitCostSummaryService.saveCostSummary(summary[i]);
            }
        }

        if (!ObjectUtils.isEmpty(constructId)){
            //费用定额 计算
            await this.service.autoCostMathService.autoCostMath({
                constructId:constructId,
                singleId:singleId,
                unitId:unitId});

            //汇总计算
            this.service.unitCostCodePriceService.countCostCodePrice({
                constructId: constructId,
                singleId: singleId,
                unitId: unitId,
            });
        }
    }


    /**
     * 预算审核 获取 一键审取费 单价构成
     * @returns
     * @private
     */
    createDefaultMbDjgc() {
        let unitPriceBase = [
            {"sort": 1,type: 1,"code": "ZJF", "name": "直接费", "caculateBase": "RGF+CLF+JXF+XZCF", "desc": "人工费+材料费+机械费+主材费", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 2,type: 2, "code": "RGF", "name": "其中：人工费", "caculateBase": "XRGF", "desc": "人工费", "rate": null, "unitPrice": 0, "budgetTotal": 0, "budgetUnitPrice":0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 3, type:3 ,"code": "CLF", "name": "其中：材料费", "caculateBase": "XCLF", "desc": "材料费", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 4,type:4 , "code": "JXF", "name": "其中：机械费", "caculateBase": "XJXF", "desc": "机械费", "rate": null, "unitPrice": 0, "budgetTotal": 0, "budgetUnitPrice":0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 5,type: 5, "code": "ZCF", "name": "其中：主材费", "caculateBase": "XZCF", "desc": "主材费", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 6,type: 6, "code": "QFJS", "name": "(直接费中:人工费+机械费)", "caculateBase": "RGF+JXF", "desc": "人工费+机械费", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 7,type:7 , "code": "FY1", "name": "企业管理费", "caculateBase": "GLF", "desc": "", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 8,type:8 , "code": "FY2", "name": "利润", "caculateBase": "LR", "desc": "", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 9,type: 9, "code": "FY3", "name": "规费", "caculateBase": "GF", "desc": "", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 10,type: 10, "code": "FY4", "name": "安文费", "caculateBase": "AWF", "desc": "直接费+企业管理费+利润+规费", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0},
            {"sort": 11,type: 11, "code": "ZJ", "name": "合计", "caculateBase": "ZJ", "desc": "直接费+企业管理费+利润", "rate": null, "unitPrice": 0, "allPrice": 0, "notice": null,
                "displayUnitPrice": 0, "displayAllPrice": 0}
        ];

        return unitPriceBase;

    }

}


YsshYjsqfService.toString = () => '[class YsshYjsqfService]';
module.exports = YsshYjsqfService;