'use strict';



const YsshssConstant = require("../enum/YsshssConstant");
const {ProcessFluctuateUtil} = require("../utils/ProcessFluctuateUtil");
const {ConvertUtil} = require("../../../electron/utils/ConvertUtils");
const {ResponseData} = require("../../../common/ResponseData");
const {NumberUtil} = require("../../../common/NumberUtil");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const {ObjectUtils} = require("../utils/ObjectUtils");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {Service} = require("../../../core");

/**
 * 费用汇总Service
 * @class
 */
class YsshCostSummaryService extends Service {

  constructor(ctx) {
    super(ctx);
  }


  /**
   * 费用汇总
   * @returns
   */
  async initMatchCostSummaryProject(args){
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 审定
    const costSummaryProject =await PricingFileFindUtils.getUnitCostSummary(constructId, singleId, unitId);
    // 送审
    const ssCostSummaryProject =await PricingFileFindUtils.getUnitCostSummary(ssConstructId, ssSingleId, ssUnitId);


    if(ObjectUtils.isEmpty(costSummaryProject)|| ObjectUtils.isEmpty(ssCostSummaryProject)){
      return;
    }
    let ssCostSummaryProjects = ConvertUtil.deepCopy(ssCostSummaryProject);
    costSummaryProject.forEach(item=>{
      let ssCostSummaryProjectItem = ssCostSummaryProjects.filter(ssItem=>{
        if(ObjectUtils.isEmpty(item.calculateFormula) || ObjectUtils.isEmpty(ssItem.calculateFormula)){
          return false;
        }
        return ObjectUtils.compareStringsIgnoringSpaces(item.calculateFormula, ssItem.calculateFormula);
      });
      let  ssCostSummary =  null;
      if(ObjectUtils.isNotEmpty(ssCostSummaryProjectItem)){
        ssCostSummary = ssCostSummaryProjectItem.find(item=>ObjectUtils.isEmpty(item.ysshMatch));
      }
      if(ObjectUtils.isNotEmpty(ssCostSummary)){
        item.ysshGlId = ssCostSummary.sequenceNbr;
        item.ysshUnitId = ssCostSummary.unitId;
        ssCostSummary.ysshMatch = true;
      }

    });
  }

  async changeCostSummaryRelation(args){
    let {detailId,matchingId, constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId} = args;
    // 审定
    const costSummaryProject =await PricingFileFindUtils.getUnitCostSummary(constructId, singleId, unitId);
    // 送审
    const ssCostSummaryProject =await PricingFileFindUtils.getUnitCostSummary(ssConstructId, ssSingleId, ssUnitId);
    let detailItem = costSummaryProject.find(item=>item.sequenceNbr == detailId);
    let sshDetailItem = ssCostSummaryProject.find(item=>item.sequenceNbr == matchingId);
    if(ObjectUtils.isEmpty(detailItem) || ObjectUtils.isEmpty(sshDetailItem)){
      return ResponseData.fail('数据不存在');
    }
    //清空已匹配的清空
    costSummaryProject.forEach(item=>{
      if(item.ysshGlId === sshDetailItem.sequenceNbr){
        item.ysshGlId = null;
        item.ysshSingleId = null;
      }
    });
    //重新匹配
    detailItem.ysshGlId = sshDetailItem.sequenceNbr;
    return ResponseData.success();

  }
  /**
   * 获取费用汇总的数据对比结果集合
   * @return {Promise<ResponseData>}
   */
  async getCostSummaryComparisonList(args) {
    const { constructId, singleId, unitId, ssConstructId, ssSingleId, ssUnitId } = args;
    // 送审的费用汇总数据
    const ssUnitCostSummaryArray = ssUnitId ? PricingFileFindUtils.getUnitCostSummary(ssConstructId, ssSingleId, ssUnitId) : null;
    // 审定的费用汇总数据
    const sdUnitCostSummaryArray = unitId ? PricingFileFindUtils.getUnitCostSummary(constructId, singleId, unitId) : null;
    // 返回结果的数组
    const resArray = [];
    const relevanceIds = new Set();

    function setAddItemData(itemObj, sdUnitCostSummary) {
      itemObj[YsshssConstant.ysshSysj] = {
        dispNo: null,
        code: null,
        name: null,
        calculateFormula: null,
        rate: null,
        price: null,
        [YsshssConstant.change]: YsshssConstant.insert,
        [YsshssConstant.changeTotal]: sdUnitCostSummary.price,
        [YsshssConstant.changeRatio]: 100
      };
    }

    if (ObjectUtil.isNotEmpty(sdUnitCostSummaryArray)) {
      for (const sdUnitCostSummary of sdUnitCostSummaryArray) {
        // 以审定数据为基础
        const itemObj = { ...sdUnitCostSummary };
        // 如果审定的数据有已经关联的送审id  并且送审数据不为空
        if (ObjectUtil.isNotEmpty(itemObj.ysshGlId) && ObjectUtil.isNotEmpty(ssUnitCostSummaryArray)) {
          // 记录关联id
          relevanceIds.add(itemObj.ysshGlId);
          // 根据关联id找到关联的送审数据
          const ssUnitCostSummary = ssUnitCostSummaryArray.find(ssUnitCostSummary => ssUnitCostSummary.sequenceNbr === itemObj.ysshGlId);
          if (ObjectUtil.isNotEmpty(ssUnitCostSummary)) {
            // 把送审的字段填写到审定数据的字段中
            itemObj[YsshssConstant.ysshSysj] = {
              sequenceNbr: ssUnitCostSummary.sequenceNbr,
              dispNo: ssUnitCostSummary.dispNo,
              code: ssUnitCostSummary.code,
              name: ssUnitCostSummary.name,
              calculateFormula: ssUnitCostSummary.calculateFormula,
              rate: ssUnitCostSummary.rate,
              price: ssUnitCostSummary.price,
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeTotal]: 0,
              [YsshssConstant.changeRatio]: 0
            };
            if (!NumberUtil.isEqualNum(itemObj.price, ssUnitCostSummary.price)) {
              if (ObjectUtil.isEmpty(itemObj.price) || NumberUtil.isEqualNum(itemObj.price, 0)) {
                // 匹配项  但是审定的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.delete;
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.deleteExplain;
              } else if (ObjectUtil.isEmpty(ssUnitCostSummary.price) || NumberUtil.isEqualNum(ssUnitCostSummary.price, 0)) {
                // 匹配项  但是送审的金额为空或为零
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.insert;
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.insertExplain;
              } else {
                // 改项
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.change] = YsshssConstant.update;
                itemObj[YsshssConstant.ysshSysj][YsshssConstant.changeExplain] = YsshssConstant.changePriceExplain;
              }
              // 计算差额和比例
              itemObj[YsshssConstant.ysshSysj] = { ...itemObj[YsshssConstant.ysshSysj], ...ProcessFluctuateUtil.getChangeTotalAndRatio(ssUnitCostSummary, itemObj, 'price') };
            }
          } else {
            // 审增项
            setAddItemData(itemObj, sdUnitCostSummary);
          }
        } else {
          // 审增项
          setAddItemData(itemObj, sdUnitCostSummary);
        }
        resArray.push(itemObj);
      }
    }

    if (ObjectUtil.isNotEmpty(ssUnitCostSummaryArray)) {
      // 审删项数组
      const subtractArr = ssUnitCostSummaryArray.filter(ssUnitCostSummary => !relevanceIds.has(ssUnitCostSummary.sequenceNbr));
      if (ObjectUtil.isNotEmpty(subtractArr)) {
        for (const ssUnitCostSummary of subtractArr) {
          resArray.push({
            dispNo: null,
            code: null,
            name: null,
            calculateFormula: null,
            rate: null,
            price: null,
            [YsshssConstant.ysshSysj]: {
              [YsshssConstant.change]: YsshssConstant.delete,
              [YsshssConstant.changeTotal]: NumberUtil.relativeValue(ssUnitCostSummary.price),
              [YsshssConstant.changeRatio]: -100,
              sequenceNbr: ssUnitCostSummary.sequenceNbr,
              dispNo: ssUnitCostSummary.dispNo,
              code: ssUnitCostSummary.code,
              name: ssUnitCostSummary.name,
              calculateFormula: ssUnitCostSummary.calculateFormula,
              rate: ssUnitCostSummary.rate,
              price: ssUnitCostSummary.price
            }
          });
        }
      }
    }

    return ResponseData.success(resArray);
  }

  async getGfeeFeeComparisonData(args) {
    const { ssGfeeResult, sdGfeeResult } = args;
    const resData = [];

    function setAddGfeeData(item, sdFindItem) {
      item[YsshssConstant.ysshSysj] = {
        [YsshssConstant.change]: YsshssConstant.noChange,
        costMajorName: sdFindItem.costMajorName,
        costFeeBase: '',
        gfee: '',
        gfeeRate: '',
        feeAmount: '',
        [YsshssConstant.changeTotal]: sdFindItem.feeAmount,
        [YsshssConstant.changeRatio]: 100
      };
    }

    if (ObjectUtil.isNotEmpty(sdGfeeResult)) {
      for (const sdGfee of sdGfeeResult) {
        const item = { ...sdGfee };
        if (ObjectUtil.isNotEmpty(ssGfeeResult)) {
          const ssFindItem = ssGfeeResult.find(ssGfee => item.costMajorName === ssGfee.costMajorName);
          if (ObjectUtil.isNotEmpty(ssFindItem)) {
            const roundHalfUp = NumberUtil.roundHalfUp(item.feeAmount - ssFindItem.feeAmount);
            let number = NumberUtil.divide(roundHalfUp, ssFindItem.feeAmount);
            if (!ObjectUtil.isNumber(number)) {
              number = 0;
            }
            // 有匹配到的送审数据
            item[YsshssConstant.ysshSysj] = {
              [YsshssConstant.change]: YsshssConstant.noChange,
              costMajorName: ssFindItem.costMajorName,
              costFeeBase: ssFindItem.costFeeBase,
              gfee: ssFindItem.gfee,
              gfeeRate: ssFindItem.gfeeRate,
              feeAmount: ssFindItem.feeAmount,
              [YsshssConstant.changeTotal]: roundHalfUp,
              [YsshssConstant.changeRatio]: NumberUtil.roundHalfUp(NumberUtil.roundHalfUp4(number) * 100)
            };
          } else {
            // 审增项
            setAddGfeeData(item, sdGfee);
          }
        } else {
          // 审增项
          setAddGfeeData(item, sdGfee);
        }
        resData.push(item);
      }
    } else {
      // 审定没有数据  那么如果有送审的  送审全部为审删数据
      if (ObjectUtil.isNotEmpty(ssGfeeResult)) {
        for (const ssGfee of ssGfeeResult) {
          resData.push({
            costMajorName: ssGfee.costMajorName,
            costFeeBase: '',
            gfee: '',
            feeAmount: '',
            [YsshssConstant.ysshSysj]: {
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeTotal]: ssGfee.feeAmount,
              [YsshssConstant.changeRatio]: -100,
              costMajorName: ssGfee.costMajorName,
              costFeeBase: ssGfee.costFeeBase,
              gfee: ssGfee.gfee,
              gfeeRate: ssFindItem.gfeeRate,
              feeAmount: ssGfee.feeAmount
            }
          });
        }
      }
    }

    return ResponseData.success(resData);
  }

  async getSafeFeeComparisonData(args) {
    const { ssSafeFeeResult, sdSafeFeeResult } = args;
    const resData = [];

    function setAddGfeeData(item, sdFindItem) {
      item[YsshssConstant.ysshSysj] = {
        [YsshssConstant.change]: YsshssConstant.noChange,
        costMajorName: sdFindItem.costMajorName,
        costFeeBase: '',
        basicRate: '',
        addRate: '',
        feeAmount: '',
        [YsshssConstant.changeTotal]: sdFindItem.feeAmount,
        [YsshssConstant.changeRatio]: 100
      };
    }

    if (ObjectUtil.isNotEmpty(sdSafeFeeResult)) {
      for (const sdSafeFee of sdSafeFeeResult) {
        const item = { ...sdSafeFee };
        if (ObjectUtil.isNotEmpty(ssSafeFeeResult)) {
          const ssFindItem = ssSafeFeeResult.find(ssSafeFee => item.costMajorName === ssSafeFee.costMajorName);
          if (ObjectUtil.isNotEmpty(ssFindItem)) {
            const roundHalfUp = NumberUtil.roundHalfUp(item.feeAmount - ssFindItem.feeAmount);
            // 有匹配到的送审数据
            item[YsshssConstant.ysshSysj] = {
              [YsshssConstant.change]: YsshssConstant.noChange,
              costMajorName: ssFindItem.costMajorName,
              costFeeBase: ssFindItem.costFeeBase,
              basicRate: ssFindItem.basicRate,
              addRate: ssFindItem.addRate,
              feeAmount: ssFindItem.feeAmount,
              [YsshssConstant.changeTotal]: roundHalfUp,
              [YsshssConstant.changeRatio]: ssFindItem.feeAmount==0? "0.00":NumberUtil.roundHalfUp(NumberUtil.roundHalfUp4(NumberUtil.divide(roundHalfUp, ssFindItem.feeAmount)) * 100)
            };
          } else {
            // 审增项
            setAddGfeeData(item, sdSafeFee);
          }
        } else {
          // 审增项
          setAddGfeeData(item, sdSafeFee);
        }
        resData.push(item);
      }
    } else {
      // 审定没有数据  那么如果有送审的  送审全部为审删数据
      if (ObjectUtil.isNotEmpty(ssSafeFeeResult)) {
        for (const ssSafeFee of ssSafeFeeResult) {
          resData.push({
            costMajorName: ssSafeFee.costMajorName,
            costFeeBase: '',
            basicRate: '',
            addRate: '',
            feeAmount: '',
            [YsshssConstant.ysshSysj]: {
              [YsshssConstant.change]: YsshssConstant.noChange,
              [YsshssConstant.changeTotal]: ssSafeFee.feeAmount,
              [YsshssConstant.changeRatio]: -100,
              costMajorName: ssSafeFee.costMajorName,
              costFeeBase: ssSafeFee.costFeeBase,
              gfee: ssSafeFee.gfee,
              feeAmount: ssSafeFee.feeAmount
            }
          });
        }
      }
    }
    return ResponseData.success(resData);
  }

}


YsshCostSummaryService.toString = () => '[class YsshCostSummaryService]';
module.exports = YsshCostSummaryService;

