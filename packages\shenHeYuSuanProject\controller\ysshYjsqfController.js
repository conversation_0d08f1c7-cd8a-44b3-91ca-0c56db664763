const {ResponseData} = require("../../../common/ResponseData");
const { Controller } = require('../../../core');


/**
 * 一键审取费
 */
class YsshYjsqfController extends Controller{

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 查询 一键审取费 单价构成 和 费用汇总
     * @param
     * @returns {Promise<ResponseData>}
     */
    async getYjsqf(args){
        let data = await this.service.shenHeYuSuanProject.ysshYjsqfService.getYjsqf(args);
        return ResponseData.success(data);
    }


    /**
     * 一键审取费 修改
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async updataYjsqf(args){
        await this.service.shenHeYuSuanProject.ysshYjsqfService.updataYjsqf(args);

        return ResponseData.success();

    }






}


YsshYjsqfController.toString = () => '[class YsshYjsqfController]';
module.exports = YsshYjsqfController;