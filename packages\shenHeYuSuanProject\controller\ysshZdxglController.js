const {ResponseData} = require("../../../common/ResponseData");
const { Controller } = require('../../../core');


/**
 * 重点项过滤
 */
class YsshZdxglController extends Controller{

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 重点项过滤 默认数据
     * @param
     * @returns {Promise<ResponseData>}
     */
    async getDefaultZdxglData(){
        let data = await this.service.shenHeYuSuanProject.ysshZdxglService.getDefaultZdxglData();
        return ResponseData.success(data);
    }


    /**
     * 重点项过滤 修改 单位工程数据
     * @param
     * @returns {Promise<ResponseData>}
     */
    async updateUnitZdxglData(args){

        await this.service.shenHeYuSuanProject.ysshZdxglService.updateUnitZdxglData(args);
        return ResponseData.success();
    }

    /**
     * 重点项过滤 获取 单位工程数据
     * @param
     * @returns {Promise<ResponseData>}
     */
    async getUnitZdxglData(args){

        let data = await this.service.shenHeYuSuanProject.ysshZdxglService.getUnitZdxglData(args);
        return ResponseData.success(data);
    }


    /**
     * 重点项过滤 清除 单位工程数据
     * @param
     * @returns {Promise<ResponseData>}
     */
    async deleteUnitZdxglData(args){

        await this.service.shenHeYuSuanProject.ysshZdxglService.deleteUnitZdxglData(args);
        return ResponseData.success();
    }

    /**
     * 查询工程特征和基本信息
     * @param args
     * @returns {Promise<ResponseData>}
     */
    async getYsshProjectOverview(args){
        const res = await this.service.shenHeYuSuanProject.ysshZdxglService.getYsshProjectOverview(args);
        return ResponseData.success(res);
    }


}


YsshZdxglController.toString = () => '[class YsshZdxglController]';
module.exports = YsshZdxglController;