const path = require('path');
const {Service} = require("../../../core");
const util = require('util');
const exec = util.promisify(require('child_process').exec);
const UtilsPs = require('../../../core/ps');
const {ExcelUtil} = require("../../../electron/utils/ExcelUtil.js");
const fs = require('fs');
const TaxCalculationMethodEnum = require("../../../electron/enum/TaxCalculationMethodEnum");
const {
    app: electronApp,
    dialog, shell, BrowserView, Notification,
    powerMonitor, screen, nativeTheme, BrowserWindow
} = require('electron');
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const ExcelEnum = require("../../../electron/enum/ExcelEnum");
const ConstantUtil = require("../../../electron/enum/ConstantUtil");
const {ShenHeWriteExcelBySheetUtil} = require("../utils/ShenHeWriteExcelBySheetUtil");
const {ShenHeExcelUtil} = require("../utils/ShenHeExcelUtil");

class ShenHeExportPdfService extends Service {
    constructor(ctx) {
        super(ctx);
    }


    async runCommand(command) {
        try {
            const {stdout, stderr} = await exec(command);
            console.log(`命令输出结果: ${stdout}`);
            console.error(`命令错误输出: ${stderr}`);
        } catch (error) {
            console.error(`执行命令时出错: ${error}`);
        }
    }


    async excelToPdf(params) {

        const dialogOptions = {
            title: '保存文件',
            defaultPath: params.headLine,
            filters: [{name: 'pdf', extensions: ['pdf']}]
        };
        let result = dialog.showSaveDialogSync(null, dialogOptions);
        //弹出弹框确定路径以后 走下面
        if (result && !result.canceled) {
            let filePath = result;
            await this.exportPdf(params, filePath);
            return true;
        } else {
            return false;
        }
    }


    async exportPdf(params, pdfPath) {
        let constructIs2022= PricingFileFindUtils.getConstructDeStandard(params.id)==ConstantUtil.DE_STANDARD_22;
        let project = await this.initWorkBook("project",constructIs2022);
        let single = await this.initWorkBook("single",constructIs2022);
        let unit = await this.initWorkBook("unit",constructIs2022);

        let fileDir = this.getProjectRootPath() + "\\excelTemplate\\exportShenHe\\" + params.headLine;
        let workBookList = [];
        let args = {};
        args['constructId'] = params.id;
        args['ssConstructId'] = params.ssConstructId;
        await this.parseParams(params, project, single, unit, fileDir, args, workBookList,constructIs2022);

        //合成一个大excel文件  最后生成一个pdf文件
        //先对workBookList[0]._worksheets 按照 worksheets 进行重排  worksheets属性 始终是有序的
        for (let i = 0; i < workBookList[0].worksheets.length; i++) {
            //确定_worksheets 中当前的索引  及id相同的索引 进行位置交换
            let indexCur = await this.getIndexIn_worksheets(i + 1, workBookList[0]);
            let indexId = await this.getIndexOfSameId(workBookList[0].worksheets[i].id, workBookList[0]);
            [workBookList[0]._worksheets[indexCur], workBookList[0]._worksheets[indexId]] = [workBookList[0]._worksheets[indexId], workBookList[0]._worksheets[indexCur]];
        }
        for (let i = 1; i < workBookList.length; i++) {
            let bookElement = workBookList[i];
            for (let j = 0; j < bookElement.worksheets.length; j++) {
                let worksheet = bookElement.worksheets[j];
                if (worksheet != null) {
                    workBookList[0]._worksheets.push(worksheet);
                }
            }
        }
        //excel表格乱序 展示顺序是按照 worksheets数组的顺序来的 而不是  _worksheets
        //如果这里不重置id和orderNo 会导致sheet名称和实际内容对不上  因为会有重复的id和orderNo
        let orderNo = 0;
        for (let i = 0; i < workBookList[0]._worksheets.length; i++) {
            let worksheetSam = workBookList[0]._worksheets[i];
            if (worksheetSam != null) {
                worksheetSam.id = ++orderNo;
                worksheetSam.orderNo = orderNo;
            }
        }
        //生成excel
        let excelFilePath = UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\exportShenHe\\pdf.xlsx";
        await this.createDirectory(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\exportShenHe");
        await workBookList[0].xlsx.writeFile(excelFilePath);
        //设置环境变量
        let javaCommand = UtilsPs.getExtraResourcesDir() + "\\jre\\bin\\java";
        let javaHomePath = UtilsPs.getExtraResourcesDir() + "\\jre";
        let jarPath = UtilsPs.getExtraResourcesDir() + "\\pdfUtil.jar";
        let parameters = excelFilePath
            + "   " + pdfPath;

        await this.runCommand(javaCommand + " -DJAVA_HOME=" + javaHomePath + "  -jar " + jarPath + "  " + parameters);
        //删除原来生成的excel文件
        fs.unlink(UtilsPs.getExtraResourcesDir() + "\\excelTemplate\\exportShenHe\\pdf.xlsx", (err) => {
            if (err) {
                console.error('删除文件时出错:', err);
                return;
            }
            console.log('文件删除成功！');
        });
    }

    async getIndexIn_worksheets(order, workbook) {
        let index = 0;
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i] != null) {
                index++;
                if (index == order) {
                    return i;
                }
            }
        }
    }

    async getIndexOfSameId(idParam, workbook) {
        for (let i = 0; i < workbook._worksheets.length; i++) {
            if (workbook._worksheets[i] != null && workbook._worksheets[i].id == idParam) {
                return i;
            }
        }
    }

    getProjectRootPath() {
        // let relativePath = __filename;
        // let index = relativePath.indexOf("pricing-cs");
        // let prefix = relativePath.substring(0,index);
        return UtilsPs.getExtraResourcesDir();
        // return prefix+"pricing-cs";
    }

    async createDirectory(directoryPath) {
        if (!fs.existsSync(directoryPath)) {
            fs.mkdirSync(directoryPath, {recursive: true});
        } else {
        }
    }

    async deleteDirectory(dirPath) {
        if (fs.existsSync(dirPath)) {
            fs.readdirSync(dirPath).forEach(file => {
                const filePath = path.join(dirPath, file);

                if (fs.lstatSync(filePath).isDirectory()) {
                    deleteDirectory(filePath); // 递归删除子目录
                } else {
                    fs.unlinkSync(filePath); // 删除文件
                }
            });

            fs.rmdirSync(dirPath); // 删除空目录
            console.log('目录删除成功');
        } else {
            console.log('目录不存在');
        }
    }

    async initWorkBook(projectLevel,constructIs2022) {
        let loadDir = "";
        if (!constructIs2022) {
            loadDir = this.getProjectRootPath() + "\\excelTemplate\\shenhe\\12";
        }else {

        }

        let loadPath = "";

        if (projectLevel == "single") {
            loadPath = loadDir + "\\单项工程层级.xlsx";
        } else if (projectLevel == "unit") {
            loadPath = loadDir + "\\单位工程层级.xlsx";
        } else if (projectLevel == "project") {
            loadPath = loadDir + "\\工程项目层级.xlsx";
        }

        //加载workbook
        let workbook = await ExcelUtil.readToWorkBook(loadPath);
        return workbook;
    }

    async parseParams(params, project, single, unit, fileDir, args, workBookList,constructIs2022) {
        if (args == null) {
            args = {};
        }
        for (let i = 0; i < params.childrenList.length; i++) {
            let param = params.childrenList[i];
            //如果为总工程层级
            if (param.projectLevel != null && param.projectLevel == "project") {
                args["constructId"] = params.id;
                args["sdConstructId"] = params.id;
                args["ssConstructId"] = params.ssConstructId;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(project, param.projectLevel, param.headLine, args);
                } else {
                    project.removeWorksheet(param.headLine);
                }
                if (project.worksheets.length == 1 && project.worksheets[0].name == "格式替换sheet") {
                    project.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == "single") {
                args["singleId"] = params.id;
                args["sdSingleId"] = params.id;
                args["ssSingleId"] = params.ssSingleId;
                args["levelType"] = params.levelType;
                if (param.selected) {
                    await this.getWorkSheetWithData(single, param.projectLevel, param.headLine, args);
                } else {
                    single.removeWorksheet(param.headLine);
                }
                if (single.worksheets.length == 1 && single.worksheets[0].name == "格式替换sheet") {
                    single.removeWorksheet("格式替换sheet");
                }
            }
            if (param.projectLevel != null && param.projectLevel == "unit") {
                //分情况  如果预算项目为单位工程 args要重新赋值 因为返回前端的是constructId
                if (params.biddingType == 2) {  //如果预算工程为单位工程
                    args["constructId"] = params.id;
                    args["singleId"] = params.id;
                    args["unitId"] = params.id;

                    args["sdConstructId"] = params.id;
                    args["ssConstructId"] = params.ssConstructId;
                    args["sdSingleId"] = params.id;
                    args["ssSingleId"] = params.ssSingleId;
                    args["sdUnitId"] = params.id;
                    args["ssUnitId"] = params.ssUnitId;
                } else {
                    args["unitId"] = params.id;
                    args["sdUnitId"] = params.id;
                    args["ssUnitId"] = params.ssUnitId;
                }
                if (param.selected) {
                    await this.getWorkSheetWithData(unit, param.projectLevel, param.headLine, args);
                } else {
                    unit.removeWorksheet(param.headLine);
                }
                if (unit.worksheets.length == 1 && unit.worksheets[0].name == "格式替换sheet") {
                    unit.removeWorksheet("格式替换sheet");
                }
            }
            //1、去对应的栏目下拿到对应的sheet
            //2、对每一层级的sheet进行组装   生成文件 并压缩
            //3、返回文件流  删掉原文件
        }

        if (params.childrenList != null && params.childrenList[0].projectLevel == "project") {
            project.removeWorksheet("格式替换sheet");
            if (project.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(project);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == "single") {
            single.removeWorksheet("格式替换sheet");
            if (single.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(single);
            }
        }
        if (params.childrenList != null && params.childrenList[0].projectLevel == "unit") {
            unit.removeWorksheet("格式替换sheet");
            if (unit.model.worksheets.length > 0) {  //在有sheet表的前提下创建目录和文件
                workBookList.push(unit);
            }
        }
        let filter = params.childrenList.filter(itemParam => itemParam.childrenList != null);//含有子节点的节点
        if (filter != null) {
            let directory;
            for (let i = 0; i < filter.length; i++) {
                //同时对single  和 unit对象进行初始化
                single = await this.initWorkBook("single",constructIs2022);
                unit = await this.initWorkBook("unit",constructIs2022);
                directory = fileDir + "\\" + filter[i].headLine;
                await this.parseParams(filter[i], project, single, unit, directory, args, workBookList,constructIs2022);
            }
        }
    }


    async getWorkSheetWithData(workbook, projectType, sheetName, args) {
        let worksheet = workbook.getWorksheet(sheetName);
        args["workbook"] = workbook;
        try {
            await this.switchWorkSheet(projectType, worksheet, args);
        } catch (e) {
            console.log("报表填充数据异常");
        }
        return worksheet;
    }

    async switchWorkSheet(projectType, worksheet, args) {
        let {constructId, unitId, singleId, workbook} = args;
        let unit = {};
        if (constructId != null && singleId != null && unitId != null) {
            unit = PricingFileFindUtils.getUnit(constructId, singleId, unitId);
        }
        if (projectType == "project") {
            let constructProjectJBXX = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //工程项目层级
                case "【封面1】工程审核书封面":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet1(constructProjectJBXX, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 13;
                    headArgsQd1['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    break;
                case "【封面2】工程审核书签署页":
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet2(constructProjectJBXX, worksheet);
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 9;
                    headArgsQd2['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    break;
                case "【封面3】审核签署表":
                    let constructProjectTotal = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet3(args, constructProjectJBXX);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet3(constructProjectTotal, worksheet);
                    let headArgsQd3 = {};
                    headArgsQd3['headStartNum'] = 1;
                    headArgsQd3['headEndNum'] = 11;
                    headArgsQd3['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    break;
                case "【封面4】工程审核认证单":
                    let constructProjectTotal4 = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet4(args, constructProjectJBXX);
                    let constructProjectSheet4List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet4List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet4(constructProjectTotal4, constructProjectSheet4List, worksheet);
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 4;
                    headArgsQd4['titlePage'] = false;
                    headArgsQd4['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    // let heJiCell4 = ExcelUtil.findValueCell(worksheet, "合计");
                    // let row4 = worksheet.getRow(heJiCell4.cell._row._number);
                    // await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(1, row4.number + 1, worksheet, workSheetGeshi, 1, 4, 8);
                    // await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(2, row4.number + 2, worksheet, workSheetGeshi, 1, 4, 8);
                    // await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(3, row4.number + 3, worksheet, workSheetGeshi, 1, 4, 8);

                    let totalSS = constructProjectTotal4.filter(object => object.name == "totalS")[0];
                    await this.service.shenHeYuSuanProject.shenHeExportViewService.sheetMerge(worksheet, headArgsQd4['headEndNum'], totalSS.remark);
                    break;
                case "【封面5】工程造价审查书":
                    let constructProjectTotal5 = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet3(args, constructProjectJBXX);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet5(constructProjectTotal5, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 9;
                    headArgsQd5['titlePage'] = true;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    break;
                case "【项1】工程审核汇总对比表":
                    let constructProjectTotal6 = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet4(args, constructProjectJBXX);
                    let constructProjectSheet6List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet6List(args, []);
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet6(constructProjectTotal6, constructProjectSheet6List, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 4;
                    headArgsQd6['titlePage'] = false;
                    headArgsQd6['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    let heJiCell6 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row6 = worksheet.getRow(heJiCell6.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(5, row6.number + 1, worksheet, workSheetGeshi, 3, 6, 9);
                    break;
                case "【人材机1】人材机汇总对比表":
                    let constructProjectSheet7List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSheet7List(args);
                    constructProjectJBXX["projectRcjHeji"] = args["projectRcjHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToProjectSheet7(constructProjectJBXX, constructProjectSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell7 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(7, row7.number + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    break;
            }
        }
        if (projectType == "single") {
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //单项工程层级
                case "【单项1】单项工程审核对比表":
                    let constructSingle1 = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSingleJBXX(args);
                    let constructSingleSheet1List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructSingleSheet1List(args);
                    constructSingle1["singleHeji"] = args["singleHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToSingleSheet1(constructSingle1, constructSingleSheet1List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell2 = ExcelUtil.findValueCell(worksheet, "合计（不含设备及其税金）");
                    let row2 = worksheet.getRow(heJiCell2.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(1, row2.number + 1, worksheet, workSheetGeshi, 2, 4, 5);
                    break;
            }
        }
        if (projectType == "unit") {
            let constructUnitJBXX = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructProjectSingleUnitJBXX(args);
            let workbook = args["workbook"];
            let workSheetGeshi = workbook.getWorksheet("格式替换sheet");
            switch (worksheet.name) {
                //单位工程层级
                case "【费1】单位工程审核对比表":
                    let constructUnitSheet1List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet1List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet1(constructUnitJBXX, constructUnitSheet1List, worksheet);
                    let headArgsQd1 = {};
                    headArgsQd1['headStartNum'] = 1;
                    headArgsQd1['headEndNum'] = 4;
                    headArgsQd1['titlePage'] = false;
                    headArgsQd1['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd1);
                    // let heJiCell1 = ExcelUtil.findValueCell(worksheet, "合计");
                    // let row1 = worksheet.getRow(heJiCell1.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(1, worksheet._rows.length + 1, worksheet, workSheetGeshi, 3, 5, 8);
                    break;
                case "【分部1】分部分项清单对比表":
                    let constructUnitSheet2List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet2List(args);
                    constructUnitJBXX["unitFbfxHeji"] = args["unitFbfxHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet2(constructUnitJBXX, constructUnitSheet2List, worksheet);
                    let headArgsQd2 = {};
                    headArgsQd2['headStartNum'] = 1;
                    headArgsQd2['headEndNum'] = 4;
                    headArgsQd2['titlePage'] = false;
                    headArgsQd2['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd2);
                    let heJiCell2 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row2 = worksheet.getRow(heJiCell2.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(3, row2.number + 1, worksheet, workSheetGeshi, 3, 7, 12);
                    break;
                case "【分部6】分部分项清单对比表(含关联项)":
                    let constructUnitSheet3List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet3List(args);
                    constructUnitJBXX["unitFbfxHeji"] = args["unitFbfxHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet3(constructUnitJBXX, constructUnitSheet3List, worksheet);
                    let headArgsQd3 = {};
                    headArgsQd3['headStartNum'] = 1;
                    headArgsQd3['headEndNum'] = 4;
                    headArgsQd3['titlePage'] = false;
                    headArgsQd3['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd3);
                    let heJiCell3 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row3 = worksheet.getRow(heJiCell3.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(5, row3.number + 1, worksheet, workSheetGeshi, 4, 8, 14);
                    await this.service.shenHeYuSuanProject.shenHeExportViewService.sheetMerge(worksheet, headArgsQd3['headEndNum'], "");
                    break;
                case "【措施1】措施项目审核对比表":
                    let constructUnitSheet4List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet4List(args);
                    constructUnitJBXX["unitCuoshiHeji"] = args["unitCuoshiHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet4(constructUnitJBXX, constructUnitSheet4List, worksheet);
                    let headArgsQd4 = {};
                    headArgsQd4['headStartNum'] = 1;
                    headArgsQd4['headEndNum'] = 4;
                    headArgsQd4['titlePage'] = false;
                    headArgsQd4['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd4);
                    let heJiCell4 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row4 = worksheet.getRow(heJiCell4.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(7, row4.number + 1, worksheet, workSheetGeshi, 4, 7, 11);
                    break;
                case "【其他1】其他项目审核对比表":
                    let constructUnitSheet5List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet5List(args);
                    constructUnitJBXX["unitOtherHeji"] = args["unitOtherHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet5(constructUnitJBXX, constructUnitSheet5List, worksheet);
                    let headArgsQd5 = {};
                    headArgsQd5['headStartNum'] = 1;
                    headArgsQd5['headEndNum'] = 3;
                    headArgsQd5['titlePage'] = false;
                    headArgsQd5['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd5);
                    let heJiCell5 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row5 = worksheet.getRow(heJiCell5.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(9, row5.number + 1, worksheet, workSheetGeshi, 2, 4, 6);
                    break;
                case "【计日工1】计日工审核对比表":
                    let constructUnitSheet6List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet6List(args);
                    constructUnitJBXX["unitOtherJrgHeji"] = args["unitOtherJrgHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet6(constructUnitJBXX, constructUnitSheet6List, worksheet);
                    let headArgsQd6 = {};
                    headArgsQd6['headStartNum'] = 1;
                    headArgsQd6['headEndNum'] = 4;
                    headArgsQd6['titlePage'] = false;
                    headArgsQd6['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd6);
                    let heJiCell6 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row6 = worksheet.getRow(heJiCell6.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(11, row6.number + 1, worksheet, workSheetGeshi, 4, 8, 12);
                    break;
                case "【人材机2】人材机审核对比表":
                    let constructUnitSheet7List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet7List(args);
                    constructUnitJBXX["unitRcjHeji1"] = args["unitRcjHeji1"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet7(constructUnitJBXX, constructUnitSheet7List, worksheet);
                    let headArgsQd7 = {};
                    headArgsQd7['headStartNum'] = 1;
                    headArgsQd7['headEndNum'] = 4;
                    headArgsQd7['titlePage'] = false;
                    headArgsQd7['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd7);
                    let heJiCell7 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row7 = worksheet.getRow(heJiCell7.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(13, row7.number + 1, worksheet, workSheetGeshi, 5, 10, 15);
                    break;
                case "【人材机3】人材机价差汇总对比表":
                    let constructUnitSheet8List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet8List(args);
                    constructUnitJBXX["unitRcjHeji2"] = args["unitRcjHeji2"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet8(constructUnitJBXX, constructUnitSheet8List, worksheet);
                    let headArgsQd8 = {};
                    headArgsQd8['headStartNum'] = 1;
                    headArgsQd8['headEndNum'] = 4;
                    headArgsQd8['titlePage'] = false;
                    headArgsQd8['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd8);
                    let heJiCell8 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row8 = worksheet.getRow(heJiCell8.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(15, row8.number + 1, worksheet, workSheetGeshi, 5, 10, 16);
                    break;
                case "【增值税1】材料、机械、设备增值税对比表":
                    let constructUnitSheet9List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet9List(args);
                    constructUnitJBXX['unitcljxsbHeji'] = args["unitcljxsbHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet9(constructUnitJBXX, constructUnitSheet9List, worksheet);
                    let headArgsQd9 = {};
                    headArgsQd9['headStartNum'] = 1;
                    headArgsQd9['headEndNum'] = 4;
                    headArgsQd9['titlePage'] = false;
                    headArgsQd9['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd9);
                    let heJiCell9 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row9 = worksheet.getRow(heJiCell9.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(17, row9.number + 1, worksheet, workSheetGeshi, 5, 10, 14);
                    await this.service.shenHeYuSuanProject.shenHeExportViewService.sheetMerge(worksheet, headArgsQd9['headEndNum'], "");
                    break;
                case "【规费1】规费明细对比表":
                    let constructUnitSheet10List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet10List(args);
                    constructUnitJBXX["unitGuifeiHeji"] = args["unitGuifeiHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet10(constructUnitJBXX, constructUnitSheet10List, worksheet);
                    let headArgsQd10 = {};
                    headArgsQd10['headStartNum'] = 1;
                    headArgsQd10['headEndNum'] = 4;
                    headArgsQd10['titlePage'] = false;
                    headArgsQd10['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd10);
                    let heJiCell10 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row10 = worksheet.getRow(heJiCell10.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(19, row10.number + 1, worksheet, workSheetGeshi, 4, 8, 11);
                    break;
                case "【安全文施1】安全文明施工费明细对比表":
                    let constructUnitSheet11List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet11List(args);
                    constructUnitJBXX["unitAnwenfeiHeji"] = args["unitAnwenfeiHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet11(constructUnitJBXX, constructUnitSheet11List, worksheet);
                    let headArgsQd11 = {};
                    headArgsQd11['headStartNum'] = 1;
                    headArgsQd11['headEndNum'] = 4;
                    headArgsQd11['titlePage'] = false;
                    headArgsQd11['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd11);
                    let heJiCell11 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row11 = worksheet.getRow(heJiCell11.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(21, row11.number + 1, worksheet, workSheetGeshi, 4, 8, 13);
                    break;
                case "【工程量1】审定工程量计算书":
                    let constructUnitSheet12List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet12List(args);
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet12(constructUnitJBXX, constructUnitSheet12List, worksheet);
                    let headArgsQd12 = {};
                    headArgsQd12['headStartNum'] = 1;
                    headArgsQd12['headEndNum'] = 3;
                    headArgsQd12['titlePage'] = false;
                    headArgsQd12['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd12);
                    // let heJiCell12 = ExcelUtil.findValueCell(worksheet, "合计");
                    // let row12 = worksheet.getRow(heJiCell12.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(23, worksheet._rows.length + 1, worksheet, workSheetGeshi, 2, 5, 7);
                    break;
                case "【增值税4】增值税进项税额对比表":
                    let constructUnitSheet13List = await this.service.shenHeYuSuanProject.shenHeExportViewService.getconstructUnitSheet13List(args);
                    constructUnitJBXX["unitZzsjxseHeji"] = args["unitZzsjxseHeji"];
                    await ShenHeWriteExcelBySheetUtil.writeDataToUnitSheet13(constructUnitJBXX, constructUnitSheet13List, worksheet);
                    let headArgsQd13 = {};
                    headArgsQd13['headStartNum'] = 1;
                    headArgsQd13['headEndNum'] = 3;
                    headArgsQd13['titlePage'] = false;
                    headArgsQd13['workSheetGeshi'] = workSheetGeshi;
                    await ShenHeExcelUtil.dealWithPage(worksheet, workbook, headArgsQd13);
                    let heJiCell13 = ExcelUtil.findValueCell(worksheet, "合计");
                    let row13 = worksheet.getRow(heJiCell13.cell._row._number);
                    await ShenHeExcelUtil.copyRowsWithOtherSheetHeji(25, row13.number + 1, worksheet, workSheetGeshi, 2, 3, 5);
                    break;
                default:
            }
        }

    }

    async getRatioWidthSheet(workSheet) {

        let pageSetupJsonObject = {
            "fitToPage": false,
            "margins": {
                "left": 0.585166666666667,
                "right": 0.585166666666667,
                "top": 0.979166666666667,
                "bottom": 0,
                "header": 0.979166666666667,
                "footer": 0
            },
            "paperSize": 9,
            "orientation": "portrait",
            "horizontalDpi": 4294967295,
            "verticalDpi": 4294967295,
            "pageOrder": "downThenOver",
            "blackAndWhite": false,
            "draft": false,
            "cellComments": "None",
            "errors": "displayed",
            "scale": 100,
            "fitToWidth": 1,
            "fitToHeight": 1,
            "firstPageNumber": 1,
            "useFirstPageNumber": false,
            "usePrinterDefaults": false,
            "copies": 1,
            "showRowColHeaders": false,
            "showGridLines": false,
            "horizontalCentered": true,
            "verticalCentered": false
        }
        let columnWidthTotal = 0;
        for (let i = 0; i < workSheet._columns.length; i++) {
            let columnWidth = workSheet._columns[i].width;
            columnWidthTotal += columnWidth;
        }
        let differ = 0;
        if (workSheet.name.includes("【封面3】审核签署表")
            || workSheet.name.includes("【封面4】工程审核认证单")
            || workSheet.name.includes("【封面5】工程造价审查书")
            || workSheet.name.includes("【项1】工程审核汇总对比表")
            || workSheet.name.includes("【人材机1】人材机汇总对比表")
            || workSheet.name.includes("【费1】单位工程审核对比表")
            || workSheet.name.includes("【分部1】分部分项清单对比表")
            || workSheet.name.includes("【分部6】分部分项清单对比表(含关联项)")
            || workSheet.name.includes("【措施1】措施项目审核对比表")
            || workSheet.name.includes("【人材机2】人材机审核对比表")
            || workSheet.name.includes("【人材机3】人材机价差汇总对比表")
            || workSheet.name.includes("【增值税1】材料、机械、设备增值税对比表")
            || workSheet.name.includes("【规费1】规费明细对比表")
            || workSheet.name.includes("【安全文施1】安全文明施工费明细对比表")) {
            differ = (ExcelEnum.A4WidthHorizontal - ExcelEnum.A4LeftHorizontal - ExcelEnum.A4RightHorizontal);
        } else {
            differ = (ExcelEnum.A4Width - ExcelEnum.A4Left - ExcelEnum.A4Right);
        }

        for (let i = 0; i < workSheet._columns.length; i++) {
            workSheet._columns[i].width = (workSheet._columns[i].width / columnWidthTotal) * differ;
        }
        if (workSheet.name.includes("封面") || workSheet.name.includes("扉页") || workSheet.name.includes("编制说明")
            || workSheet.name.includes("工程项目总价表")) {
            workSheet.pageSetup = pageSetupJsonObject;
        }
    }


}

ShenHeExportPdfService.toString = () => '[class ShenHeExportPdfService]';
module.exports = ShenHeExportPdfService;
