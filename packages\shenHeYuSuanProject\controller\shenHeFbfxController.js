const {ResponseData} = require("../../../electron/utils/ResponseData");
const { Controller } = require('../../../core');

/**
 * 分部分项操作
 */
class ShenHeFbfxController extends Controller {
  constructor(ctx) {
    super(ctx);
  }


  // ysshFbfxService=this.service.shenHeYuSuanProject.ysshFbfxService;

  /**
   * 分部分项数据匹配
   * @return
   */
  async fbfxDataPiPeiColl(args) {
    let newFbfxList = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxDataMatching(args);
    return ResponseData.success(newFbfxList);
  }


  /**
   * 分部分项数据对比  查询对比数据
   * @return
   */
  async fbfxDataDuiBiColl(args) {
    //第二个 参数 1这个值表示从前端传来查询的接口
    let fbfxListComparison = await this.service.shenHeYuSuanProject.ysshFbfxService.fbfxListComparison(args);
    return ResponseData.success(fbfxListComparison);
  }


  /**
   * 单价构成比对
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async feeBuildComparison(args){

    let feeBuildComparison = await this.service.shenHeYuSuanProject.ysshFbfxService.feeBuildComparison(args);

    return ResponseData.success(feeBuildComparison);
  }






  /**
   * 明细区的人材机显示模块
   * 清单定额人材机明细比对
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async rcjComparison(args){

    let rcjComparison = await this.service.shenHeYuSuanProject.ysshFbfxService.rcjComparison(args);

    return ResponseData.success(rcjComparison);
  }


  /**
   * 送审到审定数据转换
   * @param args
   * @returns {Promise<void>}
   */
  async ssToSdDataConvert(args) {

    await this.service.shenHeYuSuanProject.ysshFbfxService.ssToSdDataConvert(args);
    return ResponseData.success(true);
  }

  /**
   * 审定到送审数据转换
   * @param args
   * @returns {Promise<void>}
   */
  async sdToSsDataConvert(args) {
    await this.service.shenHeYuSuanProject.ysshFbfxService.sdToSsDataConvert(args);
    return ResponseData.success(true);
  }

  /**
   * 对比匹配页面 分部分项数据重新挂关联关系
   *
   matchingId：送审明细数据的id
   detailId:  审定明细数据的id
   constructId: 审定项目id
   spId：  审定单项id
   unitId：审定单位id
   ssConstructId:  审定项目id
   ssSingleId：  审定单项id
   ssUnitId：审定单位id
   */
  async changeFbfxGLGuanXi (arg) {
    return await this.service.shenHeYuSuanProject.ysshFbfxService.changeFbfxGLGuanXi(arg);
  }

  /**
   * 新建清单关联
   */
  async createQdAssociation(args) {
    return await this.service.shenHeYuSuanProject.ysshQdAssociationService.createQdAssociation(args);
  }

  /**
   * 绑定清单关联关系
   */
  async bindQdAssociation(args) {
    return await this.service.shenHeYuSuanProject.ysshQdAssociationService.bindQdAssociation(args);
  }

  /**
   * 查询已有的所有清单关联关系
   */
  async queryAllQdAssociationList(args) {
    return await this.service.shenHeYuSuanProject.ysshQdAssociationService.queryAllQdAssociationList(args);
  }


  /**
   * 查询某个清单可选择的清单关联项
   */
  async queryQdAssociation(args) {
    return await this.service.shenHeYuSuanProject.ysshQdAssociationService.queryQdAssociation(args);
  }

}

ShenHeFbfxController.toString = () => '[class ShenHeFbfxController]';
module.exports = ShenHeFbfxController;
