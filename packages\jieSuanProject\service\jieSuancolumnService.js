'use strict';



const {Service} = require("../../../core");
const {PricingFileFindUtils} = require("../../../electron/utils/PricingFileFindUtils");
const {ObjectUtils} = require("../../../electron/utils/ObjectUtils");
const {ResponseData} = require("../../../electron/utils/ResponseData");
const {ObjectUtil} = require("../../../common/ObjectUtil");

/**
 * 层级查询服务
 * @class
 */
class JieSuancolumnService extends Service {

    constructor(ctx) {
        super(ctx);
    }

    /**
     * 结算动态列展示设置，如果该页面未初始化，则初始化后返回，如果已有数据，则直接返回
     */
    // async queryColumn(type, page, constructId, singleId, unitId, Inception, requsetData) {
    //     let data = {};
    //     let file = PricingFileFindUtils.getProjectObjById(constructId).path;
    //     // let file = "E:\\开发\\云算房\\新建招标文件1一般.ysf";
    //     try {
    //         // 读取column.json文件
    //         data = await ProjectFileUtils.readLocalColumnData(file);
    //     } catch (err) {
    //         if (err.name === "SyntaxError") {
    //             data[type] = {};
    //             // 初始化column.json
    //             let a = {};
    //             a[ConstantUtil.COLUMNNAME] = ObjectUtils.toJsonString(data);
    //             await ProjectFileUtils.updateProjectFile(file, a);
    //         }
    //     }
    //     // 判断是否查到数据，true为无数据
    //     let a = false;
    //     // 查到数据的话该字段为查询到数据
    //     let datumElementElement;
    //     if (Inception === "1") {
    //         a = true;
    //     } else {
    //         try {
    //             if (!ObjectUtil.isEmpty(singleId)) {
    //                 if (!ObjectUtil.isEmpty(unitId)) {
    //                     datumElementElement = data[type][singleId][unitId][page];
    //                     if (ObjectUtil.isEmpty(datumElementElement)) {
    //                         a = true;
    //                     }
    //                 } else {
    //                     datumElementElement = data[type][singleId][page];
    //                     if (ObjectUtil.isEmpty(datumElementElement)) {
    //                         a = true;
    //                     }
    //                 }
    //             } else {
    //                 datumElementElement = data[type][page];
    //                 if (ObjectUtil.isEmpty(datumElementElement)) {
    //                     a = true;
    //                 }
    //             }
    //         } catch (err) {
    //             if (err.name === "TypeError") {
    //                 a = true;
    //             }
    //         }
    //     }
    //     // 未查询到数据，直接初始化
    //     if (a) {
    //         for (let requsetDatum of requsetData) {
    //             if (ObjectUtil.isEmpty(requsetDatum.initialize)) {
    //                 return ResponseData.fail(requsetDatum.title + "处理字段初始化异常：初始化显示字段不能为空")
    //             }
    //             requsetDatum.visible = requsetDatum.initialize;
    //         }
    //         data = requsetData;
    //     } else {
    //         data = datumElementElement;
    //     }
    //
    //     return ResponseData.success(data);
    // }

    /**
     *
     * @param type          结算预算区分    该参数后续确认不需要   因为结算预算本就是不同的工程文件
     * @param page          页面标识：分部分项=fbfx  措施项目=csxm
     * @param constructId
     * @param singleId
     * @param unitId
     * @param Inception
     * @param requestData   页面展示列的全量数据
     * @returns {Promise<ResponseData>}
     */
    async queryColumn(type, page, constructId, singleId, unitId, Inception, requestData) {
        const constructObj = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(constructObj.columnView)) {
            constructObj.columnView = {};
        }
        if (!constructObj.columnView[page]) {
            // 不存在  说明没设置过  初始化一下
            for (let requestDatum of requestData) {
                if (ObjectUtil.isEmpty(requestDatum.initialize)) {
                    return ResponseData.fail(requestDatum.title + '处理字段初始化异常：初始化显示字段不能为空');
                }
                requestDatum.visible = requestDatum.initialize;
            }
            constructObj.columnView[page] = requestData;
        }
        return ResponseData.success(constructObj.columnView[page]);
    }


    async updateColumn(type, page, constructId, singleId, unitId, requestData) {
        const constructObj = PricingFileFindUtils.getProjectObjById(constructId);
        if (ObjectUtils.isEmpty(constructObj.columnView)) {
            constructObj.columnView = {};
        }
        constructObj.columnView[page] = requestData;
        return ResponseData.success(true);
    }

    /**
     * 动态列字段展示修改
     */
    // async updateColumn(type, page, constructId, singleId, unitId, requsetData) {
    //     let data = {};
    //     let file = PricingFileFindUtils.getProjectObjById(constructId).path;
    //     // let file = "E:\\开发\\云算房\\新建招标文件1一般.ysf";
    //
    //     try {
    //         // 读取column.json文件
    //         data = await ProjectFileUtils.readLocalColumnData(file);
    //     } catch (err) {
    //         if (err.name === "SyntaxError") {
    //             data[type] = {};
    //             // 初始化column.json
    //             let a = {};
    //             a[ConstantUtil.COLUMNNAME] = ObjectUtils.toJsonString(data);
    //             await ProjectFileUtils.updateProjectFile(file, a);
    //         }
    //     }
    //
    //     // 判断存储结构
    //     if (!ObjectUtil.isEmpty(singleId)) {
    //         if (!ObjectUtil.isEmpty(unitId)) {
    //             if (ObjectUtil.isEmpty(data[type][singleId])) {
    //                 data[type][singleId]={};
    //             }
    //             if (ObjectUtil.isEmpty(data[type][singleId][unitId])) {
    //                 data[type][singleId][unitId]={};
    //             }
    //             data[type][singleId][unitId][page] = requsetData;
    //         } else {
    //             if (ObjectUtil.isEmpty(data[type][singleId])) {
    //                 data[type][singleId]={};
    //             }
    //             data[type][singleId][page] = requsetData;
    //         }
    //     } else {
    //         data[type][page] = requsetData;
    //     }
    //
    //     let a = {};
    //     a[ConstantUtil.COLUMNNAME] = ObjectUtils.toJsonString(data);
    //     await ProjectFileUtils.updateProjectFile(file, a);
    //     return ResponseData.success("文件写入成功");
    // }


}

JieSuancolumnService.toString = () => '[class JieSuancolumnService]';
module.exports = JieSuancolumnService;
